import 'package:flutter/material.dart';
import '../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class ETAWidget extends StatefulWidget {
  final ETACalculation eta;

  const ETAWidget({
    super.key,
    required this.eta,
  });

  @override
  State<ETAWidget> createState() => _ETAWidgetState();
}

class _ETAWidgetState extends State<ETAWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue.withValues(alpha: 0.1),
            AppColors.secondaryOrange.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Main ETA Display
          Row(
            children: [
              // ETA Icon with pulse animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlue,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryBlue.withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.access_time,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(width: 16),
              
              // ETA Information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Estimated Arrival',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.eta.formattedETA,
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.eta.formattedRemainingDistance,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Confidence Indicator
              _buildConfidenceIndicator(),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Additional Information
          _buildAdditionalInfo(),
        ],
      ),
    );
  }

  Widget _buildConfidenceIndicator() {
    final confidence = widget.eta.confidenceLevel;
    Color color;
    IconData icon;
    
    if (confidence >= 80) {
      color = AppColors.success;
      icon = Icons.check_circle;
    } else if (confidence >= 60) {
      color = AppColors.warning;
      icon = Icons.warning;
    } else {
      color = AppColors.error;
      icon = Icons.error;
    }
    
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${confidence}%',
          style: AppTextStyles.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Traffic Impact
          if (widget.eta.trafficImpact.isNotEmpty)
            _buildTrafficInfo(),
          
          // Factors affecting ETA
          if (widget.eta.factors.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildFactorsInfo(),
          ],
          
          // Last updated
          const SizedBox(height: 8),
          _buildLastUpdated(),
        ],
      ),
    );
  }

  Widget _buildTrafficInfo() {
    final trafficImpact = widget.eta.trafficImpact;
    final delayFactor = trafficImpact['delay_factor'] as double? ?? 1.0;
    
    String trafficCondition;
    Color trafficColor;
    IconData trafficIcon;
    
    if (delayFactor < 1.1) {
      trafficCondition = 'Light Traffic';
      trafficColor = AppColors.success;
      trafficIcon = Icons.traffic;
    } else if (delayFactor < 1.3) {
      trafficCondition = 'Moderate Traffic';
      trafficColor = AppColors.warning;
      trafficIcon = Icons.traffic;
    } else {
      trafficCondition = 'Heavy Traffic';
      trafficColor = AppColors.error;
      trafficIcon = Icons.traffic;
    }
    
    return Row(
      children: [
        Icon(
          trafficIcon,
          color: trafficColor,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          trafficCondition,
          style: AppTextStyles.bodySmall.copyWith(
            color: trafficColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        if (delayFactor > 1.1)
          Text(
            '+${((delayFactor - 1) * 100).toStringAsFixed(0)}% delay',
            style: AppTextStyles.bodySmall.copyWith(
              color: trafficColor,
            ),
          ),
      ],
    );
  }

  Widget _buildFactorsInfo() {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: widget.eta.factors.map((factor) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.info.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            _formatFactor(factor),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.info,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLastUpdated() {
    final timeDiff = DateTime.now().difference(widget.eta.calculatedAt);
    String timeText;
    
    if (timeDiff.inSeconds < 60) {
      timeText = 'Just now';
    } else if (timeDiff.inMinutes < 60) {
      timeText = '${timeDiff.inMinutes}m ago';
    } else {
      timeText = '${timeDiff.inHours}h ago';
    }
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.update,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          'Updated $timeText',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  String _formatFactor(String factor) {
    switch (factor.toLowerCase()) {
      case 'traffic':
        return 'Traffic';
      case 'gps_accuracy':
        return 'GPS';
      case 'route_optimization':
        return 'Route';
      case 'weather':
        return 'Weather';
      case 'fallback_calculation':
        return 'Estimated';
      default:
        return factor.replaceAll('_', ' ').toUpperCase();
    }
  }
}
