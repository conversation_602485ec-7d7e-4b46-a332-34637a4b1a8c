// Wallet System JavaScript

class ProjekWallet {
  constructor() {
    this.balances = {
      pc: 1250,        // ProjekCoin balance
      inr: 850,        // INR balance
      gaming: 450,     // Gaming balance in PC
      staking: 2100    // Staking balance in PC
    };
    
    this.exchangeRate = 1; // 1 INR = 1 PC
    this.stakingPlans = {
      bronze: { apy: 8, minAmount: 100, lockPeriod: 30 },
      silver: { apy: 12, minAmount: 500, lockPeriod: 90 },
      gold: { apy: 18, minAmount: 2000, lockPeriod: 180 }
    };
    
    this.transactions = [];
    this.balanceVisibility = {
      pc: true,
      inr: true,
      gaming: true,
      staking: true
    };
    
    this.initializeWallet();
  }

  initializeWallet() {
    this.updateAllBalances();
    this.loadTransactionHistory();
    this.setupEventListeners();
  }

  updateAllBalances() {
    // Update ProjekCoin balance
    const pcElement = document.getElementById('pcBalance');
    if (pcElement) {
      pcElement.textContent = this.balanceVisibility.pc ? 
        `${this.balances.pc.toLocaleString()} PC` : '••••••';
    }

    // Update INR balance
    const inrElement = document.getElementById('inrBalance');
    if (inrElement) {
      inrElement.textContent = this.balanceVisibility.inr ? 
        `₹${this.balances.inr.toLocaleString()}` : '••••••';
    }

    // Update Gaming balance
    const gamingElement = document.getElementById('gamingBalance');
    if (gamingElement) {
      gamingElement.textContent = this.balanceVisibility.gaming ? 
        `${this.balances.gaming.toLocaleString()} PC` : '••••••';
    }

    // Update Staking balance
    const stakingElement = document.getElementById('stakingBalance');
    if (stakingElement) {
      stakingElement.textContent = this.balanceVisibility.staking ? 
        `${this.balances.staking.toLocaleString()} PC` : '••••••';
    }
  }

  toggleBalance(type) {
    this.balanceVisibility[type] = !this.balanceVisibility[type];
    
    const visibilityIcon = document.getElementById(`${type}Visibility`);
    if (visibilityIcon) {
      visibilityIcon.textContent = this.balanceVisibility[type] ? 'visibility' : 'visibility_off';
    }
    
    this.updateAllBalances();
  }

  openDepositModal() {
    const modal = this.createModal('Deposit INR', `
      <div class="modal-content">
        <h3>💰 Deposit Money to Wallet</h3>
        <p>Add money to your INR balance and convert to ProjekCoin</p>
        
        <div class="input-group">
          <label>Amount (INR)</label>
          <input type="number" id="depositAmount" placeholder="Enter amount" min="1" max="50000">
          <small>Minimum: ₹1 | Maximum: ₹50,000</small>
        </div>
        
        <div class="payment-methods">
          <h4>Select Payment Method</h4>
          <div class="payment-options">
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="upi" checked>
              <span>📱 UPI</span>
            </label>
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="card">
              <span>💳 Card</span>
            </label>
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="netbanking">
              <span>🏦 Net Banking</span>
            </label>
          </div>
        </div>
        
        <div class="modal-actions">
          <button class="btn secondary" onclick="closeModal()">Cancel</button>
          <button class="btn primary" onclick="processDeposit()">Deposit Now</button>
        </div>
      </div>
    `);
  }

  processDeposit() {
    const amount = parseInt(document.getElementById('depositAmount').value);
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
    
    if (!amount || amount < 1) {
      this.showMessage('Please enter a valid amount', 'error');
      return;
    }
    
    if (amount > 50000) {
      this.showMessage('Maximum deposit limit is ₹50,000', 'error');
      return;
    }
    
    // Simulate payment processing
    this.showMessage('Processing payment...', 'info');
    
    setTimeout(() => {
      this.balances.inr += amount;
      this.addTransaction('deposit', `INR Deposit via ${paymentMethod.toUpperCase()}`, amount, 'inr');
      this.updateAllBalances();
      this.showMessage(`₹${amount} deposited successfully!`, 'success');
      this.closeModal();
    }, 2000);
  }

  convertToProjekCoin() {
    if (this.balances.inr <= 0) {
      this.showMessage('No INR balance to convert!', 'error');
      return;
    }
    
    const modal = this.createModal('Convert to ProjekCoin', `
      <div class="modal-content">
        <h3>💱 Convert INR to ProjekCoin</h3>
        <p>Exchange Rate: 1 INR = 1 PC</p>
        
        <div class="conversion-display">
          <div class="balance-info">
            <span>Available INR: ₹${this.balances.inr.toLocaleString()}</span>
          </div>
        </div>
        
        <div class="input-group">
          <label>Amount to Convert (INR)</label>
          <input type="number" id="convertAmount" placeholder="Enter amount" min="1" max="${this.balances.inr}">
          <small>You will receive: <span id="pcReceive">0</span> PC</small>
        </div>
        
        <div class="modal-actions">
          <button class="btn secondary" onclick="closeModal()">Cancel</button>
          <button class="btn primary" onclick="processConversion()">Convert Now</button>
        </div>
      </div>
    `);
    
    // Add real-time conversion calculation
    document.getElementById('convertAmount').addEventListener('input', (e) => {
      const amount = parseInt(e.target.value) || 0;
      document.getElementById('pcReceive').textContent = amount;
    });
  }

  processConversion() {
    const amount = parseInt(document.getElementById('convertAmount').value);
    
    if (!amount || amount < 1) {
      this.showMessage('Please enter a valid amount', 'error');
      return;
    }
    
    if (amount > this.balances.inr) {
      this.showMessage('Insufficient INR balance!', 'error');
      return;
    }
    
    // Process conversion
    this.balances.inr -= amount;
    this.balances.pc += amount;
    
    this.addTransaction('conversion', 'INR to ProjekCoin Conversion', amount, 'pc', true);
    this.updateAllBalances();
    this.showMessage(`Converted ₹${amount} to ${amount} PC successfully!`, 'success');
    this.closeModal();
  }

  openWithdrawModal() {
    if (this.balances.pc <= 0) {
      this.showMessage('No ProjekCoin balance to withdraw!', 'error');
      return;
    }
    
    const modal = this.createModal('Withdraw to Bank', `
      <div class="modal-content">
        <h3>🏦 Withdraw ProjekCoin to Bank</h3>
        <p>Convert PC to INR and withdraw directly to your bank account</p>
        
        <div class="balance-info">
          <span>Available PC: ${this.balances.pc.toLocaleString()} PC (≈ ₹${this.balances.pc.toLocaleString()})</span>
        </div>
        
        <div class="input-group">
          <label>Amount to Withdraw (PC)</label>
          <input type="number" id="withdrawAmount" placeholder="Enter PC amount" min="1" max="${this.balances.pc}">
          <small>You will receive: ₹<span id="inrReceive">0</span> in your bank</small>
        </div>
        
        <div class="bank-details">
          <h4>Bank Account Details</h4>
          <div class="input-group">
            <label>Account Number</label>
            <input type="text" id="accountNumber" placeholder="Enter account number">
          </div>
          <div class="input-group">
            <label>IFSC Code</label>
            <input type="text" id="ifscCode" placeholder="Enter IFSC code">
          </div>
        </div>
        
        <div class="modal-actions">
          <button class="btn secondary" onclick="closeModal()">Cancel</button>
          <button class="btn primary" onclick="processWithdrawal()">Withdraw Now</button>
        </div>
      </div>
    `);
    
    // Add real-time withdrawal calculation
    document.getElementById('withdrawAmount').addEventListener('input', (e) => {
      const amount = parseInt(e.target.value) || 0;
      document.getElementById('inrReceive').textContent = amount;
    });
  }

  processWithdrawal() {
    const amount = parseInt(document.getElementById('withdrawAmount').value);
    const accountNumber = document.getElementById('accountNumber').value;
    const ifscCode = document.getElementById('ifscCode').value;
    
    if (!amount || amount < 1) {
      this.showMessage('Please enter a valid amount', 'error');
      return;
    }
    
    if (amount > this.balances.pc) {
      this.showMessage('Insufficient ProjekCoin balance!', 'error');
      return;
    }
    
    if (!accountNumber || !ifscCode) {
      this.showMessage('Please enter bank account details', 'error');
      return;
    }
    
    // Process withdrawal
    this.showMessage('Processing withdrawal...', 'info');
    
    setTimeout(() => {
      this.balances.pc -= amount;
      this.addTransaction('withdraw', `Bank Withdrawal to ****${accountNumber.slice(-4)}`, amount, 'inr');
      this.updateAllBalances();
      this.showMessage(`₹${amount} withdrawal initiated! Funds will be credited within 24 hours.`, 'success');
      this.closeModal();
    }, 2000);
  }

  openStakeModal(plan = 'silver') {
    const planDetails = this.stakingPlans[plan];
    
    const modal = this.createModal('Stake & Earn', `
      <div class="modal-content">
        <h3>📈 Stake ProjekCoin - ${plan.charAt(0).toUpperCase() + plan.slice(1)} Plan</h3>
        <div class="plan-summary">
          <p><strong>APY:</strong> ${planDetails.apy}%</p>
          <p><strong>Minimum:</strong> ${planDetails.minAmount} PC</p>
          <p><strong>Lock Period:</strong> ${planDetails.lockPeriod} days</p>
        </div>
        
        <div class="balance-info">
          <span>Available PC: ${this.balances.pc.toLocaleString()} PC</span>
        </div>
        
        <div class="input-group">
          <label>Amount to Stake (PC)</label>
          <input type="number" id="stakeAmount" placeholder="Enter PC amount" min="${planDetails.minAmount}" max="${this.balances.pc}">
          <small>Daily Reward: <span id="dailyReward">0</span> PC</small>
        </div>
        
        <div class="modal-actions">
          <button class="btn secondary" onclick="closeModal()">Cancel</button>
          <button class="btn primary" onclick="processStaking('${plan}')">Stake Now</button>
        </div>
      </div>
    `);
    
    // Add real-time reward calculation
    document.getElementById('stakeAmount').addEventListener('input', (e) => {
      const amount = parseInt(e.target.value) || 0;
      const dailyReward = (amount * planDetails.apy / 100 / 365).toFixed(2);
      document.getElementById('dailyReward').textContent = dailyReward;
    });
  }

  processStaking(plan) {
    const amount = parseInt(document.getElementById('stakeAmount').value);
    const planDetails = this.stakingPlans[plan];
    
    if (!amount || amount < planDetails.minAmount) {
      this.showMessage(`Minimum staking amount is ${planDetails.minAmount} PC`, 'error');
      return;
    }
    
    if (amount > this.balances.pc) {
      this.showMessage('Insufficient ProjekCoin balance!', 'error');
      return;
    }
    
    // Process staking
    this.balances.pc -= amount;
    this.balances.staking += amount;
    
    this.addTransaction('staking', `Staked in ${plan.charAt(0).toUpperCase() + plan.slice(1)} Plan`, amount, 'pc');
    this.updateAllBalances();
    this.showMessage(`${amount} PC staked successfully! Start earning ${planDetails.apy}% APY.`, 'success');
    this.closeModal();
  }

  transferToGaming() {
    if (this.balances.pc <= 0) {
      this.showMessage('No ProjekCoin balance to transfer!', 'error');
      return;
    }
    
    const amount = prompt(`Enter PC amount to transfer to gaming (Available: ${this.balances.pc} PC):`);
    if (amount && !isNaN(amount) && amount > 0 && amount <= this.balances.pc) {
      this.balances.pc -= parseInt(amount);
      this.balances.gaming += parseInt(amount);
      
      this.addTransaction('transfer', 'Transfer to Gaming Balance', parseInt(amount), 'pc');
      this.updateAllBalances();
      this.showMessage(`${amount} PC transferred to gaming balance!`, 'success');
    }
  }

  // Gaming integration methods
  processGameWin(amount) {
    this.balances.gaming += amount;
    this.addTransaction('gaming', 'Gaming Win', amount, 'pc', true);
    this.updateAllBalances();
    this.showMessage(`🎉 You won ${amount} PC!`, 'success');
  }

  processGameLoss(amount) {
    if (this.balances.gaming >= amount) {
      this.balances.gaming -= amount;
      this.addTransaction('gaming', 'Gaming Entry Fee', amount, 'pc', false);
      this.updateAllBalances();
      return true;
    }
    return false;
  }

  addTransaction(type, description, amount, currency, isWin = null) {
    const transaction = {
      id: Date.now(),
      type: type,
      description: description,
      amount: amount,
      currency: currency,
      isWin: isWin,
      timestamp: new Date(),
      status: 'completed'
    };
    
    this.transactions.unshift(transaction);
    this.loadTransactionHistory();
  }

  loadTransactionHistory() {
    // This would typically load from a database
    // For now, we'll use the existing HTML transactions
  }

  filterTransactions(filter) {
    // Update filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Filter logic would go here
    this.showMessage(`Showing ${filter} transactions`, 'info');
  }

  createModal(title, content) {
    const modalContainer = document.getElementById('modalContainer');
    modalContainer.innerHTML = `
      <div class="modal-overlay" onclick="closeModal()">
        <div class="modal" onclick="event.stopPropagation()">
          <div class="modal-header">
            <h2>${title}</h2>
            <button class="close-btn" onclick="closeModal()">
              <span class="material-icons">close</span>
            </button>
          </div>
          ${content}
        </div>
      </div>
    `;
    
    // Add modal styles
    if (!document.getElementById('modalStyles')) {
      const modalStyles = document.createElement('style');
      modalStyles.id = 'modalStyles';
      modalStyles.textContent = `
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        }
        .modal {
          background: white;
          border-radius: 20px;
          max-width: 500px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
        }
        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 2rem 2rem 1rem;
          border-bottom: 1px solid #f0f0f0;
        }
        .modal-content {
          padding: 2rem;
        }
        .input-group {
          margin-bottom: 1.5rem;
        }
        .input-group label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
        }
        .input-group input {
          width: 100%;
          padding: 1rem;
          border: 2px solid #f0f0f0;
          border-radius: 10px;
          font-size: 1rem;
        }
        .input-group small {
          color: #666;
          font-size: 0.9rem;
        }
        .modal-actions {
          display: flex;
          gap: 1rem;
          margin-top: 2rem;
        }
        .btn {
          flex: 1;
          padding: 1rem;
          border: none;
          border-radius: 10px;
          font-weight: 600;
          cursor: pointer;
        }
        .btn.primary {
          background: linear-gradient(135deg, #ff9933, #138808);
          color: white;
        }
        .btn.secondary {
          background: #f0f0f0;
          color: #333;
        }
        .payment-options {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 1rem;
          margin-top: 1rem;
        }
        .payment-option {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          border: 2px solid #f0f0f0;
          border-radius: 10px;
          cursor: pointer;
        }
        .payment-option:has(input:checked) {
          border-color: #ff9933;
          background: rgba(255, 153, 51, 0.1);
        }
        .balance-info {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 10px;
          margin-bottom: 1.5rem;
          text-align: center;
          font-weight: 600;
        }
        .plan-summary {
          background: #f8f9fa;
          padding: 1.5rem;
          border-radius: 10px;
          margin-bottom: 1.5rem;
        }
        .plan-summary p {
          margin-bottom: 0.5rem;
        }
      `;
      document.head.appendChild(modalStyles);
    }
  }

  closeModal() {
    const modalContainer = document.getElementById('modalContainer');
    modalContainer.innerHTML = '';
  }

  showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 1rem 2rem;
      border-radius: 10px;
      color: white;
      font-weight: 600;
      z-index: 10001;
      animation: slideIn 0.3s ease-out;
    `;
    
    const colors = {
      success: '#28a745',
      error: '#dc3545',
      warning: '#ffc107',
      info: '#17a2b8'
    };
    messageDiv.style.background = colors[type] || colors.info;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
      messageDiv.style.animation = 'slideOut 0.3s ease-out forwards';
      setTimeout(() => messageDiv.remove(), 300);
    }, 3000);
  }

  setupEventListeners() {
    // Add any additional event listeners here
  }
}

// Global functions for HTML onclick events
function toggleBalance(type) {
  window.wallet.toggleBalance(type);
}

function openDepositModal() {
  window.wallet.openDepositModal();
}

function openWithdrawModal() {
  window.wallet.openWithdrawModal();
}

function openStakeModal(plan) {
  window.wallet.openStakeModal(plan);
}

function convertToProjekCoin() {
  window.wallet.convertToProjekCoin();
}

function transferToGaming() {
  window.wallet.transferToGaming();
}

function processDeposit() {
  window.wallet.processDeposit();
}

function processConversion() {
  window.wallet.processConversion();
}

function processWithdrawal() {
  window.wallet.processWithdrawal();
}

function processStaking(plan) {
  window.wallet.processStaking(plan);
}

function closeModal() {
  window.wallet.closeModal();
}

function filterTransactions(filter) {
  window.wallet.filterTransactions(filter);
}

function addMoney() {
  window.wallet.openDepositModal();
}

function sendMoney() {
  window.wallet.showMessage('Send money feature coming soon!', 'info');
}

function openUnstakeModal() {
  window.wallet.showMessage('Unstaking feature coming soon!', 'info');
}

// Initialize wallet when page loads
document.addEventListener('DOMContentLoaded', () => {
  window.wallet = new ProjekWallet();
});
