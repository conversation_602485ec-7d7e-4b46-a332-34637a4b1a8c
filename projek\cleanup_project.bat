@echo off
echo ========================================
echo Project Cleanup Script
echo ========================================
echo.

echo Creating documentation folder...
if not exist "docs" mkdir docs

echo Moving documentation files to docs folder...
move "APK_BUILD_FIX_SUMMARY.md" "docs\" 2>nul
move "BUILD_SYSTEM_README.md" "docs\" 2>nul
move "CUSTOMER_SUPPORT_CHAT_IMPLEMENTATION.md" "docs\" 2>nul
move "DEVELOPMENT_SETUP.md" "docs\" 2>nul
move "ECOMMERCE_IMPLEMENTATION_SUMMARY.md" "docs\" 2>nul
move "FINAL_CLEANUP_SUMMARY.md" "docs\" 2>nul
move "FINAL_FIXES_SUMMARY.md" "docs\" 2>nul
move "FIREBASE_CHAT_IMPLEMENTATION_SUMMARY.md" "docs\" 2>nul
move "FIREBASE_CHAT_SETUP.md" "docs\" 2>nul
move "FIXES_VERIFICATION_REPORT.md" "docs\" 2>nul
move "GOOGLE_CLIENT_ID_CONFIGURATION.md" "docs\" 2>nul
move "GOOGLE_SIGNIN_FIX.md" "docs\" 2>nul
move "HELP_CENTER_DOCUMENTATION.md" "docs\" 2>nul
move "HOME_PAGE_REDESIGN_SUMMARY.md" "docs\" 2>nul
move "SUPPORT_CHAT_SETUP_GUIDE.md" "docs\" 2>nul
move "TEST_SETUP_GUIDE.md" "docs\" 2>nul
move "UNUSED_FIELDS_FIX.md" "docs\" 2>nul
move "UNUSED_IMPORT_FIX.md" "docs\" 2>nul
move "online_signing_guide.md" "docs\" 2>nul
move "test_categories.md" "docs\" 2>nul
move "test_overflow_fix.md" "docs\" 2>nul
move "vivo_v23_installation_guide.md" "docs\" 2>nul

echo Creating scripts folder...
if not exist "scripts" mkdir scripts

echo Moving build scripts to scripts folder...
move "build_dev_apk.bat" "scripts\" 2>nul
move "build_minimal_debug.bat" "scripts\" 2>nul
move "create_keystore.bat" "scripts\" 2>nul
move "quick_setup.bat" "scripts\" 2>nul
move "run_app.bat" "scripts\" 2>nul
move "setup_firebase_auto.bat" "scripts\" 2>nul
move "sign_apk.bat" "scripts\" 2>nul
move "test_all_fixes.bat" "scripts\" 2>nul
move "test_build.bat" "scripts\" 2>nul

echo Creating test files folder...
if not exist "test_files" mkdir test_files

echo Moving test files...
move "test_firebase_chat.dart" "test_files\" 2>nul
move "test_support_chat.dart" "test_files\" 2>nul

echo Cleaning up Firebase config files...
if exist "firebase_auto.json" del "firebase_auto.json"
if exist "firebase_config_auto.json" del "firebase_config_auto.json"

echo.
echo ========================================
echo Cleanup Complete!
echo ========================================
echo.
echo Project structure is now organized:
echo - docs/           - All documentation files
echo - scripts/        - Build and setup scripts  
echo - test_files/     - Test files
echo - lib/            - Main source code
echo - android/        - Android configuration
echo - assets/         - App assets
echo.
echo Main files in root:
echo - README.md
echo - AUTHENTICATION_FIX_GUIDE.md (latest guide)
echo - fix_google_signin.bat (SHA-1 fix script)
echo - pubspec.yaml
echo - analysis_options.yaml
echo.
pause
