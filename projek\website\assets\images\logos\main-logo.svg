<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 80" width="300" height="80">
  <!-- Background -->
  <rect width="300" height="80" fill="transparent"/>
  
  <!-- Logo Icon -->
  <g transform="translate(10, 10)">
    <!-- Icon Background -->
    <circle cx="30" cy="30" r="28" fill="url(#logoGradient)" stroke="#138808" stroke-width="2"/>
    
    <!-- Super Vision Icon -->
    <g transform="translate(10, 10)">
      <!-- Eye Symbol -->
      <ellipse cx="20" cy="20" rx="15" ry="10" fill="#ffffff" opacity="0.9"/>
      <circle cx="20" cy="20" r="8" fill="#146eb4"/>
      <circle cx="20" cy="20" r="4" fill="#ffffff"/>
      
      <!-- Vision Rays -->
      <g stroke="#ff9933" stroke-width="2" opacity="0.7">
        <line x1="5" y1="15" x2="8" y2="18"/>
        <line x1="5" y1="25" x2="8" y2="22"/>
        <line x1="35" y1="15" x2="32" y2="18"/>
        <line x1="35" y1="25" x2="32" y2="22"/>
        <line x1="20" y1="5" x2="20" y2="8"/>
        <line x1="20" y1="35" x2="20" y2="32"/>
      </g>
    </g>
  </g>
  
  <!-- Brand Text -->
  <g transform="translate(80, 25)">
    <!-- Super Vision Text -->
    <text x="0" y="20" font-family="Poppins, sans-serif" font-size="24" font-weight="700" fill="#2c2c54">
      Super Vision
    </text>
    
    <!-- Tagline -->
    <text x="0" y="40" font-family="Poppins, sans-serif" font-size="12" font-weight="400" fill="#138808">
      Connecting India
    </text>
  </g>
  
  <!-- Indian Flag Colors Accent -->
  <g transform="translate(270, 20)">
    <rect x="0" y="0" width="4" height="12" fill="#ff9933"/>
    <rect x="0" y="12" width="4" height="12" fill="#ffffff" stroke="#ddd" stroke-width="0.5"/>
    <rect x="0" y="24" width="4" height="12" fill="#138808"/>
    <circle cx="2" cy="18" r="3" fill="#000080" opacity="0.8"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fff3e0;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
