# 🔥 Projek Super App - Firestore Collections Documentation

## 📋 **Project Overview**
- **Project Name**: Projek Super App (3-in-1 Platform)
- **Firebase Projects**: 
  - User App: `projek-user`
  - Rider App: `projek-rider-575d2`
  - Seller App: `projek-seller`
- **Database Type**: Cloud Firestore
- **Generated**: December 2024

---

## 🏗️ **Database Architecture Overview**

### **Multi-App Structure**
The Projek Super App uses a distributed Firestore architecture across three Firebase projects:

1. **User App Database** - Customer-facing features
2. **Rider App Database** - Delivery partner features  
3. **Seller App Database** - Merchant/vendor features

### **Cross-App Data Synchronization**
- **Shared Collections**: Orders, Tracking, Payments
- **App-Specific Collections**: User profiles, Rider profiles, Seller profiles
- **Real-time Sync**: Using Firebase Functions and Firestore triggers

---

## 👤 **USER APP COLLECTIONS** (`projek-user`)

### **1. users**
```json
{
  "userId": "string (document ID)",
  "email": "string",
  "phoneNumber": "string",
  "displayName": "string",
  "profileImageUrl": "string",
  "dateOfBirth": "timestamp",
  "gender": "string",
  "bio": "string",
  "addresses": [
    {
      "id": "string",
      "type": "home|work|other",
      "street": "string",
      "city": "string",
      "state": "string",
      "pincode": "string",
      "landmark": "string",
      "isDefault": "boolean",
      "coordinates": {
        "latitude": "number",
        "longitude": "number"
      }
    }
  ],
  "preferences": {
    "language": "string",
    "currency": "string",
    "notifications": {
      "orders": "boolean",
      "promotions": "boolean",
      "chat": "boolean"
    }
  },
  "loyaltyTier": "bronze|silver|gold|platinum|diamond",
  "totalOrders": "number",
  "totalSpent": "number",
  "referralCode": "string",
  "referredBy": "string",
  "isActive": "boolean",
  "isVerified": "boolean",
  "kycStatus": "pending|verified|rejected",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **2. wallets**
```json
{
  "userId": "string (document ID)",
  "projekCoinBalance": "number",
  "inrBalance": "number",
  "rewardsBalance": "number",
  "cashbackBalance": "number",
  "isActive": "boolean",
  "isVerified": "boolean",
  "limits": {
    "dailySpend": "number",
    "monthlySpend": "number",
    "maxBalance": "number"
  },
  "settings": {
    "autoRecharge": "boolean",
    "rechargeThreshold": "number",
    "rechargeAmount": "number"
  },
  "lastUpdated": "timestamp"
}
```

### **3. transactions**
```json
{
  "transactionId": "string (document ID)",
  "userId": "string",
  "type": "topup|payment|transfer|reward|cashback|refund",
  "walletType": "projekcoin|inr|rewards|cashback",
  "amount": "number",
  "balanceBefore": "number",
  "balanceAfter": "number",
  "description": "string",
  "orderId": "string",
  "recipientId": "string",
  "recipientName": "string",
  "status": "pending|completed|failed|cancelled",
  "paymentMethod": {
    "id": "string",
    "type": "card|upi|wallet|netbanking|cod",
    "name": "string"
  },
  "metadata": "object",
  "timestamp": "timestamp"
}
```

### **4. orders**
```json
{
  "orderId": "string (document ID)",
  "userId": "string",
  "sellerId": "string",
  "riderId": "string",
  "type": "marketplace|food|grocery|service",
  "status": "pending|confirmed|preparing|ready|picked|delivered|cancelled",
  "items": [
    {
      "productId": "string",
      "name": "string",
      "price": "number",
      "quantity": "number",
      "variant": "object"
    }
  ],
  "pricing": {
    "subtotal": "number",
    "deliveryFee": "number",
    "taxes": "number",
    "discount": "number",
    "total": "number"
  },
  "addresses": {
    "pickup": "object",
    "delivery": "object"
  },
  "timeline": [
    {
      "status": "string",
      "timestamp": "timestamp",
      "note": "string"
    }
  ],
  "estimatedDelivery": "timestamp",
  "actualDelivery": "timestamp",
  "paymentStatus": "pending|paid|refunded",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **5. chats**
```json
{
  "chatId": "string (document ID)",
  "participants": ["userId1", "userId2"],
  "chatType": "user_support|user_seller|user_rider|group",
  "orderId": "string",
  "productId": "string",
  "lastMessage": {
    "text": "string",
    "senderId": "string",
    "timestamp": "timestamp",
    "type": "text|image|file|location"
  },
  "unreadCounts": {
    "userId1": "number",
    "userId2": "number"
  },
  "isActive": "boolean",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **6. messages**
```json
{
  "messageId": "string (document ID)",
  "chatId": "string",
  "senderId": "string",
  "senderName": "string",
  "text": "string",
  "type": "text|image|file|location|system",
  "attachments": [
    {
      "type": "image|pdf|document",
      "url": "string",
      "name": "string",
      "size": "number"
    }
  ],
  "location": {
    "latitude": "number",
    "longitude": "number",
    "address": "string"
  },
  "isEdited": "boolean",
  "isDeleted": "boolean",
  "readBy": ["userId1", "userId2"],
  "timestamp": "timestamp"
}
```

### **7. gamification**
```json
{
  "userId": "string (document ID)",
  "level": "number",
  "xp": "number",
  "projekCoinsEarned": "number",
  "achievements": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "icon": "string",
      "unlockedAt": "timestamp",
      "isClaimed": "boolean"
    }
  ],
  "dailyCheckin": {
    "streak": "number",
    "lastCheckin": "timestamp",
    "totalCheckins": "number"
  },
  "spinStats": {
    "totalSpins": "number",
    "lastSpin": "timestamp",
    "dailySpinsUsed": "number",
    "totalWinnings": "number"
  },
  "referrals": {
    "totalReferred": "number",
    "successfulReferrals": "number",
    "bonusEarned": "number"
  },
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **8. reviews**
```json
{
  "reviewId": "string (document ID)",
  "userId": "string",
  "orderId": "string",
  "productId": "string",
  "sellerId": "string",
  "riderId": "string",
  "type": "product|seller|rider|service",
  "rating": "number (1-5)",
  "title": "string",
  "comment": "string",
  "images": ["string"],
  "isVerified": "boolean",
  "helpfulVotes": "number",
  "reportCount": "number",
  "response": {
    "text": "string",
    "responderId": "string",
    "timestamp": "timestamp"
  },
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **9. notifications**
```json
{
  "notificationId": "string (document ID)",
  "userId": "string",
  "title": "string",
  "body": "string",
  "type": "order|payment|promotion|system|chat",
  "data": {
    "orderId": "string",
    "chatId": "string",
    "deepLink": "string"
  },
  "isRead": "boolean",
  "priority": "low|normal|high",
  "scheduledFor": "timestamp",
  "sentAt": "timestamp",
  "createdAt": "timestamp"
}
```

### **10. support_tickets**
```json
{
  "ticketId": "string (document ID)",
  "userId": "string",
  "subject": "string",
  "description": "string",
  "category": "order|payment|technical|account|other",
  "priority": "low|medium|high|urgent",
  "status": "open|in_progress|resolved|closed",
  "assignedTo": "string",
  "attachments": ["string"],
  "messages": [
    {
      "senderId": "string",
      "senderType": "user|support",
      "message": "string",
      "timestamp": "timestamp"
    }
  ],
  "resolution": "string",
  "satisfactionRating": "number",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "resolvedAt": "timestamp"
}
```

---

## 🚗 **RIDER APP COLLECTIONS** (`projek-rider-575d2`)

### **1. riders**
```json
{
  "riderId": "string (document ID)",
  "email": "string",
  "phoneNumber": "string",
  "displayName": "string",
  "profileImageUrl": "string",
  "dateOfBirth": "timestamp",
  "gender": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "pincode": "string"
  },
  "documents": {
    "aadhar": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    },
    "pan": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    },
    "drivingLicense": {
      "number": "string",
      "imageUrl": "string",
      "expiryDate": "timestamp",
      "verified": "boolean"
    },
    "vehicleRC": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    }
  },
  "vehicle": {
    "type": "bike|scooter|car|bicycle",
    "brand": "string",
    "model": "string",
    "registrationNumber": "string",
    "color": "string",
    "fuelType": "petrol|diesel|electric|cng"
  },
  "bankDetails": {
    "accountNumber": "string",
    "ifscCode": "string",
    "accountHolderName": "string",
    "bankName": "string",
    "verified": "boolean"
  },
  "status": "pending|approved|suspended|blocked",
  "isOnline": "boolean",
  "isAvailable": "boolean",
  "currentLocation": {
    "latitude": "number",
    "longitude": "number",
    "address": "string",
    "lastUpdated": "timestamp"
  },
  "serviceAreas": ["string"],
  "rating": "number",
  "totalRatings": "number",
  "totalDeliveries": "number",
  "totalEarnings": "number",
  "joiningDate": "timestamp",
  "lastActiveAt": "timestamp",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **2. rider_orders**
```json
{
  "orderId": "string (document ID)",
  "riderId": "string",
  "userId": "string",
  "sellerId": "string",
  "status": "assigned|accepted|picked|in_transit|delivered|cancelled",
  "orderType": "marketplace|food|grocery|service",
  "pickupLocation": {
    "latitude": "number",
    "longitude": "number",
    "address": "string",
    "contactName": "string",
    "contactPhone": "string"
  },
  "deliveryLocation": {
    "latitude": "number",
    "longitude": "number",
    "address": "string",
    "contactName": "string",
    "contactPhone": "string"
  },
  "items": [
    {
      "name": "string",
      "quantity": "number",
      "specialInstructions": "string"
    }
  ],
  "pricing": {
    "deliveryFee": "number",
    "tip": "number",
    "bonus": "number",
    "total": "number"
  },
  "timeline": [
    {
      "status": "string",
      "timestamp": "timestamp",
      "location": {
        "latitude": "number",
        "longitude": "number"
      }
    }
  ],
  "estimatedPickupTime": "timestamp",
  "actualPickupTime": "timestamp",
  "estimatedDeliveryTime": "timestamp",
  "actualDeliveryTime": "timestamp",
  "distance": "number",
  "duration": "number",
  "otp": "string",
  "specialInstructions": "string",
  "paymentMethod": "cash|online",
  "cashCollected": "number",
  "assignedAt": "timestamp",
  "acceptedAt": "timestamp",
  "completedAt": "timestamp",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **3. rider_earnings**
```json
{
  "earningId": "string (document ID)",
  "riderId": "string",
  "orderId": "string",
  "type": "delivery|tip|bonus|incentive|penalty",
  "amount": "number",
  "description": "string",
  "date": "timestamp",
  "weekNumber": "number",
  "monthNumber": "number",
  "year": "number",
  "isPaid": "boolean",
  "payoutId": "string",
  "createdAt": "timestamp"
}
```

### **4. rider_tracking**
```json
{
  "trackingId": "string (document ID)",
  "riderId": "string",
  "orderId": "string",
  "currentLocation": {
    "latitude": "number",
    "longitude": "number",
    "accuracy": "number",
    "speed": "number",
    "heading": "number"
  },
  "route": [
    {
      "latitude": "number",
      "longitude": "number",
      "timestamp": "timestamp"
    }
  ],
  "eta": {
    "pickup": "timestamp",
    "delivery": "timestamp",
    "confidence": "number"
  },
  "status": "en_route_pickup|at_pickup|en_route_delivery|delivered",
  "lastUpdated": "timestamp",
  "createdAt": "timestamp"
}
```

### **5. rider_payouts**
```json
{
  "payoutId": "string (document ID)",
  "riderId": "string",
  "period": {
    "startDate": "timestamp",
    "endDate": "timestamp",
    "type": "weekly|monthly"
  },
  "earnings": {
    "deliveryFees": "number",
    "tips": "number",
    "bonuses": "number",
    "incentives": "number",
    "penalties": "number",
    "total": "number"
  },
  "deductions": {
    "tax": "number",
    "insurance": "number",
    "other": "number",
    "total": "number"
  },
  "netAmount": "number",
  "status": "pending|processing|paid|failed",
  "paymentMethod": "bank_transfer|upi|wallet",
  "transactionId": "string",
  "processedAt": "timestamp",
  "createdAt": "timestamp"
}
```

---

## 🏪 **SELLER APP COLLECTIONS** (`projek-seller`)

### **1. sellers**
```json
{
  "sellerId": "string (document ID)",
  "email": "string",
  "phoneNumber": "string",
  "businessName": "string",
  "displayName": "string",
  "profileImageUrl": "string",
  "businessType": "restaurant|grocery|electronics|clothing|services|pharmacy",
  "category": "string",
  "subcategory": "string",
  "description": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "pincode": "string",
    "landmark": "string",
    "coordinates": {
      "latitude": "number",
      "longitude": "number"
    }
  },
  "businessHours": {
    "monday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "tuesday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "wednesday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "thursday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "friday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "saturday": {"open": "string", "close": "string", "isOpen": "boolean"},
    "sunday": {"open": "string", "close": "string", "isOpen": "boolean"}
  },
  "documents": {
    "gst": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    },
    "pan": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    },
    "businessLicense": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    },
    "fssai": {
      "number": "string",
      "imageUrl": "string",
      "verified": "boolean"
    }
  },
  "bankDetails": {
    "accountNumber": "string",
    "ifscCode": "string",
    "accountHolderName": "string",
    "bankName": "string",
    "verified": "boolean"
  },
  "status": "pending|approved|suspended|blocked",
  "isOnline": "boolean",
  "isAcceptingOrders": "boolean",
  "rating": "number",
  "totalRatings": "number",
  "totalOrders": "number",
  "totalRevenue": "number",
  "commission": "number",
  "deliveryRadius": "number",
  "minimumOrderValue": "number",
  "averagePreparationTime": "number",
  "tags": ["string"],
  "features": ["delivery", "pickup", "dine_in"],
  "joiningDate": "timestamp",
  "lastActiveAt": "timestamp",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **2. products**
```json
{
  "productId": "string (document ID)",
  "sellerId": "string",
  "name": "string",
  "description": "string",
  "category": "string",
  "subcategory": "string",
  "brand": "string",
  "sku": "string",
  "images": ["string"],
  "pricing": {
    "basePrice": "number",
    "salePrice": "number",
    "discount": "number",
    "discountType": "percentage|fixed",
    "tax": "number",
    "currency": "INR"
  },
  "inventory": {
    "stock": "number",
    "lowStockThreshold": "number",
    "isUnlimited": "boolean",
    "trackInventory": "boolean"
  },
  "variants": [
    {
      "id": "string",
      "name": "string",
      "options": [
        {
          "name": "string",
          "price": "number",
          "stock": "number"
        }
      ]
    }
  ],
  "specifications": {
    "weight": "string",
    "dimensions": "string",
    "material": "string",
    "color": "string",
    "size": "string"
  },
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "keywords": ["string"]
  },
  "isActive": "boolean",
  "isFeatured": "boolean",
  "isDigital": "boolean",
  "requiresShipping": "boolean",
  "preparationTime": "number",
  "rating": "number",
  "totalRatings": "number",
  "totalSales": "number",
  "tags": ["string"],
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **3. seller_orders**
```json
{
  "orderId": "string (document ID)",
  "sellerId": "string",
  "userId": "string",
  "riderId": "string",
  "status": "pending|confirmed|preparing|ready|picked|delivered|cancelled",
  "items": [
    {
      "productId": "string",
      "name": "string",
      "price": "number",
      "quantity": "number",
      "variant": "object",
      "specialInstructions": "string"
    }
  ],
  "pricing": {
    "subtotal": "number",
    "tax": "number",
    "discount": "number",
    "commission": "number",
    "sellerEarning": "number"
  },
  "customerDetails": {
    "name": "string",
    "phone": "string",
    "address": "object"
  },
  "preparationTime": "number",
  "estimatedReadyTime": "timestamp",
  "actualReadyTime": "timestamp",
  "specialInstructions": "string",
  "paymentStatus": "pending|paid|refunded",
  "acceptedAt": "timestamp",
  "rejectedAt": "timestamp",
  "rejectionReason": "string",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **4. seller_analytics**
```json
{
  "analyticsId": "string (document ID)",
  "sellerId": "string",
  "date": "timestamp",
  "metrics": {
    "totalOrders": "number",
    "totalRevenue": "number",
    "averageOrderValue": "number",
    "cancelledOrders": "number",
    "completedOrders": "number",
    "newCustomers": "number",
    "returningCustomers": "number",
    "topProducts": [
      {
        "productId": "string",
        "name": "string",
        "sales": "number",
        "revenue": "number"
      }
    ]
  },
  "performance": {
    "averagePreparationTime": "number",
    "onTimeDeliveryRate": "number",
    "customerSatisfactionScore": "number",
    "orderAcceptanceRate": "number"
  },
  "period": "daily|weekly|monthly",
  "createdAt": "timestamp"
}
```

### **5. seller_payouts**
```json
{
  "payoutId": "string (document ID)",
  "sellerId": "string",
  "period": {
    "startDate": "timestamp",
    "endDate": "timestamp",
    "type": "weekly|monthly"
  },
  "earnings": {
    "totalRevenue": "number",
    "commission": "number",
    "tax": "number",
    "adjustments": "number",
    "netAmount": "number"
  },
  "orderCount": "number",
  "status": "pending|processing|paid|failed",
  "paymentMethod": "bank_transfer|upi",
  "transactionId": "string",
  "processedAt": "timestamp",
  "createdAt": "timestamp"
}
```

---

## 🔄 **SHARED COLLECTIONS** (Cross-App Data)

### **1. unified_orders** (Synced across all apps)
```json
{
  "orderId": "string (document ID)",
  "userId": "string",
  "sellerId": "string",
  "riderId": "string",
  "type": "marketplace|food|grocery|service",
  "status": "pending|confirmed|preparing|ready|picked|delivered|cancelled",
  "milestones": [
    {
      "status": "string",
      "timestamp": "timestamp",
      "updatedBy": "user|seller|rider|system",
      "note": "string",
      "location": {
        "latitude": "number",
        "longitude": "number"
      }
    }
  ],
  "items": ["object"],
  "pricing": "object",
  "addresses": "object",
  "timeline": "object",
  "tracking": {
    "isActive": "boolean",
    "currentLocation": "object",
    "eta": "object",
    "route": ["object"]
  },
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### **2. real_time_tracking** (Live tracking data)
```json
{
  "trackingId": "string (document ID)",
  "orderId": "string",
  "riderId": "string",
  "status": "active|paused|completed",
  "currentLocation": {
    "latitude": "number",
    "longitude": "number",
    "accuracy": "number",
    "speed": "number",
    "heading": "number",
    "timestamp": "timestamp"
  },
  "route": [
    {
      "latitude": "number",
      "longitude": "number",
      "timestamp": "timestamp",
      "speed": "number"
    }
  ],
  "eta": {
    "pickup": "timestamp",
    "delivery": "timestamp",
    "confidence": "number",
    "lastCalculated": "timestamp"
  },
  "milestones": [
    {
      "type": "pickup|delivery",
      "location": "object",
      "estimatedTime": "timestamp",
      "actualTime": "timestamp",
      "status": "pending|reached|completed"
    }
  ],
  "createdAt": "timestamp",
  "lastUpdated": "timestamp"
}
```

---

## 🔐 **SECURITY RULES**

### **User App Security Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Wallets - users can only access their own wallet
    match /wallets/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Transactions - users can only see their own transactions
    match /transactions/{transactionId} {
      allow read: if request.auth != null &&
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.userId;
    }

    // Orders - users can see their own orders
    match /orders/{orderId} {
      allow read, write: if request.auth != null &&
        request.auth.uid == resource.data.userId;
    }

    // Public read access for products
    match /products/{productId} {
      allow read: if true;
    }
  }
}
```

### **Rider App Security Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Riders can only access their own data
    match /riders/{riderId} {
      allow read, write: if request.auth != null && request.auth.uid == riderId;
    }

    // Rider orders - riders can see assigned orders
    match /rider_orders/{orderId} {
      allow read, write: if request.auth != null &&
        request.auth.uid == resource.data.riderId;
    }

    // Tracking data - riders can update their own tracking
    match /rider_tracking/{trackingId} {
      allow read, write: if request.auth != null &&
        request.auth.uid == resource.data.riderId;
    }
  }
}
```

### **Seller App Security Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Sellers can only access their own data
    match /sellers/{sellerId} {
      allow read, write: if request.auth != null && request.auth.uid == sellerId;
    }

    // Products - sellers can manage their own products
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.uid == resource.data.sellerId;
    }

    // Seller orders - sellers can see their orders
    match /seller_orders/{orderId} {
      allow read, write: if request.auth != null &&
        request.auth.uid == resource.data.sellerId;
    }
  }
}
```

---

## 📊 **FIRESTORE INDEXES**

### **Required Composite Indexes**

```javascript
// User App Indexes
{
  "collectionGroup": "transactions",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "timestamp", "order": "DESCENDING"}
  ]
}

{
  "collectionGroup": "orders",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "status", "order": "ASCENDING"},
    {"fieldPath": "createdAt", "order": "DESCENDING"}
  ]
}

// Rider App Indexes
{
  "collectionGroup": "rider_orders",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "riderId", "order": "ASCENDING"},
    {"fieldPath": "status", "order": "ASCENDING"},
    {"fieldPath": "assignedAt", "order": "DESCENDING"}
  ]
}

// Seller App Indexes
{
  "collectionGroup": "products",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "sellerId", "order": "ASCENDING"},
    {"fieldPath": "category", "order": "ASCENDING"},
    {"fieldPath": "isActive", "order": "ASCENDING"}
  ]
}

{
  "collectionGroup": "seller_orders",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "sellerId", "order": "ASCENDING"},
    {"fieldPath": "status", "order": "ASCENDING"},
    {"fieldPath": "createdAt", "order": "DESCENDING"}
  ]
}
```

---

## 🚀 **IMPLEMENTATION GUIDE**

### **1. Firebase Project Setup**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize projects
firebase init firestore --project projek-user
firebase init firestore --project projek-rider-575d2
firebase init firestore --project projek-seller
```

### **2. Collection Creation Script**
```dart
// Create initial collections with sample data
Future<void> initializeFirestore() async {
  final firestore = FirebaseFirestore.instance;

  // Create sample user
  await firestore.collection('users').doc('sample_user').set({
    'email': '<EMAIL>',
    'displayName': 'Sample User',
    'createdAt': FieldValue.serverTimestamp(),
  });

  // Create sample wallet
  await firestore.collection('wallets').doc('sample_user').set({
    'userId': 'sample_user',
    'projekCoinBalance': 0.0,
    'inrBalance': 0.0,
    'lastUpdated': FieldValue.serverTimestamp(),
  });
}
```

### **3. Data Migration Strategy**
1. **Phase 1**: Set up User app collections
2. **Phase 2**: Set up Rider app collections
3. **Phase 3**: Set up Seller app collections
4. **Phase 4**: Implement cross-app synchronization
5. **Phase 5**: Set up real-time tracking

### **4. Backup & Recovery**
```bash
# Export data
gcloud firestore export gs://projek-backup-bucket/$(date +%Y%m%d)

# Import data
gcloud firestore import gs://projek-backup-bucket/20241201
```

---

## 📈 **MONITORING & ANALYTICS**

### **Key Metrics to Track**
- **User App**: DAU, Order conversion, Wallet usage
- **Rider App**: Delivery completion rate, Earnings per hour
- **Seller App**: Order acceptance rate, Revenue per day
- **Cross-App**: Order fulfillment time, Customer satisfaction

### **Firestore Usage Monitoring**
- Document reads/writes per day
- Storage usage per collection
- Query performance metrics
- Security rule violations

---

## 🔧 **MAINTENANCE TASKS**

### **Daily**
- Monitor error logs
- Check security rule violations
- Review performance metrics

### **Weekly**
- Analyze usage patterns
- Update indexes if needed
- Review backup status

### **Monthly**
- Data cleanup (old notifications, expired sessions)
- Performance optimization
- Security audit

---

## 📞 **SUPPORT CONTACTS**

- **Firebase Console**: https://console.firebase.google.com
- **User Project**: https://console.firebase.google.com/project/projek-user/firestore
- **Rider Project**: https://console.firebase.google.com/project/projek-rider-575d2/firestore
- **Seller Project**: https://console.firebase.google.com/project/projek-seller/firestore

---

**Document Version**: 1.0
**Last Updated**: December 2024
**Next Review**: January 2025
