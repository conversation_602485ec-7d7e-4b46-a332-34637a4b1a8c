@echo off
echo ========================================
echo PROJEK USER APP - BUILD APK
echo ========================================
echo.

echo Setting up environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
echo Using custom pub cache: %PUB_CACHE%
echo.

echo Cleaning previous builds...
flutter clean
echo.

echo Getting dependencies...
flutter pub get
echo.

echo Choose build type:
echo 1. Debug APK (faster build, larger size)
echo 2. Release APK (optimized, smaller size)
echo.
set /p buildtype="Enter choice (1-2): "

if "%buildtype%"=="1" (
    echo Building Debug APK for User App...
    flutter build apk --target lib/main_user.dart --debug
    set APK_PATH=build\app\outputs\flutter-apk\app-debug.apk
) else (
    echo Building Release APK for User App...
    flutter build apk --target lib/main_user.dart --release
    set APK_PATH=build\app\outputs\flutter-apk\app-release.apk
)

echo.
echo ========================================
echo BUILD COMPLETED!
echo ========================================
echo.

if exist "%APK_PATH%" (
    echo ✅ APK successfully built!
    echo 📱 Location: %APK_PATH%
    echo 📦 Size: 
    dir "%APK_PATH%" | findstr /C:".apk"
    echo.
    echo To install on your device:
    echo adb install "%APK_PATH%"
    echo.
    echo Or copy the APK to your phone and install manually.
) else (
    echo ❌ APK build failed! Check the output above for errors.
)

echo.
pause
