<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50" width="200" height="50">
  <!-- Logo Icon -->
  <g transform="translate(5, 5)">
    <!-- Icon Background -->
    <circle cx="20" cy="20" r="18" fill="url(#navGradient)" stroke="#138808" stroke-width="1.5"/>
    
    <!-- Super Vision Icon -->
    <g transform="translate(8, 8)">
      <!-- Eye Symbol -->
      <ellipse cx="12" cy="12" rx="10" ry="6" fill="#ffffff" opacity="0.9"/>
      <circle cx="12" cy="12" r="5" fill="#146eb4"/>
      <circle cx="12" cy="12" r="2.5" fill="#ffffff"/>
      
      <!-- Vision Rays -->
      <g stroke="#ff9933" stroke-width="1.5" opacity="0.7">
        <line x1="3" y1="9" x2="5" y2="11"/>
        <line x1="3" y1="15" x2="5" y2="13"/>
        <line x1="21" y1="9" x2="19" y2="11"/>
        <line x1="21" y1="15" x2="19" y2="13"/>
        <line x1="12" y1="3" x2="12" y2="5"/>
        <line x1="12" y1="21" x2="12" y2="19"/>
      </g>
    </g>
  </g>
  
  <!-- Brand Text -->
  <g transform="translate(50, 15)">
    <!-- Super Vision Text -->
    <text x="0" y="15" font-family="Poppins, sans-serif" font-size="16" font-weight="700" fill="#2c2c54">
      Super Vision
    </text>
    
    <!-- Tagline -->
    <text x="0" y="28" font-family="Poppins, sans-serif" font-size="8" font-weight="400" fill="#138808">
      Connecting India
    </text>
  </g>
  
  <!-- Indian Flag Accent -->
  <g transform="translate(180, 15)">
    <rect x="0" y="0" width="3" height="6" fill="#ff9933"/>
    <rect x="0" y="6" width="3" height="6" fill="#ffffff" stroke="#ddd" stroke-width="0.3"/>
    <rect x="0" y="12" width="3" height="6" fill="#138808"/>
    <circle cx="1.5" cy="9" r="2" fill="#000080" opacity="0.8"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="navGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fff3e0;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
