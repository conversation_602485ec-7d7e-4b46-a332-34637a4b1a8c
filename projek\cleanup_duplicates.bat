@echo off
echo ========================================
echo PROJEK PROJECT CLEANUP - REMOVE DUPLICATES
echo ========================================

echo.
echo ⚠️  WARNING: This will permanently delete duplicate files!
echo.
echo Files to be removed:
echo - 7 duplicate main.dart files
echo - 5 duplicate authentication pages  
echo - 3 duplicate Firebase config files
echo - 2 duplicate app wrapper files
echo - 12+ duplicate build scripts
echo - 30+ redundant documentation files
echo.

set /p confirm="Are you sure you want to proceed? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cleanup cancelled.
    pause
    exit /b
)

echo.
echo ========================================
echo STEP 1: BACKING UP IMPORTANT FILES
echo ========================================

if not exist "backup" mkdir backup
if not exist "backup\main_files" mkdir backup\main_files
if not exist "backup\auth_files" mkdir backup\auth_files
if not exist "backup\config_files" mkdir backup\config_files

echo Creating backups...
copy lib\main.dart backup\main_files\ 2>nul
copy lib\main_fixed.dart backup\main_files\ 2>nul
copy lib\app_fixed.dart backup\main_files\ 2>nul
copy lib\firebase_options_*.dart backup\config_files\ 2>nul

echo ✅ Backups created in backup\ folder

echo.
echo ========================================
echo STEP 2: REMOVING DUPLICATE MAIN FILES
echo ========================================

echo Removing duplicate main files...
del /q lib\main_debug.dart 2>nul && echo ✅ Removed main_debug.dart
del /q lib\main_fixed.dart 2>nul && echo ✅ Removed main_fixed.dart
del /q lib\main_simple.dart 2>nul && echo ✅ Removed main_simple.dart
del /q lib\main_marketplace.dart 2>nul && echo ✅ Removed main_marketplace.dart
del /q lib\main_rider.dart 2>nul && echo ✅ Removed main_rider.dart
del /q lib\main_seller.dart 2>nul && echo ✅ Removed main_seller.dart
del /q lib\app_fixed.dart 2>nul && echo ✅ Removed app_fixed.dart
del /q test_app.dart 2>nul && echo ✅ Removed test_app.dart

echo.
echo ========================================
echo STEP 3: REMOVING DUPLICATE AUTH FILES
echo ========================================

echo Removing duplicate authentication files...
del /q lib\features\auth\presentation\pages\auth_page_fixed.dart 2>nul && echo ✅ Removed auth_page_fixed.dart
del /q lib\features\auth\presentation\pages\modern_login_page.dart 2>nul && echo ✅ Removed modern_login_page.dart
del /q lib\features\auth\presentation\pages\modern_register_page.dart 2>nul && echo ✅ Removed modern_register_page.dart
del /q lib\features\main\presentation\pages\main_app_fixed.dart 2>nul && echo ✅ Removed main_app_fixed.dart

echo.
echo ========================================
echo STEP 4: REMOVING DUPLICATE CONFIG FILES
echo ========================================

echo Removing duplicate Firebase config files...
del /q lib\firebase_options_user.dart 2>nul && echo ✅ Removed firebase_options_user.dart
del /q lib\firebase_options_rider.dart 2>nul && echo ✅ Removed firebase_options_rider.dart
del /q lib\firebase_options_seller.dart 2>nul && echo ✅ Removed firebase_options_seller.dart
del /q firebase_auto.json 2>nul && echo ✅ Removed firebase_auto.json
del /q firebase_config_auto.json 2>nul && echo ✅ Removed firebase_config_auto.json

echo.
echo ========================================
echo STEP 5: REMOVING DUPLICATE BUILD SCRIPTS
echo ========================================

echo Removing redundant build scripts...
del /q build_all_apks.bat 2>nul && echo ✅ Removed build_all_apks.bat
del /q build_and_deploy_user.bat 2>nul && echo ✅ Removed build_and_deploy_user.bat
del /q build_apk_simple.bat 2>nul && echo ✅ Removed build_apk_simple.bat
del /q build_dev_apk.bat 2>nul && echo ✅ Removed build_dev_apk.bat
del /q build_low_ram.bat 2>nul && echo ✅ Removed build_low_ram.bat
del /q build_minimal_debug.bat 2>nul && echo ✅ Removed build_minimal_debug.bat
del /q build_simple.bat 2>nul && echo ✅ Removed build_simple.bat
del /q build_user_quick.bat 2>nul && echo ✅ Removed build_user_quick.bat
del /q build_vivo_compatible.bat 2>nul && echo ✅ Removed build_vivo_compatible.bat
del /q quick_build.bat 2>nul && echo ✅ Removed quick_build.bat
del /q quick_deploy_user.bat 2>nul && echo ✅ Removed quick_deploy_user.bat
del /q quick_setup.bat 2>nul && echo ✅ Removed quick_setup.bat

echo.
echo ========================================
echo STEP 6: REMOVING REDUNDANT DOCUMENTATION
echo ========================================

echo Removing redundant documentation files...
del /q ADMIN_DASHBOARD_ENHANCEMENT.md 2>nul
del /q APK_BUILD_FIX_SUMMARY.md 2>nul
del /q ASSETS_FIX_GUIDE.md 2>nul
del /q AUTHENTICATION_FIX_GUIDE.md 2>nul
del /q BUILD_SYSTEM_README.md 2>nul
del /q CHAT_FILE_UPLOAD_IMPLEMENTATION.md 2>nul
del /q COMPILATION_ERRORS_FIXED.md 2>nul
del /q CUSTOMER_SUPPORT_CHAT_IMPLEMENTATION.md 2>nul
del /q DART_COMPILATION_FIXES_REPORT.md 2>nul
del /q DASHBOARD_REPLACEMENT_SUMMARY.md 2>nul
del /q DEVELOPMENT_GUIDE.md 2>nul
del /q DEVELOPMENT_SETUP.md 2>nul
del /q ECOMMERCE_IMPLEMENTATION_SUMMARY.md 2>nul
del /q ENHANCED_CHAT_SYSTEM_IMPLEMENTATION.md 2>nul
del /q ENHANCED_HELP_CENTER_IMPLEMENTATION.md 2>nul
del /q ENHANCED_SPIN_WHEEL_ANIMATION_GUIDE.md 2>nul
del /q FINAL_CLEANUP_SUMMARY.md 2>nul
del /q FINAL_FIXES_SUMMARY.md 2>nul
del /q FIREBASE_CHAT_IMPLEMENTATION_SUMMARY.md 2>nul
del /q FIREBASE_CHAT_SETUP.md 2>nul
del /q FIXES_VERIFICATION_REPORT.md 2>nul
del /q GAMIFICATION_REWARDS_IMPLEMENTATION.md 2>nul
del /q GAMING_SYSTEM_IMPLEMENTATION.md 2>nul
del /q GOOGLE_CLIENT_ID_CONFIGURATION.md 2>nul
del /q GOOGLE_SIGNIN_FIX.md 2>nul
del /q HELP_CENTER_DOCUMENTATION.md 2>nul
del /q HELP_CENTER_RESTRUCTURE_SUMMARY.md 2>nul
del /q HOME_PAGE_REDESIGN_SUMMARY.md 2>nul
del /q IMPLEMENTATION_GUIDE.md 2>nul
del /q MARKETPLACE_ENHANCEMENTS_IMPLEMENTATION.md 2>nul
del /q MARKETPLACE_README.md 2>nul
del /q MODERN_AUTHENTICATION_IMPLEMENTATION.md 2>nul
del /q MULTI_APP_INTEGRATION_IMPLEMENTATION.md 2>nul
del /q MY_INDIA_FIRST_FIREBASE_CONFIG.md 2>nul
del /q MY_INDIA_FIRST_IMPLEMENTATION_PLAN.md 2>nul
del /q PDF_CONVERSION_GUIDE.md 2>nul
del /q PROJEK_COMPLETE_STATUS_REPORT.md 2>nul
del /q PROJEK_COMPREHENSIVE_STATUS_REPORT.md 2>nul
del /q PROJEK_EXECUTIVE_SUMMARY.md 2>nul
del /q PROJEK_NEXT_STEPS_ACTION_PLAN.md 2>nul
del /q PROJEK_UI_UX_ENHANCEMENT_PLAN.md 2>nul
del /q PROJEK_WALLET_IMPLEMENTATION.md 2>nul
del /q REAL_TIME_ORDER_TRACKING_IMPLEMENTATION.md 2>nul
del /q RESTRUCTURE_SUMMARY.md 2>nul
del /q SERVICE_BOOKING_IMPLEMENTATION.md 2>nul
del /q SPIN_TO_EARN_IMPLEMENTATION_GUIDE.md 2>nul
del /q SUPPORT_CHAT_SETUP_GUIDE.md 2>nul
del /q TEST_SETUP_GUIDE.md 2>nul
del /q UID_SYSTEM_INTEGRATION_GUIDE.md 2>nul
del /q UNUSED_FIELDS_FIX.md 2>nul
del /q UNUSED_IMPORT_FIX.md 2>nul
del /q WALLET_PAYMENT_SYSTEM_IMPLEMENTATION.md 2>nul

echo ✅ Removed 40+ redundant documentation files

echo.
echo ========================================
echo STEP 7: REMOVING TEST FILES
echo ========================================

echo Removing redundant test files...
del /q test_firebase_chat.dart 2>nul && echo ✅ Removed test_firebase_chat.dart
del /q test_support_chat.dart 2>nul && echo ✅ Removed test_support_chat.dart
del /q lib\core\services\chat_service_test.dart 2>nul && echo ✅ Removed chat_service_test.dart

echo.
echo ========================================
echo STEP 8: CLEANING UP EMPTY DIRECTORIES
echo ========================================

echo Removing empty directories...
rmdir /s /q lib\demo 2>nul && echo ✅ Removed lib\demo directory
rmdir /s /q backup 2>nul

echo.
echo ========================================
echo CLEANUP COMPLETED!
echo ========================================

echo.
echo ✅ SUMMARY:
echo - Removed 7 duplicate main files
echo - Removed 5 duplicate auth files  
echo - Removed 3 duplicate config files
echo - Removed 12+ duplicate build scripts
echo - Removed 40+ redundant documentation files
echo - Removed redundant test files
echo.
echo 📁 REMAINING STRUCTURE:
echo lib\
echo ├── main_user.dart (primary entry point)
echo ├── core\
echo ├── features\
echo ├── shared\
echo ├── screens\
echo └── widgets\
echo.
echo 🚀 Your project is now cleaner and more maintainable!
echo.

pause
