import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../domain/models/booking.dart';
import '../../data/services/booking_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class BookingManagementPage extends ConsumerStatefulWidget {
  final String bookingId;

  const BookingManagementPage({super.key, required this.bookingId});

  @override
  ConsumerState<BookingManagementPage> createState() =>
      _BookingManagementPageState();
}

class _BookingManagementPageState extends ConsumerState<BookingManagementPage> {
  Booking? _booking;
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadBooking();
  }

  Future<void> _loadBooking() async {
    try {
      final booking = await BookingService.getBookingById(widget.bookingId);
      setState(() {
        _booking = booking;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load booking details');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Booking Details'),
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_booking == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Booking Details'),
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: Text('Booking not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Booking #${_booking!.bookingNumber}'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        actions: [
          if (_canCancelBooking())
            IconButton(
              onPressed: _showCancelDialog,
              icon: const Icon(Icons.cancel),
              tooltip: 'Cancel Booking',
            ),
          if (_canRescheduleBooking())
            IconButton(
              onPressed: _showRescheduleDialog,
              icon: const Icon(Icons.schedule),
              tooltip: 'Reschedule Booking',
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadBooking,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusCard(),
              const SizedBox(height: 16),
              _buildBookingDetails(),
              const SizedBox(height: 16),
              _buildServiceProviderCard(),
              const SizedBox(height: 16),
              _buildScheduleCard(),
              const SizedBox(height: 16),
              _buildPaymentCard(),
              if (_booking!.confirmation != null) ...[
                const SizedBox(height: 16),
                _buildConfirmationCard(),
              ],
              if (_booking!.cancellation != null) ...[
                const SizedBox(height: 16),
                _buildCancellationCard(),
              ],
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildActionButtons(),
    );
  }

  Widget _buildStatusCard() {
    final status = _booking!.status;
    Color statusColor = AppColors.textSecondary;
    IconData statusIcon = Icons.info;

    switch (status) {
      case BookingStatus.pending:
        statusColor = AppColors.warning;
        statusIcon = Icons.pending;
        break;
      case BookingStatus.confirmed:
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        break;
      case BookingStatus.inProgress:
        statusColor = AppColors.info;
        statusIcon = Icons.play_circle;
        break;
      case BookingStatus.completed:
        statusColor = AppColors.success;
        statusIcon = Icons.task_alt;
        break;
      case BookingStatus.cancelled:
        statusColor = AppColors.error;
        statusIcon = Icons.cancel;
        break;
      case BookingStatus.rejected:
        statusColor = AppColors.error;
        statusIcon = Icons.block;
        break;
      case BookingStatus.rescheduled:
        statusColor = AppColors.warning;
        statusIcon = Icons.schedule;
        break;
      case BookingStatus.noShow:
        statusColor = AppColors.error;
        statusIcon = Icons.person_off;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 32),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusText(status),
                  style: AppTextStyles.titleLarge.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStatusDescription(status),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetails() {
    return _buildCard(
      title: 'Booking Details',
      icon: Icons.receipt_long,
      child: Column(
        children: [
          _buildDetailRow('Service', _booking!.serviceName),
          _buildDetailRow('Booking Number', _booking!.bookingNumber),
          _buildDetailRow('Created', _formatDateTime(_booking!.createdAt)),
          _buildDetailRow(
            'Duration',
            '${_booking!.schedule.durationHours} hours',
          ),
          if (_booking!.specialInstructions != null)
            _buildDetailRow('Instructions', _booking!.specialInstructions!),
        ],
      ),
    );
  }

  Widget _buildServiceProviderCard() {
    return _buildCard(
      title: 'Service Provider',
      icon: Icons.person,
      child: Column(
        children: [
          _buildDetailRow('Name', _booking!.providerName),
          _buildDetailRow('Phone', _booking!.providerPhone),
          if (_booking!.providerEmail != null)
            _buildDetailRow('Email', _booking!.providerEmail!),
        ],
      ),
    );
  }

  Widget _buildScheduleCard() {
    return _buildCard(
      title: 'Schedule',
      icon: Icons.schedule,
      child: Column(
        children: [
          _buildDetailRow(
            'Date',
            _formatDate(_booking!.schedule.scheduledDate),
          ),
          _buildDetailRow(
            'Time',
            '${_formatTime(_booking!.schedule.startTime)} - ${_formatTime(_booking!.schedule.endTime)}',
          ),
          _buildDetailRow('Address', _booking!.address.street),
          if (_booking!.address.landmark != null)
            _buildDetailRow('Landmark', _booking!.address.landmark!),
        ],
      ),
    );
  }

  Widget _buildPaymentCard() {
    return _buildCard(
      title: 'Payment Details',
      icon: Icons.payment,
      child: Column(
        children: [
          _buildDetailRow(
            'Service Amount',
            '₹${_booking!.serviceAmount.toStringAsFixed(2)}',
          ),
          _buildDetailRow(
            'Platform Fee',
            '₹${_booking!.platformFee.toStringAsFixed(2)}',
          ),
          _buildDetailRow('Taxes', '₹${_booking!.taxes.toStringAsFixed(2)}'),
          const Divider(),
          _buildDetailRow(
            'Total Amount',
            '₹${_booking!.totalAmount.toStringAsFixed(2)}',
            isHighlighted: true,
          ),
          _buildDetailRow(
            'Payment Status',
            _getPaymentStatusText(_booking!.paymentStatus),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationCard() {
    final confirmation = _booking!.confirmation!;
    return _buildCard(
      title: 'Confirmation Details',
      icon: Icons.check_circle,
      child: Column(
        children: [
          _buildDetailRow(
            'Confirmed At',
            _formatDateTime(confirmation.confirmedAt),
          ),
          _buildDetailRow('Confirmed By', 'Service Provider'),
          if (confirmation.confirmationMessage != null)
            _buildDetailRow('Message', confirmation.confirmationMessage!),
        ],
      ),
    );
  }

  Widget _buildCancellationCard() {
    final cancellation = _booking!.cancellation!;
    return _buildCard(
      title: 'Cancellation Details',
      icon: Icons.cancel,
      child: Column(
        children: [
          _buildDetailRow(
            'Cancelled At',
            _formatDateTime(cancellation.cancelledAt),
          ),
          _buildDetailRow(
            'Reason',
            _getCancellationReasonText(cancellation.reason),
          ),
          if (cancellation.description != null)
            _buildDetailRow('Description', cancellation.description!),
          if (cancellation.refundAmount > 0)
            _buildDetailRow(
              'Refund Amount',
              '₹${cancellation.refundAmount.toStringAsFixed(2)}',
            ),
          if (cancellation.cancellationFee > 0)
            _buildDetailRow(
              'Cancellation Fee',
              '₹${cancellation.cancellationFee.toStringAsFixed(2)}',
            ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                color: isHighlighted
                    ? AppColors.primaryBlue
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_booking!.status == BookingStatus.completed ||
        _booking!.status == BookingStatus.cancelled) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_canCancelBooking())
            Expanded(
              child: OutlinedButton(
                onPressed: _isProcessing ? null : _showCancelDialog,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.error,
                  side: BorderSide(color: AppColors.error),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Cancel Booking'),
              ),
            ),

          if (_canCancelBooking() && _canRescheduleBooking())
            const SizedBox(width: 16),

          if (_canRescheduleBooking())
            Expanded(
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _showRescheduleDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isProcessing
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('Reschedule'),
              ),
            ),
        ],
      ),
    );
  }

  // Action methods
  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning, color: AppColors.warning),
            const SizedBox(width: 12),
            const Text('Cancel Booking'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to cancel this booking?'),
            const SizedBox(height: 16),
            _buildCancellationPolicy(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showCancellationReasonDialog();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel Booking'),
          ),
        ],
      ),
    );
  }

  void _showCancellationReasonDialog() {
    String? selectedReason;
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('Cancellation Reason'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<CancellationReason>(
                decoration: const InputDecoration(
                  labelText: 'Select Reason',
                  border: OutlineInputBorder(),
                ),
                items:
                    [
                      CancellationReason.userCancelled,
                      CancellationReason.emergencyIssue,
                      CancellationReason.weatherConditions,
                      CancellationReason.other,
                    ].map((reason) {
                      return DropdownMenuItem(
                        value: reason,
                        child: Text(_getCancellationReasonText(reason)),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() => selectedReason = value?.toString());
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  labelText: 'Additional Details (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedReason != null
                  ? () {
                      Navigator.of(context).pop();
                      _cancelBooking(
                        CancellationReason.values.firstWhere(
                          (r) => r.toString() == selectedReason,
                        ),
                        reasonController.text.isNotEmpty
                            ? reasonController.text
                            : null,
                      );
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
              ),
              child: const Text('Confirm Cancellation'),
            ),
          ],
        ),
      ),
    );
  }

  void _showRescheduleDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('Reschedule Booking'),
        content: const Text(
          'Rescheduling feature will allow you to select a new date and time for your service.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonSnackBar('Reschedule feature coming soon!');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reschedule'),
          ),
        ],
      ),
    );
  }

  Widget _buildCancellationPolicy() {
    final now = DateTime.now();
    final scheduledTime = _booking!.schedule.scheduledDate;
    final hoursUntilService = scheduledTime.difference(now).inHours;

    String refundText = '';
    if (hoursUntilService >= 24) {
      refundText = 'Full refund (100%)';
    } else if (hoursUntilService >= 12) {
      refundText = '75% refund (25% cancellation fee)';
    } else if (hoursUntilService >= 6) {
      refundText = '50% refund (50% cancellation fee)';
    } else {
      refundText = 'No refund';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cancellation Policy:',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            refundText,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.warning,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelBooking(
    CancellationReason reason,
    String? description,
  ) async {
    setState(() => _isProcessing = true);

    try {
      final updatedBooking = await BookingService.cancelBooking(
        bookingId: _booking!.id,
        reason: reason,
        description: description,
      );

      setState(() {
        _booking = updatedBooking;
        _isProcessing = false;
      });

      _showSuccessSnackBar('Booking cancelled successfully');
    } catch (e) {
      setState(() => _isProcessing = false);
      _showErrorSnackBar('Failed to cancel booking: ${e.toString()}');
    }
  }

  // Helper methods
  bool _canCancelBooking() {
    return _booking!.status == BookingStatus.pending ||
        _booking!.status == BookingStatus.confirmed;
  }

  bool _canRescheduleBooking() {
    return _booking!.status == BookingStatus.pending ||
        _booking!.status == BookingStatus.confirmed;
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending Confirmation';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.inProgress:
        return 'In Progress';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.rejected:
        return 'Rejected';
      case BookingStatus.rescheduled:
        return 'Rescheduled';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  String _getStatusDescription(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 'Waiting for service provider confirmation';
      case BookingStatus.confirmed:
        return 'Service provider has confirmed your booking';
      case BookingStatus.inProgress:
        return 'Service is currently in progress';
      case BookingStatus.completed:
        return 'Service has been completed successfully';
      case BookingStatus.cancelled:
        return 'This booking has been cancelled';
      case BookingStatus.rejected:
        return 'Service provider rejected this booking';
      case BookingStatus.rescheduled:
        return 'This booking has been rescheduled';
      case BookingStatus.noShow:
        return 'Service provider did not show up';
    }
  }

  String _getPaymentStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partialRefund:
        return 'Partially Refunded';
    }
  }

  String _getCancellationReasonText(CancellationReason reason) {
    switch (reason) {
      case CancellationReason.userCancelled:
        return 'User Cancelled';
      case CancellationReason.providerCancelled:
        return 'Provider Cancelled';
      case CancellationReason.emergencyIssue:
        return 'Emergency Issue';
      case CancellationReason.weatherConditions:
        return 'Weather Conditions';
      case CancellationReason.technicalIssue:
        return 'Technical Issue';
      case CancellationReason.noShow:
        return 'No Show';
      case CancellationReason.other:
        return 'Other';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} at ${_formatTime(dateTime.toString().substring(11, 16))}';
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatTime(String time) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);

    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showComingSoonSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
