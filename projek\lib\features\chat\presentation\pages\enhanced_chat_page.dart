import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../domain/models/enhanced_chat_models.dart';
import '../../data/services/working_chat_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/message_bubble_widget.dart';
import '../widgets/media_picker_widget.dart';
import '../widgets/chat_input_widget.dart';

final chatProvider = StreamProvider.family<EnhancedChat, String>((ref, chatId) {
  return Stream.fromFuture(
    WorkingChatService.getChatById(chatId).then((chat) => chat as EnhancedChat)
  );
});

final messagesProvider = StreamProvider.family<List<EnhancedChatMessage>, String>((ref, chatId) {
  return WorkingChatService.getChatMessagesStream(chatId).map(
    (messages) => messages.map((msg) => msg as EnhancedChatMessage).toList()
  );
});

class EnhancedChatPage extends ConsumerStatefulWidget {
  final String chatId;

  const EnhancedChatPage({
    super.key,
    required this.chatId,
  });

  @override
  ConsumerState<EnhancedChatPage> createState() => _EnhancedChatPageState();
}

class _EnhancedChatPageState extends ConsumerState<EnhancedChatPage>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabController;
  
  bool _isTyping = false;
  bool _showMediaPicker = false;
  EnhancedChatMessage? _replyToMessage;
  final List<File> _selectedMedia = [];

  @override
  void initState() {
    super.initState();
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  void _onTypingChanged() {
    final isTyping = _messageController.text.isNotEmpty;
    if (isTyping != _isTyping) {
      setState(() => _isTyping = isTyping);
      if (isTyping) {
        _fabController.forward();
      } else {
        _fabController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _fabController.dispose();
    super.dispose();
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _messageController.addListener(_onTypingChanged);
  }

  @override
  Widget build(BuildContext context) {
    final chatAsync = ref.watch(chatProvider(widget.chatId));
    final messagesAsync = ref.watch(messagesProvider(widget.chatId));

    return Scaffold(
      appBar: _buildAppBar(chatAsync),
      body: Column(
        children: [
          // Messages
          Expanded(
            child: messagesAsync.when(
              data: (messages) => _buildMessagesList(messages),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error loading messages: $error'),
              ),
            ),
          ),
          
          // Media picker
          if (_showMediaPicker) _buildMediaPicker(),
          
          // Reply preview
          if (_replyToMessage != null) _buildReplyPreview(),
          
          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AsyncValue<EnhancedChat> chatAsync) {
    return AppBar(
      backgroundColor: AppColors.primaryBlue,
      foregroundColor: Colors.white,
      title: chatAsync.when(
        data: (chat) => Row(
          children: [
            if (chat.avatarUrl != null)
              CircleAvatar(
                radius: 16,
                backgroundImage: CachedNetworkImageProvider(chat.avatarUrl!),
              )
            else
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                child: Icon(
                  _getChatIcon(chat.type),
                  size: 16,
                  color: Colors.white,
                ),
              ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    chat.name,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (chat.type == EnhancedChatType.group)
                    Text(
                      '${chat.participants.length} members',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        loading: () => const Text('Loading...'),
        error: (_, __) => const Text('Error'),
      ),
      actions: [
        IconButton(
          onPressed: () => _showChatInfo(chatAsync.value),
          icon: const Icon(Icons.info_outline),
        ),
        if (chatAsync.value?.type == EnhancedChatType.group)
          IconButton(
            onPressed: () => _showParticipants(chatAsync.value!),
            icon: const Icon(Icons.group),
          ),
      ],
    );
  }

  Widget _buildMessagesList(List<EnhancedChatMessage> messages) {
    if (messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No messages yet',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start the conversation!',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      reverse: true,
      padding: const EdgeInsets.all(16),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final previousMessage = index < messages.length - 1 ? messages[index + 1] : null;
        final nextMessage = index > 0 ? messages[index - 1] : null;
        
        return MessageBubbleWidget(
          message: message,
          previousMessage: previousMessage,
          nextMessage: nextMessage,
          onReply: (msg) => _setReplyMessage(msg),
          onReaction: (emoji) => _addReaction(message.id, emoji),
        );
      },
    );
  }

  Widget _buildMediaPicker() {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: MediaPickerWidget(
        onImageSelected: _handleImageSelected,
        onVideoSelected: _handleVideoSelected,
        onDocumentSelected: _handleDocumentSelected,
        onLocationSelected: _handleLocationSelected,
      ),
    );
  }

  Widget _buildReplyPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: AppColors.primaryBlue,
            width: 3,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to ${_replyToMessage!.senderName}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _replyToMessage!.content,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => setState(() => _replyToMessage = null),
            icon: Icon(Icons.close, size: 20, color: AppColors.textSecondary),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ChatInputWidget(
        controller: _messageController,
        onSendMessage: _sendMessage,
        onAttachmentTap: _toggleMediaPicker,
        onVoiceRecordStart: _startVoiceRecording,
        onVoiceRecordEnd: _endVoiceRecording,
        isTyping: _isTyping,
        hasSelectedMedia: _selectedMedia.isNotEmpty,
      ),
    );
  }

  // Event handlers
  void _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty && _selectedMedia.isEmpty) return;

    try {
      if (_selectedMedia.isNotEmpty) {
        // Send message with media
        await WorkingChatService.sendMessageWithMedia(
          chatId: widget.chatId,
          content: content,
          mediaFiles: _selectedMedia,
          type: _getMessageTypeFromMedia(_selectedMedia.first).toString().split('.').last,
          replyToMessageId: _replyToMessage?.id,
        );
      } else {
        // Send text message
        await WorkingChatService.sendMessage(
          chatId: widget.chatId,
          content: content,
          replyToMessageId: _replyToMessage?.id,
          extraData: {},
        );
      }

      // Clear input
      _messageController.clear();
      _selectedMedia.clear();
      _replyToMessage = null;
      _showMediaPicker = false;
      setState(() {});

      // Scroll to bottom
      _scrollToBottom();
    } catch (e) {
      _showErrorSnackBar('Failed to send message: ${e.toString()}');
    }
  }

  void _toggleMediaPicker() {
    setState(() => _showMediaPicker = !_showMediaPicker);
  }

  void _handleImageSelected(File image) async {
    setState(() {
      _selectedMedia.add(image);
      _showMediaPicker = false;
    });
  }

  void _handleVideoSelected(File video) async {
    setState(() {
      _selectedMedia.add(video);
      _showMediaPicker = false;
    });
  }

  void _handleDocumentSelected(File document) async {
    setState(() {
      _selectedMedia.add(document);
      _showMediaPicker = false;
    });
  }

  void _handleLocationSelected(double lat, double lng) async {
    // Handle location sharing
    await WorkingChatService.sendMessage(
      chatId: widget.chatId,
      content: 'Shared location',
      type: EnhancedMessageType.location.toString().split('.').last,
      extraData: {
        'latitude': lat,
        'longitude': lng,
      },
    );
  }

  void _setReplyMessage(EnhancedChatMessage message) {
    setState(() => _replyToMessage = message);
  }

  void _addReaction(String messageId, String emoji) async {
    try {
      await WorkingChatService.addReaction(
        messageId: messageId,
        emoji: emoji,
      );
    } catch (e) {
      _showErrorSnackBar('Failed to add reaction');
    }
  }

  void _startVoiceRecording() {
    // Implement voice recording
    debugPrint('Starting voice recording...');
  }

  void _endVoiceRecording() {
    // Implement voice recording end
    debugPrint('Ending voice recording...');
  }

  void _showChatInfo(EnhancedChat? chat) {
    if (chat == null) return;
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => _buildChatInfoSheet(chat, scrollController),
      ),
    );
  }

  Widget _buildChatInfoSheet(EnhancedChat chat, ScrollController scrollController) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // Chat info
          Text(
            'Chat Information',
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // Add more chat info widgets here
        ],
      ),
    );
  }

  void _showParticipants(EnhancedChat chat) {
    // Show participants list
    debugPrint('Showing participants for ${chat.name}');
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  IconData _getChatIcon(EnhancedChatType type) {
    switch (type) {
      case EnhancedChatType.directMessage:
        return Icons.person;
      case EnhancedChatType.group:
        return Icons.group;
      case EnhancedChatType.order:
        return Icons.shopping_bag;
      case EnhancedChatType.service:
        return Icons.build;
      case EnhancedChatType.support:
        return Icons.support_agent;
      case EnhancedChatType.broadcast:
        return Icons.emergency;
    }
  }

  EnhancedMessageType _getMessageTypeFromMedia(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif'].contains(extension)) {
      return EnhancedMessageType.image;
    } else if (['mp4', 'mov', 'avi'].contains(extension)) {
      return EnhancedMessageType.video;
    } else if (['mp3', 'wav', 'aac'].contains(extension)) {
      return EnhancedMessageType.audio;
    } else {
      return EnhancedMessageType.file;
    }
  }
}





















