enum Environment { development, staging, production }

class EnvironmentConfig {
  static Environment _currentEnvironment = Environment.development;
  
  static Environment get currentEnvironment => _currentEnvironment;
  
  static void setEnvironment(Environment environment) {
    _currentEnvironment = environment;
  }
  
  static bool get isDevelopment => _currentEnvironment == Environment.development;
  static bool get isStaging => _currentEnvironment == Environment.staging;
  static bool get isProduction => _currentEnvironment == Environment.production;
  
  // API Base URLs
  static String get apiBaseUrl {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'https://dev-api.projek.com';
      case Environment.staging:
        return 'https://staging-api.projek.com';
      case Environment.production:
        return 'https://api.projek.com';
    }
  }
  
  // Firebase Project IDs
  static String get firebaseProjectId {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'projek-dev';
      case Environment.staging:
        return 'projek-staging';
      case Environment.production:
        return 'projek-prod';
    }
  }
  
  // Payment Gateway Keys
  static String get razorpayKeyId {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'rzp_test_dev_key';
      case Environment.staging:
        return 'rzp_test_staging_key';
      case Environment.production:
        return 'rzp_live_prod_key';
    }
  }
  
  // Google Maps API Key
  static String get googleMapsApiKey {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'dev_google_maps_key';
      case Environment.staging:
        return 'staging_google_maps_key';
      case Environment.production:
        return 'prod_google_maps_key';
    }
  }
  
  // Analytics Configuration
  static bool get enableAnalytics {
    switch (_currentEnvironment) {
      case Environment.development:
        return false;
      case Environment.staging:
        return true;
      case Environment.production:
        return true;
    }
  }
  
  // Crashlytics Configuration
  static bool get enableCrashlytics {
    switch (_currentEnvironment) {
      case Environment.development:
        return false;
      case Environment.staging:
        return true;
      case Environment.production:
        return true;
    }
  }
  
  // Debug Configuration
  static bool get enableDebugMode {
    switch (_currentEnvironment) {
      case Environment.development:
        return true;
      case Environment.staging:
        return true;
      case Environment.production:
        return false;
    }
  }
  
  // Logging Configuration
  static bool get enableDetailedLogging {
    switch (_currentEnvironment) {
      case Environment.development:
        return true;
      case Environment.staging:
        return true;
      case Environment.production:
        return false;
    }
  }
  
  // Cache Configuration
  static Duration get cacheExpiry {
    switch (_currentEnvironment) {
      case Environment.development:
        return const Duration(minutes: 5);
      case Environment.staging:
        return const Duration(hours: 1);
      case Environment.production:
        return const Duration(hours: 24);
    }
  }
  
  // Network Timeout
  static Duration get networkTimeout {
    switch (_currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 60);
      case Environment.staging:
        return const Duration(seconds: 30);
      case Environment.production:
        return const Duration(seconds: 30);
    }
  }
}
