import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:go_router/go_router.dart';

import '../../../wallet/presentation/providers/wallet_provider.dart';

class SuperAppDashboard extends ConsumerStatefulWidget {
  const SuperAppDashboard({super.key});

  @override
  ConsumerState<SuperAppDashboard> createState() => _SuperAppDashboardState();
}

class _SuperAppDashboardState extends ConsumerState<SuperAppDashboard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final user = FirebaseAuth.instance.currentUser;
    final walletAsync = ref.watch(walletProvider);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  // Custom App Bar
                  _buildSliverAppBar(theme, colorScheme, user),

                  // Dashboard Content
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Wallet Balance Card
                          _buildWalletCard(theme, colorScheme, walletAsync),
                          const SizedBox(height: 20),

                          // Quick Actions
                          _buildQuickActions(theme, colorScheme),
                          const SizedBox(height: 24),

                          // Services Grid
                          _buildServicesGrid(theme, colorScheme),
                          const SizedBox(height: 24),

                          // Recent Activities
                          _buildRecentActivities(theme, colorScheme),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSliverAppBar(
    ThemeData theme,
    ColorScheme colorScheme,
    User? user,
  ) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [colorScheme.primary, colorScheme.secondary],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      // User Avatar
                      CircleAvatar(
                        radius: 24,
                        backgroundColor: colorScheme.onPrimary.withValues(
                          alpha: 0.2,
                        ),
                        backgroundImage: user?.photoURL != null
                            ? NetworkImage(user!.photoURL!)
                            : null,
                        child: user?.photoURL == null
                            ? Icon(
                                Icons.person,
                                color: colorScheme.onPrimary,
                                size: 28,
                              )
                            : null,
                      ),
                      const SizedBox(width: 12),

                      // Welcome Text
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome back!',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onPrimary.withValues(
                                  alpha: 0.8,
                                ),
                              ),
                            ),
                            Text(
                              user?.displayName ?? 'User',
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Notification Icon
                      IconButton(
                        onPressed: () {
                          // Navigate to notifications page
                          context.push('/notifications');
                        },
                        icon: Badge(
                          label: const Text('3'),
                          child: Icon(
                            Icons.notifications_outlined,
                            color: colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWalletCard(
    ThemeData theme,
    ColorScheme colorScheme,
    AsyncValue walletAsync,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primaryContainer,
            colorScheme.secondaryContainer,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My India First Wallet',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(
                Icons.account_balance_wallet,
                color: colorScheme.onPrimaryContainer,
              ),
            ],
          ),
          const SizedBox(height: 12),

          walletAsync.when(
            data: (wallet) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '₹${wallet.balance.toStringAsFixed(2)}',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${wallet.balance.toStringAsFixed(0)} Project Coins',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onPrimaryContainer.withValues(
                      alpha: 0.8,
                    ),
                  ),
                ),
              ],
            ),
            loading: () => const CircularProgressIndicator(),
            error: (error, stack) => Text(
              'Error loading wallet',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Quick Wallet Actions
          Row(
            children: [
              Expanded(
                child: _buildWalletAction(
                  theme,
                  colorScheme,
                  Icons.add,
                  'Add Money',
                  () => context.push('/wallet/add-money'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildWalletAction(
                  theme,
                  colorScheme,
                  Icons.send,
                  'Send',
                  () => context.push('/wallet/send'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildWalletAction(
                  theme,
                  colorScheme,
                  Icons.qr_code_scanner,
                  'Scan QR',
                  () => context.push('/wallet/qr-scanner'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWalletAction(
    ThemeData theme,
    ColorScheme colorScheme,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            Icon(icon, color: colorScheme.onPrimaryContainer, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(ThemeData theme, ColorScheme colorScheme) {
    final actions = [
      {'icon': Icons.phone, 'label': 'Recharge', 'route': '/recharge'},
      {'icon': Icons.receipt, 'label': 'Bills', 'route': '/bills'},
      {'icon': Icons.train, 'label': 'Travel', 'route': '/travel'},
      {'icon': Icons.local_offer, 'label': 'Offers', 'route': '/offers'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: actions.map((action) {
            return _buildQuickActionItem(
              theme,
              colorScheme,
              action['icon'] as IconData,
              action['label'] as String,
              () => context.push(action['route'] as String),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickActionItem(
    ThemeData theme,
    ColorScheme colorScheme,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: colorScheme.onPrimaryContainer,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesGrid(ThemeData theme, ColorScheme colorScheme) {
    final services = [
      {
        'title': 'Bike Rental',
        'subtitle': 'Book bikes nearby',
        'icon': Icons.two_wheeler,
        'color': Colors.orange,
        'route': '/booking/bike',
      },
      {
        'title': 'Car Rental',
        'subtitle': 'Rent cars hourly',
        'icon': Icons.directions_car,
        'color': Colors.blue,
        'route': '/booking/car',
      },
      {
        'title': 'Spin & Earn',
        'subtitle': 'Win up to ₹500',
        'icon': Icons.casino,
        'color': Colors.purple,
        'route': '/games/spin-wheel',
      },
      {
        'title': 'Shop',
        'subtitle': 'Earn cashback',
        'icon': Icons.shopping_bag,
        'color': Colors.green,
        'route': '/shop',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Services',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: services.length,
          itemBuilder: (context, index) {
            final service = services[index];
            return _buildServiceCard(
              theme,
              colorScheme,
              service['title'] as String,
              service['subtitle'] as String,
              service['icon'] as IconData,
              service['color'] as Color,
              () => context.push(service['route'] as String),
            );
          },
        ),
      ],
    );
  }

  Widget _buildServiceCard(
    ThemeData theme,
    ColorScheme colorScheme,
    String title,
    String subtitle,
    IconData icon,
    Color iconColor,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: iconColor, size: 24),
              ),
              const Spacer(),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivities(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activities',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.push('/activities'),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Sample activities
        _buildActivityItem(
          theme,
          colorScheme,
          Icons.two_wheeler,
          'Bike Rental',
          'Completed • 2 hours ago',
          '₹45',
          Colors.green,
        ),
        _buildActivityItem(
          theme,
          colorScheme,
          Icons.casino,
          'Spin & Earn',
          'Won • 1 day ago',
          '+₹150',
          Colors.purple,
        ),
        _buildActivityItem(
          theme,
          colorScheme,
          Icons.shopping_bag,
          'Shopping Cashback',
          'Earned • 2 days ago',
          '+₹25',
          Colors.blue,
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    ThemeData theme,
    ColorScheme colorScheme,
    IconData icon,
    String title,
    String subtitle,
    String amount,
    Color iconColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          Text(
            amount,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: amount.startsWith('+')
                  ? Colors.green
                  : colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
