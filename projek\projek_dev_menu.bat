@echo off
title Projek Development Menu
color 0F

:menu
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    PROJEK DEVELOPMENT MENU                  ║
echo ║                  Complete Development Suite                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      MAIN OPTIONS                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  1. 🚀 Start Development (Hot Reload)                      ║
echo ║  2. 🔧 Setup Development Environment                        ║
echo ║  3. 🛠️  Fix Build Issues                                   ║
echo ║  4. 📱 Check Device Connection                              ║
echo ║  5. 📊 Performance Mode                                     ║
echo ║  6. 📦 Build APK                                           ║
echo ║  7. 🌐 Run on Web Browser                                  ║
echo ║  8. 🖥️  Run on Windows Desktop                             ║
echo ║  9. 📋 View Development Guide                               ║
echo ║  0. ❌ Exit                                                 ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📱 Target Device: V2130 (1397182984001HG)
echo 🎯 Target App: Projek User App (lib/main_user.dart)
echo.
set /p choice="Enter your choice (0-9): "

if "%choice%"=="1" goto start_dev
if "%choice%"=="2" goto setup_env
if "%choice%"=="3" goto fix_issues
if "%choice%"=="4" goto check_device
if "%choice%"=="5" goto performance
if "%choice%"=="6" goto build_apk
if "%choice%"=="7" goto run_web
if "%choice%"=="8" goto run_windows
if "%choice%"=="9" goto view_guide
if "%choice%"=="0" goto exit
goto invalid

:start_dev
echo.
echo 🚀 Starting development mode with hot reload...
call dev_user_app.bat
goto menu

:setup_env
echo.
echo 🔧 Setting up development environment...
call dev_setup.bat
goto menu

:fix_issues
echo.
echo 🛠️ Fixing build issues...
call fix_build_issues.bat
goto menu

:check_device
echo.
echo 📱 Checking device connection...
call check_device.bat
goto menu

:performance
echo.
echo 📊 Starting performance monitoring...
call dev_performance.bat
goto menu

:build_apk
echo.
echo 📦 Building APK...
call build_user_apk.bat
goto menu

:run_web
echo.
echo 🌐 Running on web browser...
flutter run --target lib/main_user.dart -d chrome --hot
pause
goto menu

:run_windows
echo.
echo 🖥️ Running on Windows desktop...
flutter run --target lib/main_user.dart -d windows --hot
pause
goto menu

:view_guide
echo.
echo 📋 Opening development guide...
start DEVELOPMENT_GUIDE.md
goto menu

:invalid
echo.
echo ❌ Invalid choice. Please enter a number between 0-9.
pause
goto menu

:exit
echo.
echo 👋 Thank you for using Projek Development Suite!
echo Happy coding! 🚀
pause
exit
