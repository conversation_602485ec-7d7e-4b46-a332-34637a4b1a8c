import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'firebase_options_seller.dart';
import 'core/config/app_config.dart';
import 'core/config/environment.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/theme_provider.dart';
import 'core/router/seller_router.dart';
import 'core/services/notification_service.dart';
import 'core/services/analytics_service.dart';
import 'core/database/hive_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set environment
  EnvironmentConfig.setEnvironment(Environment.development);

  // Set app type
  AppConfig.setAppType(AppType.seller);

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptionsSeller.currentPlatform,
  );

  // Initialize Hive
  await Hive.initFlutter();
  await HiveService.initialize();

  // Initialize seller-specific services
  await NotificationService.initialize();
  await AnalyticsService.initialize();

  runApp(const ProviderScope(child: ProjekSellerApp()));
}

class ProjekSellerApp extends ConsumerWidget {
  const ProjekSellerApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(sellerRouterProvider);
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp.router(
      title: AppConfig.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0), // Prevent text scaling
          ),
          child: child!,
        );
      },
    );
  }
}
