// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SellerProfileAdapter extends TypeAdapter<SellerProfile> {
  @override
  final int typeId = 103;

  @override
  SellerProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellerProfile(
      id: fields[0] as String,
      businessName: fields[1] as String,
      ownerName: fields[2] as String,
      email: fields[3] as String,
      phone: fields[4] as String,
      businessAddress: fields[5] as String?,
      businessDescription: fields[6] as String?,
      logoUrl: fields[7] as String?,
      bannerUrl: fields[8] as String?,
      status: fields[9] as SellerStatus,
      rating: fields[10] as double,
      reviewCount: fields[11] as int,
      categories: (fields[12] as List).cast<String>(),
      businessDocuments: (fields[13] as Map).cast<String, dynamic>(),
      createdAt: fields[14] as DateTime,
      updatedAt: fields[15] as DateTime,
      isVerified: fields[16] as bool,
      settings: (fields[17] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, SellerProfile obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessName)
      ..writeByte(2)
      ..write(obj.ownerName)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.phone)
      ..writeByte(5)
      ..write(obj.businessAddress)
      ..writeByte(6)
      ..write(obj.businessDescription)
      ..writeByte(7)
      ..write(obj.logoUrl)
      ..writeByte(8)
      ..write(obj.bannerUrl)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.rating)
      ..writeByte(11)
      ..write(obj.reviewCount)
      ..writeByte(12)
      ..write(obj.categories)
      ..writeByte(13)
      ..write(obj.businessDocuments)
      ..writeByte(14)
      ..write(obj.createdAt)
      ..writeByte(15)
      ..write(obj.updatedAt)
      ..writeByte(16)
      ..write(obj.isVerified)
      ..writeByte(17)
      ..write(obj.settings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellerProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InventoryItemAdapter extends TypeAdapter<InventoryItem> {
  @override
  final int typeId = 104;

  @override
  InventoryItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InventoryItem(
      id: fields[0] as String,
      productId: fields[1] as String,
      sellerId: fields[2] as String,
      sku: fields[3] as String,
      currentStock: fields[4] as int,
      minStockLevel: fields[5] as int,
      maxStockLevel: fields[6] as int,
      costPrice: fields[7] as double,
      sellingPrice: fields[8] as double,
      status: fields[9] as InventoryStatus,
      location: fields[10] as String?,
      lastRestocked: fields[11] as DateTime?,
      expiryDate: fields[12] as DateTime?,
      metadata: (fields[13] as Map).cast<String, dynamic>(),
      createdAt: fields[14] as DateTime,
      updatedAt: fields[15] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, InventoryItem obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.sellerId)
      ..writeByte(3)
      ..write(obj.sku)
      ..writeByte(4)
      ..write(obj.currentStock)
      ..writeByte(5)
      ..write(obj.minStockLevel)
      ..writeByte(6)
      ..write(obj.maxStockLevel)
      ..writeByte(7)
      ..write(obj.costPrice)
      ..writeByte(8)
      ..write(obj.sellingPrice)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.location)
      ..writeByte(11)
      ..write(obj.lastRestocked)
      ..writeByte(12)
      ..write(obj.expiryDate)
      ..writeByte(13)
      ..write(obj.metadata)
      ..writeByte(14)
      ..write(obj.createdAt)
      ..writeByte(15)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InventoryItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SellerOrderAdapter extends TypeAdapter<SellerOrder> {
  @override
  final int typeId = 105;

  @override
  SellerOrder read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellerOrder(
      id: fields[0] as String,
      sellerId: fields[1] as String,
      customerId: fields[2] as String,
      customerName: fields[3] as String,
      items: (fields[4] as List).cast<OrderItem>(),
      subtotal: fields[5] as double,
      tax: fields[6] as double,
      shippingFee: fields[7] as double,
      total: fields[8] as double,
      status: fields[9] as OrderStatus,
      shippingAddress: fields[10] as String?,
      trackingNumber: fields[11] as String?,
      orderDate: fields[12] as DateTime,
      shippedDate: fields[13] as DateTime?,
      deliveredDate: fields[14] as DateTime?,
      metadata: (fields[15] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, SellerOrder obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.sellerId)
      ..writeByte(2)
      ..write(obj.customerId)
      ..writeByte(3)
      ..write(obj.customerName)
      ..writeByte(4)
      ..write(obj.items)
      ..writeByte(5)
      ..write(obj.subtotal)
      ..writeByte(6)
      ..write(obj.tax)
      ..writeByte(7)
      ..write(obj.shippingFee)
      ..writeByte(8)
      ..write(obj.total)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.shippingAddress)
      ..writeByte(11)
      ..write(obj.trackingNumber)
      ..writeByte(12)
      ..write(obj.orderDate)
      ..writeByte(13)
      ..write(obj.shippedDate)
      ..writeByte(14)
      ..write(obj.deliveredDate)
      ..writeByte(15)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellerOrderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderItemAdapter extends TypeAdapter<OrderItem> {
  @override
  final int typeId = 106;

  @override
  OrderItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OrderItem(
      productId: fields[0] as String,
      productName: fields[1] as String,
      productImage: fields[2] as String?,
      quantity: fields[3] as int,
      unitPrice: fields[4] as double,
      totalPrice: fields[5] as double,
      productVariant: (fields[6] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, OrderItem obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.productId)
      ..writeByte(1)
      ..write(obj.productName)
      ..writeByte(2)
      ..write(obj.productImage)
      ..writeByte(3)
      ..write(obj.quantity)
      ..writeByte(4)
      ..write(obj.unitPrice)
      ..writeByte(5)
      ..write(obj.totalPrice)
      ..writeByte(6)
      ..write(obj.productVariant);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SellerAnalyticsAdapter extends TypeAdapter<SellerAnalytics> {
  @override
  final int typeId = 107;

  @override
  SellerAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellerAnalytics(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      totalSales: fields[2] as double,
      totalOrders: fields[3] as int,
      totalProducts: fields[4] as int,
      averageOrderValue: fields[5] as double,
      newCustomers: fields[6] as int,
      returningCustomers: fields[7] as int,
      categoryWiseSales: (fields[8] as Map).cast<String, double>(),
      topSellingProducts: (fields[9] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, SellerAnalytics obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.totalSales)
      ..writeByte(3)
      ..write(obj.totalOrders)
      ..writeByte(4)
      ..write(obj.totalProducts)
      ..writeByte(5)
      ..write(obj.averageOrderValue)
      ..writeByte(6)
      ..write(obj.newCustomers)
      ..writeByte(7)
      ..write(obj.returningCustomers)
      ..writeByte(8)
      ..write(obj.categoryWiseSales)
      ..writeByte(9)
      ..write(obj.topSellingProducts);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellerAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SellerStatusAdapter extends TypeAdapter<SellerStatus> {
  @override
  final int typeId = 100;

  @override
  SellerStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SellerStatus.pending;
      case 1:
        return SellerStatus.verified;
      case 2:
        return SellerStatus.suspended;
      case 3:
        return SellerStatus.rejected;
      default:
        return SellerStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, SellerStatus obj) {
    switch (obj) {
      case SellerStatus.pending:
        writer.writeByte(0);
        break;
      case SellerStatus.verified:
        writer.writeByte(1);
        break;
      case SellerStatus.suspended:
        writer.writeByte(2);
        break;
      case SellerStatus.rejected:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellerStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InventoryStatusAdapter extends TypeAdapter<InventoryStatus> {
  @override
  final int typeId = 101;

  @override
  InventoryStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return InventoryStatus.inStock;
      case 1:
        return InventoryStatus.lowStock;
      case 2:
        return InventoryStatus.outOfStock;
      case 3:
        return InventoryStatus.discontinued;
      default:
        return InventoryStatus.inStock;
    }
  }

  @override
  void write(BinaryWriter writer, InventoryStatus obj) {
    switch (obj) {
      case InventoryStatus.inStock:
        writer.writeByte(0);
        break;
      case InventoryStatus.lowStock:
        writer.writeByte(1);
        break;
      case InventoryStatus.outOfStock:
        writer.writeByte(2);
        break;
      case InventoryStatus.discontinued:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InventoryStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderStatusAdapter extends TypeAdapter<OrderStatus> {
  @override
  final int typeId = 102;

  @override
  OrderStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OrderStatus.pending;
      case 1:
        return OrderStatus.confirmed;
      case 2:
        return OrderStatus.processing;
      case 3:
        return OrderStatus.shipped;
      case 4:
        return OrderStatus.delivered;
      case 5:
        return OrderStatus.cancelled;
      case 6:
        return OrderStatus.returned;
      default:
        return OrderStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, OrderStatus obj) {
    switch (obj) {
      case OrderStatus.pending:
        writer.writeByte(0);
        break;
      case OrderStatus.confirmed:
        writer.writeByte(1);
        break;
      case OrderStatus.processing:
        writer.writeByte(2);
        break;
      case OrderStatus.shipped:
        writer.writeByte(3);
        break;
      case OrderStatus.delivered:
        writer.writeByte(4);
        break;
      case OrderStatus.cancelled:
        writer.writeByte(5);
        break;
      case OrderStatus.returned:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SellerProfile _$SellerProfileFromJson(Map<String, dynamic> json) =>
    SellerProfile(
      id: json['id'] as String,
      businessName: json['businessName'] as String,
      ownerName: json['ownerName'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      businessAddress: json['businessAddress'] as String?,
      businessDescription: json['businessDescription'] as String?,
      logoUrl: json['logoUrl'] as String?,
      bannerUrl: json['bannerUrl'] as String?,
      status: $enumDecodeNullable(_$SellerStatusEnumMap, json['status']) ??
          SellerStatus.pending,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      businessDocuments:
          json['businessDocuments'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isVerified: json['isVerified'] as bool? ?? false,
      settings: json['settings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SellerProfileToJson(SellerProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessName': instance.businessName,
      'ownerName': instance.ownerName,
      'email': instance.email,
      'phone': instance.phone,
      'businessAddress': instance.businessAddress,
      'businessDescription': instance.businessDescription,
      'logoUrl': instance.logoUrl,
      'bannerUrl': instance.bannerUrl,
      'status': _$SellerStatusEnumMap[instance.status]!,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'categories': instance.categories,
      'businessDocuments': instance.businessDocuments,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isVerified': instance.isVerified,
      'settings': instance.settings,
    };

const _$SellerStatusEnumMap = {
  SellerStatus.pending: 'pending',
  SellerStatus.verified: 'verified',
  SellerStatus.suspended: 'suspended',
  SellerStatus.rejected: 'rejected',
};

InventoryItem _$InventoryItemFromJson(Map<String, dynamic> json) =>
    InventoryItem(
      id: json['id'] as String,
      productId: json['productId'] as String,
      sellerId: json['sellerId'] as String,
      sku: json['sku'] as String,
      currentStock: (json['currentStock'] as num).toInt(),
      minStockLevel: (json['minStockLevel'] as num?)?.toInt() ?? 5,
      maxStockLevel: (json['maxStockLevel'] as num?)?.toInt() ?? 100,
      costPrice: (json['costPrice'] as num).toDouble(),
      sellingPrice: (json['sellingPrice'] as num).toDouble(),
      status: $enumDecodeNullable(_$InventoryStatusEnumMap, json['status']) ??
          InventoryStatus.inStock,
      location: json['location'] as String?,
      lastRestocked: json['lastRestocked'] == null
          ? null
          : DateTime.parse(json['lastRestocked'] as String),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$InventoryItemToJson(InventoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'sellerId': instance.sellerId,
      'sku': instance.sku,
      'currentStock': instance.currentStock,
      'minStockLevel': instance.minStockLevel,
      'maxStockLevel': instance.maxStockLevel,
      'costPrice': instance.costPrice,
      'sellingPrice': instance.sellingPrice,
      'status': _$InventoryStatusEnumMap[instance.status]!,
      'location': instance.location,
      'lastRestocked': instance.lastRestocked?.toIso8601String(),
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$InventoryStatusEnumMap = {
  InventoryStatus.inStock: 'inStock',
  InventoryStatus.lowStock: 'lowStock',
  InventoryStatus.outOfStock: 'outOfStock',
  InventoryStatus.discontinued: 'discontinued',
};

SellerOrder _$SellerOrderFromJson(Map<String, dynamic> json) => SellerOrder(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
      shippingFee: (json['shippingFee'] as num?)?.toDouble() ?? 0.0,
      total: (json['total'] as num).toDouble(),
      status: $enumDecodeNullable(_$OrderStatusEnumMap, json['status']) ??
          OrderStatus.pending,
      shippingAddress: json['shippingAddress'] as String?,
      trackingNumber: json['trackingNumber'] as String?,
      orderDate: DateTime.parse(json['orderDate'] as String),
      shippedDate: json['shippedDate'] == null
          ? null
          : DateTime.parse(json['shippedDate'] as String),
      deliveredDate: json['deliveredDate'] == null
          ? null
          : DateTime.parse(json['deliveredDate'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SellerOrderToJson(SellerOrder instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'items': instance.items,
      'subtotal': instance.subtotal,
      'tax': instance.tax,
      'shippingFee': instance.shippingFee,
      'total': instance.total,
      'status': _$OrderStatusEnumMap[instance.status]!,
      'shippingAddress': instance.shippingAddress,
      'trackingNumber': instance.trackingNumber,
      'orderDate': instance.orderDate.toIso8601String(),
      'shippedDate': instance.shippedDate?.toIso8601String(),
      'deliveredDate': instance.deliveredDate?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.processing: 'processing',
  OrderStatus.shipped: 'shipped',
  OrderStatus.delivered: 'delivered',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.returned: 'returned',
};

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => OrderItem(
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      productImage: json['productImage'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
      productVariant:
          json['productVariant'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$OrderItemToJson(OrderItem instance) => <String, dynamic>{
      'productId': instance.productId,
      'productName': instance.productName,
      'productImage': instance.productImage,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'totalPrice': instance.totalPrice,
      'productVariant': instance.productVariant,
    };

SellerAnalytics _$SellerAnalyticsFromJson(Map<String, dynamic> json) =>
    SellerAnalytics(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      totalSales: (json['totalSales'] as num?)?.toDouble() ?? 0.0,
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      totalProducts: (json['totalProducts'] as num?)?.toInt() ?? 0,
      averageOrderValue: (json['averageOrderValue'] as num?)?.toDouble() ?? 0.0,
      newCustomers: (json['newCustomers'] as num?)?.toInt() ?? 0,
      returningCustomers: (json['returningCustomers'] as num?)?.toInt() ?? 0,
      categoryWiseSales:
          (json['categoryWiseSales'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toDouble()),
              ) ??
              const {},
      topSellingProducts:
          (json['topSellingProducts'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toInt()),
              ) ??
              const {},
    );

Map<String, dynamic> _$SellerAnalyticsToJson(SellerAnalytics instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'totalSales': instance.totalSales,
      'totalOrders': instance.totalOrders,
      'totalProducts': instance.totalProducts,
      'averageOrderValue': instance.averageOrderValue,
      'newCustomers': instance.newCustomers,
      'returningCustomers': instance.returningCustomers,
      'categoryWiseSales': instance.categoryWiseSales,
      'topSellingProducts': instance.topSellingProducts,
    };
