# 🐛 PROJEK APP CRASH DEBUGGING GUIDE

## 🔍 **MANUAL DEBUGGING STEPS**

### **Step 1: Check Flutter Logs**
```bash
# Run app with verbose logging
flutter run --verbose

# Or check device logs
flutter logs

# For Android specifically
adb logcat | grep flutter
```

### **Step 2: Check Asset Files**
Verify these assets exist in your project:

**Required Asset Directories:**
- `assets/icons/shared/`
- `assets/icons/rider/`
- `assets/icons/seller/`
- `assets/icons/user/`
- `assets/images/services/teaching/`
- `assets/images/services/repairs/`
- `assets/images/services/plumber/`
- `assets/images/services/laundry/`
- `assets/images/services/medicine/`
- `assets/images/services/cleaning/`
- `assets/images/services/beautyService/`
- `assets/images/common/`
- `assets/images/categories/groceries/`
- `assets/images/categories/food/`
- `assets/images/categories/electronics/`
- `assets/images/categories/clothes/`
- `assets/navigation/`
- `assets/payment/`
- `assets/splash_screen/`

**Check Command:**
```bash
# Check if assets exist
ls -la assets/
ls -la assets/icons/
ls -la assets/images/
```

### **Step 3: Verify Firebase Configuration**

**Check Firebase Files:**
- `android/app/google-services.json` (for User app)
- `lib/firebase_options.dart`

**Verify Firebase Project IDs:**
- User: `projek-user`
- Rider: `projek-rider-575d2`
- Seller: `projek-seller`

### **Step 4: Check Dependencies**
```bash
# Clean and get dependencies
flutter clean
flutter pub get

# Check for dependency conflicts
flutter pub deps
```

### **Step 5: Test Individual Components**

**Test Firebase Initialization:**
```dart
// Add this to main.dart for testing
void testFirebase() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptionsUser.currentPlatform,
    );
    print("✅ Firebase initialized successfully");
  } catch (e) {
    print("❌ Firebase error: $e");
  }
}
```

**Test Router:**
```dart
// Test if router is working
void testRouter() {
  try {
    final router = GoRouter(
      initialLocation: '/',
      routes: [
        GoRoute(
          path: '/',
          builder: (context, state) => const Scaffold(
            body: Center(child: Text('Test Route')),
          ),
        ),
      ],
    );
    print("✅ Router created successfully");
  } catch (e) {
    print("❌ Router error: $e");
  }
}
```

### **Step 6: Common Crash Causes & Solutions**

**1. Missing Assets:**
```yaml
# Add to pubspec.yaml
flutter:
  assets:
    - assets/
    - assets/icons/
    - assets/images/
```

**2. Firebase Configuration:**
```bash
# Re-download google-services.json from Firebase Console
# Place in android/app/google-services.json
```

**3. Dependency Conflicts:**
```bash
# Update dependencies
flutter pub upgrade
```

**4. Build Issues:**
```bash
# Clean everything
flutter clean
cd android && ./gradlew clean
cd .. && flutter pub get
```

### **Step 7: Enable Crash Reporting**

Add this to your main.dart:
```dart
void main() async {
  // Enable crash reporting
  FlutterError.onError = (FlutterErrorDetails details) {
    print("🔥 Flutter Error: ${details.exception}");
    print("📍 Stack: ${details.stack}");
  };
  
  // Your existing code...
}
```

### **Step 8: Test on Different Devices**

**Vivo V23 5G Specific Issues:**
- Android version: Check if targeting correct API level
- RAM: Ensure app doesn't use too much memory
- Storage: Check available space
- Permissions: Verify all required permissions

### **Step 9: Gradual Feature Testing**

**Test Minimal App:**
```dart
// Create minimal test app
void main() {
  runApp(MaterialApp(
    home: Scaffold(
      body: Center(
        child: Text('Minimal Test App'),
      ),
    ),
  ));
}
```

**Add Features Gradually:**
1. Basic MaterialApp ✓
2. Add Firebase ✓
3. Add Router ✓
4. Add Riverpod ✓
5. Add Assets ✓
6. Add Full Features ✓

### **Step 10: Performance Monitoring**

```dart
// Add performance monitoring
import 'dart:developer' as developer;

void main() async {
  developer.Timeline.startSync('App Initialization');
  
  // Your initialization code
  
  developer.Timeline.finishSync();
}
```

## 🚨 **EMERGENCY FIXES**

### **Quick Fix 1: Minimal Working App**
Replace main.dart content with:
```dart
import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Projek Test',
      home: Scaffold(
        appBar: AppBar(title: Text('Projek Working')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, size: 100, color: Colors.green),
              Text('App is working!', style: TextStyle(fontSize: 24)),
            ],
          ),
        ),
      ),
    );
  }
}
```

### **Quick Fix 2: Disable Problematic Features**
Comment out in main.dart:
- Firebase initialization
- Complex routing
- Asset loading
- Heavy dependencies

## 📱 **DEVICE-SPECIFIC DEBUGGING (Vivo V23 5G)**

### **Check Device Compatibility:**
```bash
# Check device info
adb shell getprop ro.build.version.release  # Android version
adb shell getprop ro.product.cpu.abi        # CPU architecture
adb shell dumpsys meminfo                   # Memory info
```

### **Vivo-Specific Settings:**
1. **Developer Options:** Enable USB Debugging
2. **Battery Optimization:** Disable for your app
3. **App Permissions:** Grant all required permissions
4. **Storage:** Ensure sufficient space (>1GB)

## 🔧 **FINAL CHECKLIST**

- [ ] Assets exist and are properly referenced
- [ ] Firebase is configured correctly
- [ ] Dependencies are compatible
- [ ] No syntax errors in code
- [ ] Sufficient device storage
- [ ] Correct Android API level
- [ ] All permissions granted
- [ ] Network connectivity available
- [ ] Device developer options enabled

## 📞 **NEXT STEPS**

1. Run the enhanced main.dart with error handling
2. Check console logs for specific error messages
3. Test minimal app first, then add features gradually
4. Use the debugging commands above
5. Report specific error messages for targeted help

The enhanced main.dart now includes:
- ✅ Comprehensive error handling
- ✅ Step-by-step initialization logging
- ✅ Fallback error screens
- ✅ Detailed error reporting
- ✅ Recovery options
