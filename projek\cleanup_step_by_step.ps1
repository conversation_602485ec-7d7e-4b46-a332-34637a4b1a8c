# PowerShell version of cleanup script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PROJEK PROJECT CLEANUP - STEP BY STEP" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "This script will help you clean up duplicate files safely." -ForegroundColor Yellow
Write-Host "Each step will ask for confirmation before proceeding." -ForegroundColor Yellow
Write-Host ""

function Show-Menu {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "CLEANUP MENU" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Backup important files" -ForegroundColor White
    Write-Host "2. Remove duplicate main files (7 files)" -ForegroundColor White
    Write-Host "3. Remove duplicate auth files (5 files)" -ForegroundColor White
    Write-Host "4. Remove duplicate config files (4 files)" -ForegroundColor White
    Write-Host "5. Remove duplicate build scripts (12+ files)" -ForegroundColor White
    Write-Host "6. Remove redundant documentation (40+ files)" -ForegroundColor White
    Write-Host "7. Test app after cleanup" -ForegroundColor White
    Write-Host "8. Complete cleanup (all steps)" -ForegroundColor White
    Write-Host "9. Exit" -ForegroundColor White
    Write-Host ""
}

function Backup-Files {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "STEP 1: CREATING BACKUPS" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green

    # Create backup directories
    if (!(Test-Path "backup")) { New-Item -ItemType Directory -Path "backup" }
    if (!(Test-Path "backup\main_files")) { New-Item -ItemType Directory -Path "backup\main_files" }
    if (!(Test-Path "backup\auth_files")) { New-Item -ItemType Directory -Path "backup\auth_files" }
    if (!(Test-Path "backup\config_files")) { New-Item -ItemType Directory -Path "backup\config_files" }
    if (!(Test-Path "backup\scripts")) { New-Item -ItemType Directory -Path "backup\scripts" }

    Write-Host "Creating backups of important files..." -ForegroundColor Yellow

    # Backup main files
    if (Test-Path "lib\main.dart") { Copy-Item "lib\main.dart" "backup\main_files\" }
    if (Test-Path "lib\main_fixed.dart") { Copy-Item "lib\main_fixed.dart" "backup\main_files\" }
    if (Test-Path "lib\main_user.dart") { Copy-Item "lib\main_user.dart" "backup\main_files\" }
    if (Test-Path "lib\app_fixed.dart") { Copy-Item "lib\app_fixed.dart" "backup\main_files\" }

    # Backup config files
    Get-ChildItem "lib\firebase_options*.dart" -ErrorAction SilentlyContinue | Copy-Item -Destination "backup\config_files\"
    Get-ChildItem "build_*.bat" -ErrorAction SilentlyContinue | Copy-Item -Destination "backup\scripts\"

    Write-Host "✅ Backup completed in backup\ folder" -ForegroundColor Green
    Write-Host ""
    Read-Host "Press Enter to continue"
}

function Remove-DuplicateMainFiles {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "STEP 2: REMOVING DUPLICATE MAIN FILES" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red

    Write-Host ""
    Write-Host "Files to be removed:" -ForegroundColor Yellow
    Write-Host "- main_debug.dart (debug version)" -ForegroundColor White
    Write-Host "- main_fixed.dart (fixed version)" -ForegroundColor White
    Write-Host "- main_simple.dart (simple test version)" -ForegroundColor White
    Write-Host "- main_marketplace.dart (marketplace only)" -ForegroundColor White
    Write-Host "- main_rider.dart (rider app)" -ForegroundColor White
    Write-Host "- main_seller.dart (seller app)" -ForegroundColor White
    Write-Host "- app_fixed.dart (fixed app wrapper)" -ForegroundColor White
    Write-Host ""
    Write-Host "✅ KEEPING: main_user.dart (most complete version)" -ForegroundColor Green
    Write-Host ""

    $confirm = Read-Host "Remove these duplicate main files? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Write-Host "Removing duplicate main files..." -ForegroundColor Yellow

        $filesToRemove = @(
            "lib\main_debug.dart",
            "lib\main_fixed.dart", 
            "lib\main_simple.dart",
            "lib\main_marketplace.dart",
            "lib\main_rider.dart",
            "lib\main_seller.dart",
            "lib\app_fixed.dart",
            "test_app.dart"
        )

        foreach ($file in $filesToRemove) {
            if (Test-Path $file) {
                Remove-Item $file -Force
                Write-Host "✅ Removed $file" -ForegroundColor Green
            }
        }

        Write-Host ""
        Write-Host "✅ Duplicate main files removed successfully!" -ForegroundColor Green
    }
    Read-Host "Press Enter to continue"
}

function Remove-DuplicateAuthFiles {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "STEP 3: REMOVING DUPLICATE AUTH FILES" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red

    Write-Host ""
    Write-Host "Files to be removed:" -ForegroundColor Yellow
    Write-Host "- auth_page_fixed.dart (fixed auth page)" -ForegroundColor White
    Write-Host "- modern_login_page.dart (modern login)" -ForegroundColor White
    Write-Host "- modern_register_page.dart (modern register)" -ForegroundColor White
    Write-Host "- main_app_fixed.dart (fixed main app)" -ForegroundColor White
    Write-Host ""
    Write-Host "✅ KEEPING: unified_auth_page.dart (comprehensive auth)" -ForegroundColor Green
    Write-Host ""

    $confirm = Read-Host "Remove these duplicate auth files? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Write-Host "Removing duplicate authentication files..." -ForegroundColor Yellow

        $filesToRemove = @(
            "lib\features\auth\presentation\pages\auth_page_fixed.dart",
            "lib\features\auth\presentation\pages\modern_login_page.dart",
            "lib\features\auth\presentation\pages\modern_register_page.dart",
            "lib\features\main\presentation\pages\main_app_fixed.dart"
        )

        foreach ($file in $filesToRemove) {
            if (Test-Path $file) {
                Remove-Item $file -Force
                Write-Host "✅ Removed $file" -ForegroundColor Green
            }
        }

        Write-Host ""
        Write-Host "✅ Duplicate auth files removed successfully!" -ForegroundColor Green
    }
    Read-Host "Press Enter to continue"
}

function Remove-DuplicateConfigFiles {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "STEP 4: REMOVING DUPLICATE CONFIG FILES" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red

    Write-Host ""
    Write-Host "Files to be removed:" -ForegroundColor Yellow
    Write-Host "- firebase_options_user.dart (user-specific)" -ForegroundColor White
    Write-Host "- firebase_options_rider.dart (rider-specific)" -ForegroundColor White
    Write-Host "- firebase_options_seller.dart (seller-specific)" -ForegroundColor White
    Write-Host "- firebase_auto.json (auto-generated)" -ForegroundColor White
    Write-Host ""
    Write-Host "✅ KEEPING: firebase_options.dart (main config)" -ForegroundColor Green
    Write-Host ""

    $confirm = Read-Host "Remove these duplicate config files? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Write-Host "Removing duplicate config files..." -ForegroundColor Yellow

        $filesToRemove = @(
            "lib\firebase_options_user.dart",
            "lib\firebase_options_rider.dart",
            "lib\firebase_options_seller.dart",
            "firebase_auto.json",
            "firebase_config_auto.json"
        )

        foreach ($file in $filesToRemove) {
            if (Test-Path $file) {
                Remove-Item $file -Force
                Write-Host "✅ Removed $file" -ForegroundColor Green
            }
        }

        Write-Host ""
        Write-Host "✅ Duplicate config files removed successfully!" -ForegroundColor Green
    }
    Read-Host "Press Enter to continue"
}

function Test-AppBuild {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Blue
    Write-Host "STEP 7: TESTING APP AFTER CLEANUP" -ForegroundColor Blue
    Write-Host "========================================" -ForegroundColor Blue

    Write-Host "Testing if the app builds correctly..." -ForegroundColor Yellow
    Write-Host ""

    Write-Host "Running flutter clean..." -ForegroundColor Cyan
    flutter clean

    Write-Host "Running flutter pub get..." -ForegroundColor Cyan
    flutter pub get

    Write-Host "Testing build with main_user.dart..." -ForegroundColor Cyan
    $buildResult = flutter build apk --debug --flavor user -t lib/main_user.dart

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ SUCCESS! App builds correctly after cleanup." -ForegroundColor Green
        Write-Host "Your project is now clean and functional." -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "❌ BUILD FAILED! There might be missing dependencies." -ForegroundColor Red
        Write-Host "Check the error messages above." -ForegroundColor Red
    }

    Write-Host ""
    Read-Host "Press Enter to continue"
}

# Main menu loop
do {
    Show-Menu
    $choice = Read-Host "Choose an option (1-9)"

    switch ($choice) {
        "1" { Backup-Files }
        "2" { Remove-DuplicateMainFiles }
        "3" { Remove-DuplicateAuthFiles }
        "4" { Remove-DuplicateConfigFiles }
        "5" { 
            Write-Host "Build scripts cleanup not implemented in PowerShell version yet." -ForegroundColor Yellow
            Read-Host "Press Enter to continue"
        }
        "6" { 
            Write-Host "Documentation cleanup not implemented in PowerShell version yet." -ForegroundColor Yellow
            Read-Host "Press Enter to continue"
        }
        "7" { Test-AppBuild }
        "8" { 
            Backup-Files
            Remove-DuplicateMainFiles
            Remove-DuplicateAuthFiles
            Remove-DuplicateConfigFiles
            Test-AppBuild
        }
        "9" { 
            Write-Host "Cleanup script exited." -ForegroundColor Yellow
            exit
        }
        default { 
            Write-Host "Invalid choice! Please select 1-9." -ForegroundColor Red
            Read-Host "Press Enter to continue"
        }
    }
} while ($choice -ne "9")
