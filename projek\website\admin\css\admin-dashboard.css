/* Admin Dashboard Styles */

.admin-body {
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
  background: #f8f9fa;
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #2c2c54 0%, #40407a 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-header img {
  height: 40px;
  margin-bottom: 1rem;
  filter: brightness(0) invert(1);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-item a {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(5px);
  transition: all 0.3s ease;
}

.nav-item.active a {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-right: 3px solid #ff9933;
  transform: translateX(5px);
}

/* Enhanced navigation visibility */
.nav-item a {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.nav-item a:active {
  transform: translateX(3px);
  background: rgba(255, 255, 255, 0.2);
}

/* Profile management specific styling */
.nav-item a[data-section="riders"],
.nav-item a[data-section="sellers"],
.nav-item a[data-section="service-providers"],
.nav-item a[data-section="verification"] {
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-item a[data-section="riders"]:hover {
  border-left-color: #667eea;
}

.nav-item a[data-section="sellers"]:hover {
  border-left-color: #28a745;
}

.nav-item a[data-section="service-providers"]:hover {
  border-left-color: #ffc107;
}

.nav-item a[data-section="verification"]:hover {
  border-left-color: #dc3545;
}

/* Enhanced sidebar navigation */
.nav-item {
  margin-bottom: 0.5rem;
}

.nav-item a {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 8px;
  margin: 0 1rem;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
  min-height: 50px;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.nav-item a:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-item.active a {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-right: 4px solid #ff9933;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.nav-item a .material-icons {
  margin-right: 1rem;
  font-size: 1.4rem;
  min-width: 24px;
}

.nav-item a span:not(.material-icons):not(.badge) {
  flex: 1;
  font-size: 0.95rem;
}

.nav-item .badge {
  background: #ff9933;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: auto;
}

.nav-item .badge.pending {
  background: #ffc107;
  color: #2c2c54;
  animation: pulse 2s infinite;
}

/* Click feedback */
.nav-item a:active {
  transform: translateX(4px) scale(0.98);
  transition: all 0.1s ease;
  background: rgba(255, 255, 255, 0.25) !important;
}

/* Focus states for accessibility */
.nav-item a:focus {
  outline: 2px solid #ff9933;
  outline-offset: 2px;
}

/* Ensure clickability */
.nav-item a * {
  pointer-events: none;
}

.nav-item a {
  pointer-events: auto;
}

/* Enhanced active state */
.nav-item.active a {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-right: 4px solid #ff9933;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Sidebar navigation debugging */
.nav-item a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: auto;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .nav-item a {
    padding: 0.8rem 1rem;
    margin: 0 0.5rem;
  }

  .nav-item a .material-icons {
    margin-right: 0.8rem;
    font-size: 1.2rem;
  }

  .nav-item a span:not(.material-icons):not(.badge) {
    font-size: 0.9rem;
  }
}

.nav-item .material-icons {
  font-size: 1.3rem;
}

.nav-item .badge {
  background: #ff9933;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  margin-left: auto;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.profile-avatar .material-icons {
  font-size: 2.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.profile-info h4 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.profile-info p {
  margin: 0;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.8rem;
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #ff6b7a;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
}

.logout-btn:hover {
  background: rgba(220, 53, 69, 0.3);
  color: white;
}

/* Main Content */
.admin-main {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header */
.admin-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #2c2c54;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.sidebar-toggle:hover {
  background: #f8f9fa;
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #2c2c54;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  padding: 0.8rem 1rem;
  border-radius: 25px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box .material-icons {
  color: #6c757d;
  font-size: 1.2rem;
}

.search-box input {
  border: none;
  background: none;
  outline: none;
  font-family: 'Poppins', sans-serif;
  width: 250px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-btn {
  position: relative;
  background: none;
  border: none;
  padding: 0.8rem;
  border-radius: 50%;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #2c2c54;
}

.notification-badge {
  position: absolute;
  top: 0.3rem;
  right: 0.3rem;
  background: #dc3545;
  color: white;
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 1rem;
  text-align: center;
}

.admin-avatar .material-icons {
  font-size: 2rem;
  color: #667eea;
}

/* Content Area */
.admin-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card.revenue .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card.users .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card.riders .stat-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c2c54;
}

.stat-content p {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.stat-change.positive {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.stat-change.negative {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Charts Row */
.charts-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container.small {
  max-width: 320px;
  max-height: 220px;
  padding: 1.5rem;
}

.chart-container.small .chart-header {
  margin-bottom: 1rem;
}

.chart-container.small .chart-header h3 {
  font-size: 1rem;
}

.chart-container.small canvas {
  max-height: 120px !important;
  height: 120px !important;
}

.chart-container.large {
  min-height: 400px;
}

.chart-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.chart-header h3 {
  margin: 0;
  color: #2c2c54;
  font-size: 1.2rem;
  font-weight: 600;
}

.chart-controls select {
  padding: 0.5rem 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-family: 'Poppins', sans-serif;
  color: #2c2c54;
}

.chart-legend {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #6c757d;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.food {
  background: #ff9933;
}

.legend-color.grocery {
  background: #138808;
}

.legend-color.services {
  background: #667eea;
}

/* GPS Tracking Map */
.gps-map-container {
  position: relative;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.rider-map {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.rider-marker {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
}

.rider-marker.active {
  background: #28a745;
  box-shadow: 0 0 20px rgba(40, 167, 69, 0.6);
}

.rider-marker.busy {
  background: #ffc107;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
}

.rider-marker.offline {
  background: #dc3545;
  box-shadow: 0 0 20px rgba(220, 53, 69, 0.6);
}

.rider-marker:hover {
  transform: scale(1.2);
}

.rider-tooltip {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  font-size: 0.7rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.rider-marker:hover .rider-tooltip {
  opacity: 1;
}

.map-legend {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.legend-dot.active {
  background: #28a745;
}

.legend-dot.busy {
  background: #ffc107;
}

.legend-dot.offline {
  background: #dc3545;
}

.btn-small {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: #5a6fd8;
}

.live-text {
  font-size: 0.8rem;
  color: #28a745;
  font-weight: 600;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Booking Reports Section */
.booking-reports-section {
  margin-bottom: 2rem;
}

.booking-reports-section h3 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.booking-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.booking-stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.booking-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.booking-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.booking-stat-card.confirmed .booking-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.booking-stat-card.pending .booking-icon {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.booking-stat-card.cancelled .booking-icon {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.booking-stat-card.total .booking-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.booking-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c2c54;
}

.booking-content p {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.booking-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.booking-change.positive {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.booking-change.negative {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.booking-change.neutral {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

/* Booking Table */
.booking-table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.table-header h4 {
  margin: 0;
  color: #2c2c54;
  font-size: 1.1rem;
  font-weight: 600;
}

.table-filters {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.table-filters select {
  padding: 0.5rem 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-family: 'Poppins', sans-serif;
  color: #2c2c54;
}

.btn-export {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-export:hover {
  background: #218838;
}

.booking-table {
  overflow-x: auto;
}

.booking-table table {
  width: 100%;
  border-collapse: collapse;
}

.booking-table th,
.booking-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.booking-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c2c54;
  font-size: 0.9rem;
}

.booking-table td {
  color: #6c757d;
  font-size: 0.9rem;
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.confirmed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-badge.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-badge.cancelled {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.action-btn-small {
  background: none;
  border: none;
  padding: 0.3rem;
  border-radius: 4px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
}

.action-btn-small:hover {
  background: #f8f9fa;
  color: #2c2c54;
}

.action-btn-small.view:hover {
  color: #667eea;
}

.action-btn-small.edit:hover {
  color: #28a745;
}

/* Profile Management Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: #2c2c54;
  font-size: 1.5rem;
  font-weight: 700;
}

.section-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f8f9fa;
  color: #2c2c54;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

/* Profile Stats Grid */
.profile-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.profile-stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.profile-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.profile-stat-card.active .stat-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.profile-stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.profile-stat-card.rejected .stat-icon {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.profile-stat-card.total .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.profile-stat-card.verified .stat-icon {
  background: linear-gradient(135deg, #28a745, #138808);
}

/* Profile Table */
.profile-table-container, .verification-queue-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.profile-table, .verification-table {
  overflow-x: auto;
}

.profile-table table, .verification-table table {
  width: 100%;
  border-collapse: collapse;
}

.profile-table th, .profile-table td,
.verification-table th, .verification-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.profile-table th, .verification-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c2c54;
  font-size: 0.9rem;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e9ecef;
}

.profile-info strong {
  color: #2c2c54;
  font-size: 0.9rem;
}

.profile-info small {
  color: #6c757d;
  font-size: 0.7rem;
}

/* KYC and Document Badges */
.kyc-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
}

.kyc-badge.verified {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.kyc-badge.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.kyc-badge.rejected {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Category Badges */
.category-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.category-badge.restaurant {
  background: rgba(255, 153, 51, 0.1);
  color: #ff9933;
}

.category-badge.grocery {
  background: rgba(19, 136, 8, 0.1);
  color: #138808;
}

.category-badge.healthcare {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.category-badge.education {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.category-badge.home-services {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

/* Document Status */
.document-status {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.doc-item {
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.doc-item.verified {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.doc-item.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.doc-item.rejected {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Rating Display */
.rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
}

.rating span {
  font-weight: 700;
  color: #2c2c54;
}

.stars {
  color: #ffc107;
  font-size: 0.8rem;
}

/* Search Input */
.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-family: 'Poppins', sans-serif;
  color: #2c2c54;
  width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Verification Specific Styles */
.document-type {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.doc-icon {
  font-size: 1.5rem;
}

.priority-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.priority-badge.medium {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.priority-badge.low {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.priority-high {
  border-left: 4px solid #dc3545;
}

.priority-medium {
  border-left: 4px solid #ffc107;
}

.priority-low {
  border-left: 4px solid #28a745;
}

.action-btn-small.approve:hover {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.action-btn-small.reject:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.action-btn-small.verify:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.stat-change.urgent {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  animation: pulse 2s infinite;
}

.badge.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  animation: pulse 2s infinite;
}

/* Navigation Badge Styles */
.nav-item .badge.pending {
  background: #ffc107;
  color: white;
  animation: pulse 2s infinite;
}

/* Cancellation History Styles */
.cancellation-history-section {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 16px;
  border: 1px solid #e9ecef;
}

.cancellation-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.cancel-stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.cancel-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.cancel-stat-card.total .cancel-icon {
  background: linear-gradient(135deg, #dc3545, #c82333);
}

.cancel-stat-card.customer .cancel-icon {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
}

.cancel-stat-card.restaurant .cancel-icon {
  background: linear-gradient(135deg, #6f42c1, #5a2d91);
}

.cancel-stat-card.system .cancel-icon {
  background: linear-gradient(135deg, #6c757d, #545b62);
}

.cancel-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.cancel-content h4 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c2c54;
  margin: 0;
}

.cancel-content p {
  color: #6c757d;
  margin: 0.5rem 0;
  font-weight: 500;
}

.cancel-change {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.cancel-change.negative {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.cancel-percentage {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Cancellation Chart */
.cancellation-chart-container {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.chart-content {
  margin-top: 1rem;
}

/* Cancellation Table */
.cancellation-table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  margin-bottom: 2rem;
}

.cancellation-table {
  overflow-x: auto;
}

.cancellation-table table {
  width: 100%;
  border-collapse: collapse;
}

.cancellation-table th,
.cancellation-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.cancellation-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c2c54;
  font-size: 0.9rem;
}

/* Cancel Reason Badges */
.cancel-reason {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.cancel-reason.customer {
  background: rgba(253, 126, 20, 0.1);
  color: #fd7e14;
}

.cancel-reason.restaurant {
  background: rgba(111, 66, 193, 0.1);
  color: #6f42c1;
}

.cancel-reason.payment {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.cancel-reason.rider {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* Refund Status Badges */
.refund-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.refund-badge.completed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.refund-badge.processing {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.refund-badge.failed {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Cancellation Insights */
.cancellation-insights {
  margin-top: 2rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.insight-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.insight-card.high-priority {
  border-left: 4px solid #dc3545;
}

.insight-card.medium-priority {
  border-left: 4px solid #ffc107;
}

.insight-card.low-priority {
  border-left: 4px solid #28a745;
}

.insight-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.insight-card.high-priority .insight-icon {
  background: linear-gradient(135deg, #dc3545, #c82333);
}

.insight-card.medium-priority .insight-icon {
  background: linear-gradient(135deg, #ffc107, #e0a800);
}

.insight-card.low-priority .insight-icon {
  background: linear-gradient(135deg, #28a745, #1e7e34);
}

.insight-content h5 {
  color: #2c2c54;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.insight-content p {
  color: #6c757d;
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.insight-action {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.insight-action:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

/* Real-time Metrics */
.realtime-section {
  margin-bottom: 2rem;
}

.realtime-section h3 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-header h4 {
  margin: 0;
  color: #2c2c54;
  font-size: 0.9rem;
  font-weight: 500;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #28a745;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c2c54;
  margin-bottom: 1rem;
}

.metric-chart {
  height: 60px;
}

/* Geographic Section */
.geographic-section {
  margin-bottom: 2rem;
}

.geographic-section h3 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.geo-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.geo-map {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.map-placeholder {
  height: 400px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #dee2e6;
}

.map-content {
  text-align: center;
  color: #6c757d;
}

.map-content .material-icons {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.map-content h4 {
  color: #2c2c54;
  margin-bottom: 1rem;
}

.coverage-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
}

.coverage-item {
  text-align: center;
}

.coverage-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.coverage-label {
  font-size: 0.8rem;
  color: #6c757d;
}

.geo-stats {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.geo-stats h4 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.city-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.city-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.city-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.city-info {
  display: flex;
  flex-direction: column;
}

.city-name {
  font-weight: 600;
  color: #2c2c54;
  margin-bottom: 0.25rem;
}

.city-orders {
  font-size: 0.8rem;
  color: #6c757d;
}

.city-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-bar {
  width: 80px;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #667eea;
  min-width: 30px;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 2rem;
}

.quick-actions h3 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
  color: #2c2c54;
}

.action-card:hover {
  background: #667eea;
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.action-card .material-icons {
  font-size: 2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-row {
    grid-template-columns: 1fr;
  }

  .geo-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }

  .admin-sidebar.open {
    transform: translateX(0);
  }

  .admin-main {
    margin-left: 0;
  }

  .sidebar-toggle {
    display: block;
  }

  .search-box {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .coverage-stats {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Gaming and Wallet Sections */
.metric-card.gaming {
  border-left: 4px solid #ff9933;
  background: linear-gradient(135deg, rgba(255, 153, 51, 0.05), white);
}

.metric-card.wallet {
  border-left: 4px solid #146eb4;
  background: linear-gradient(135deg, rgba(20, 110, 180, 0.05), white);
}

.control-panel {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.control-panel h3 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.3rem;
}

.control-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-btn.pause {
  background: linear-gradient(135deg, #ffc107, #ff9800);
  color: white;
}

.control-btn.resume {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.control-btn.adjust {
  background: linear-gradient(135deg, #17a2b8, #146eb4);
  color: white;
}

.control-btn.reports {
  background: linear-gradient(135deg, #6f42c1, #563d7c);
  color: white;
}

.control-btn.freeze {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.control-btn.approve {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.control-btn.emergency {
  background: linear-gradient(135deg, #dc3545, #bd2130);
  color: white;
  animation: pulse 2s infinite;
}

.alerts-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alerts-section h3 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.3rem;
}

.alerts-container {
  min-height: 100px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  color: #6c757d;
}

.alerts-container:empty::before {
  content: "No alerts at the moment";
  font-style: italic;
}

.leaderboard-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.leaderboard-section h3 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.3rem;
}

.leaderboard-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.leaderboard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ff9933;
}

.leaderboard-item .rank {
  font-weight: 700;
  color: #ff9933;
  min-width: 40px;
}

.leaderboard-item .name {
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
}

.leaderboard-item .wins {
  color: #6c757d;
  margin-right: 1rem;
}

.leaderboard-item .earnings {
  font-weight: 700;
  color: #28a745;
}

.exchange-rate-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.exchange-rate-section h3 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.3rem;
}

.exchange-rate-display {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 8px;
}

.rate-amount {
  font-size: 2rem;
  font-weight: 700;
  color: #28a745;
}

.rate-status.live {
  background: #28a745;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.badge.gaming {
  background: linear-gradient(135deg, #ff9933, #ff7700);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.badge.wallet {
  background: linear-gradient(135deg, #146eb4, #0d5aa7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.metric-change.positive {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.metric-change.neutral {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.metric-card.gaming .metric-icon {
  background: linear-gradient(135deg, #ff9933, #ff7700);
}

.metric-card.wallet .metric-icon {
  background: linear-gradient(135deg, #146eb4, #0d5aa7);
}

.metric-content {
  flex: 1;
}

.metric-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.metric-content p {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* DEMO ONLY: Manual Game Controls */
.demo-warning-section {
  margin: 2rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, #dc3545, #c82333);
  border-radius: 12px;
  border: 3px solid #bd2130;
}

.demo-warning {
  color: white;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.manual-controls-section {
  background: #fff3cd;
  border: 3px solid #ffc107;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  position: relative;
}

.manual-controls-section::before {
  content: "⚠️ DEMO ONLY ⚠️";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
}

.manual-controls-section h3 {
  color: #856404;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.4rem;
}

.active-games-panel,
.manual-control-panel,
.player-selection-panel,
.audit-trail-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ffc107;
}

.active-games-panel h4,
.manual-control-panel h4,
.player-selection-panel h4,
.audit-trail-section h4 {
  color: #856404;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.active-games-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.active-game-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #17a2b8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-info {
  flex: 1;
}

.game-id {
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.game-players {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.game-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.game-status.active {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.game-status.waiting {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.game-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.manual-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  position: relative;
}

.manual-btn::before {
  content: "DEMO";
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-weight: 700;
}

.manual-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.manual-btn.force-win {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.manual-btn.force-lose {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.manual-btn.override-result {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #2c3e50;
}

.manual-btn.custom-outcome {
  background: linear-gradient(135deg, #6f42c1, #563d7c);
  color: white;
}

.player-controls {
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 1rem;
  align-items: center;
}

.player-select,
.outcome-select,
.custom-amount-input {
  padding: 0.8rem;
  border: 2px solid #ffc107;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  background: white;
  color: #2c3e50;
}

.apply-btn {
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.apply-btn::before {
  content: "DEMO";
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-weight: 700;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.audit-trail {
  max-height: 300px;
  overflow-y: auto;
  border: 2px dashed #ffc107;
  border-radius: 8px;
  padding: 1rem;
}

.audit-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border-left: 4px solid #dc3545;
}

.audit-action {
  flex: 1;
  color: #2c3e50;
  font-weight: 600;
}

.audit-details {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0.25rem 0;
}

.audit-timestamp {
  color: #856404;
  font-size: 0.8rem;
  font-weight: 600;
}

.audit-trail:empty::before {
  content: "No manual interventions recorded (DEMO)";
  color: #6c757d;
  font-style: italic;
  display: block;
  text-align: center;
}

/* Warning animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Responsive design for manual controls */
@media (max-width: 768px) {
  .control-grid {
    grid-template-columns: 1fr;
  }

  .player-controls {
    grid-template-columns: 1fr;
  }

  .active-games-list {
    grid-template-columns: 1fr;
  }
}
