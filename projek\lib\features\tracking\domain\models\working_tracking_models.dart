import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class WorkingRealTimeTracking extends Equatable {
  final String orderId;
  final String riderId;
  final String userId;
  final String sellerId;
  final String status; // 'active', 'paused', 'completed', 'cancelled'
  final WorkingLocation? currentLocation;
  final WorkingLocation? destinationLocation;
  final double? estimatedTimeArrival;
  final double? distanceRemaining;
  final DateTime startedAt;
  final DateTime? completedAt;
  final DateTime lastUpdated;

  const WorkingRealTimeTracking({
    required this.orderId,
    required this.riderId,
    required this.userId,
    required this.sellerId,
    required this.status,
    this.currentLocation,
    this.destinationLocation,
    this.estimatedTimeArrival,
    this.distanceRemaining,
    required this.startedAt,
    this.completedAt,
    required this.lastUpdated,
  });

  factory WorkingRealTimeTracking.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkingRealTimeTracking(
      orderId: doc.id,
      riderId: data['riderId'] ?? '',
      userId: data['userId'] ?? '',
      sellerId: data['sellerId'] ?? '',
      status: data['status'] ?? 'active',
      currentLocation: data['currentLocation'] != null 
          ? WorkingLocation.fromMap(data['currentLocation'])
          : null,
      destinationLocation: data['destinationLocation'] != null
          ? WorkingLocation.fromMap(data['destinationLocation'])
          : null,
      estimatedTimeArrival: data['estimatedTimeArrival']?.toDouble(),
      distanceRemaining: data['distanceRemaining']?.toDouble(),
      startedAt: (data['startedAt'] as Timestamp).toDate(),
      completedAt: (data['completedAt'] as Timestamp?)?.toDate(),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'riderId': riderId,
      'userId': userId,
      'sellerId': sellerId,
      'status': status,
      'currentLocation': currentLocation?.toMap(),
      'destinationLocation': destinationLocation?.toMap(),
      'estimatedTimeArrival': estimatedTimeArrival,
      'distanceRemaining': distanceRemaining,
      'startedAt': Timestamp.fromDate(startedAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  @override
  List<Object?> get props => [
    orderId, riderId, userId, sellerId, status, currentLocation,
    destinationLocation, estimatedTimeArrival, distanceRemaining,
    startedAt, completedAt, lastUpdated,
  ];
}

class WorkingLocation extends Equatable {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? altitude;
  final double? speed;
  final double? heading;
  final DateTime timestamp;

  const WorkingLocation({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.altitude,
    this.speed,
    this.heading,
    required this.timestamp,
  });

  factory WorkingLocation.fromMap(Map<String, dynamic> data) {
    return WorkingLocation(
      latitude: data['latitude'].toDouble(),
      longitude: data['longitude'].toDouble(),
      accuracy: data['accuracy']?.toDouble(),
      altitude: data['altitude']?.toDouble(),
      speed: data['speed']?.toDouble(),
      heading: data['heading']?.toDouble(),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'altitude': altitude,
      'speed': speed,
      'heading': heading,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  @override
  List<Object?> get props => [
    latitude, longitude, accuracy, altitude, speed, heading, timestamp,
  ];
}
