// Working Firestore Models - Use these instead of broken enhanced models
export '../../features/chat/domain/models/working_chat_models.dart';
import '../../features/chat/domain/models/working_chat_models.dart';
export '../../features/wallet/domain/models/working_wallet_models.dart';
import '../../features/wallet/domain/models/working_wallet_models.dart';
export '../../features/tracking/domain/models/working_tracking_models.dart';
import '../../features/tracking/domain/models/working_tracking_models.dart';

// Type aliases for easier migration
typedef EnhancedChat = WorkingChat;
typedef EnhancedChatMessage = WorkingChatMessage;
typedef ChatParticipant = WorkingChatParticipant;
typedef EnhancedWallet = WorkingWallet;
typedef RealTimeTracking = WorkingRealTimeTracking;
typedef RealTimeLocation = WorkingLocation;


