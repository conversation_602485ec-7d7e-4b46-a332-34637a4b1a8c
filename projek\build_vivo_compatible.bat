@echo off
echo ========================================
echo VIVO V23 5G COMPATIBLE APK BUILDER
echo ========================================
echo.
echo Building 3 APKs optimized for Vivo V23 5G:
echo 1. User App (Main marketplace with gaming)
echo 2. Rider App (Delivery partners)
echo 3. Seller App (Vendors/merchants)
echo.

REM Set environment for E: drive pub cache
set PUB_CACHE=E:\Appdata\flutter_pub_cache

echo Setting up environment...
echo PUB_CACHE: %PUB_CACHE%
echo.

echo ========================================
echo STEP 1: CLEANING PROJECT
echo ========================================
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed!
    pause
    exit /b 1
)

echo ========================================
echo STEP 2: GETTING DEPENDENCIES
echo ========================================
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed!
    pause
    exit /b 1
)

echo ========================================
echo STEP 3: BUILDING USER APP (DEBUG)
echo ========================================
echo Building User App for Vivo V23 5G...
flutter build apk --flavor userDev --debug --target-platform android-arm64
if %errorlevel% neq 0 (
    echo ERROR: User App build failed!
    pause
    exit /b 1
)
echo ✅ User App built successfully!

echo ========================================
echo STEP 4: BUILDING RIDER APP (DEBUG)
echo ========================================
echo Building Rider App for Vivo V23 5G...
flutter build apk --flavor riderDev --debug --target-platform android-arm64
if %errorlevel% neq 0 (
    echo ERROR: Rider App build failed!
    pause
    exit /b 1
)
echo ✅ Rider App built successfully!

echo ========================================
echo STEP 5: BUILDING SELLER APP (DEBUG)
echo ========================================
echo Building Seller App for Vivo V23 5G...
flutter build apk --flavor sellerDev --debug --target-platform android-arm64
if %errorlevel% neq 0 (
    echo ERROR: Seller App build failed!
    pause
    exit /b 1
)
echo ✅ Seller App built successfully!

echo ========================================
echo 🎉 ALL BUILDS COMPLETED!
echo ========================================
echo.
echo APK files generated for Vivo V23 5G:
dir build\app\outputs\flutter-apk\*.apk /b 2>nul
echo.
echo ========================================
echo INSTALLATION COMMANDS FOR VIVO V23 5G:
echo ========================================
echo adb install build\app\outputs\flutter-apk\app-user-dev-debug.apk
echo adb install build\app\outputs\flutter-apk\app-rider-dev-debug.apk
echo adb install build\app\outputs\flutter-apk\app-seller-dev-debug.apk
echo.
echo ========================================
echo VIVO V23 5G COMPATIBILITY NOTES:
echo ========================================
echo ✅ Target SDK: Android 14 (API 34)
echo ✅ Architecture: ARM64-v8a (optimized for Vivo V23 5G)
echo ✅ Universal APK: Compatible with all Android devices
echo ✅ Debug build: Faster installation and testing
echo.
echo These APKs should install and run properly on your Vivo V23 5G!
echo.
pause
