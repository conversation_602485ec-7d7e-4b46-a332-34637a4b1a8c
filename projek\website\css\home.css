/* Super Vision Homepage Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  padding: 1rem 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-right: 0.3rem;
}

.logo-icon {
  font-size: 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.app-logo-img {
  width: 20px;
  height: 20px;
  object-fit: contain;
  margin-right: 0.3rem;
}

.footer-logo-img {
  width: 30px;
  height: 30px;
  object-fit: contain;
  margin-right: 0.3rem;
}

.logo-tagline {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #667eea;
}

.nav-link.admin-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.nav-link.gaming-btn {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.nav-link.gaming-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

.nav-link.wallet-btn {
  background: linear-gradient(135deg, #146eb4, #667eea);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.nav-link.wallet-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(20, 110, 180, 0.3);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.hero-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 70%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  top: 40%;
  left: 50%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.hero-title {
  margin-bottom: 2rem;
}

.title-main {
  display: block;
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.title-sub {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ff9933;
}

.title-english {
  display: block;
  font-size: 1.3rem;
  font-weight: 500;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.8;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #ff9933;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: white;
  color: #333;
}

.btn-primary:hover {
  background: transparent;
  color: white;
  border-color: white;
}

.btn-secondary {
  background: transparent;
  color: white;
  border-color: white;
}

.btn-secondary:hover {
  background: white;
  color: #333;
}

/* Phone Mockup */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  width: 300px;
  height: 600px;
  background: #333;
  border-radius: 30px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 20px;
  overflow: hidden;
}

.app-interface {
  padding: 1rem;
  height: 100%;
}

.app-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.app-logo {
  font-size: 1.5rem;
}

.app-name {
  font-weight: 600;
  color: #333;
}

.app-content {
  padding: 1rem 0;
  display: grid;
  gap: 1rem;
}

.service-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  color: #333;
}

.service-icon {
  font-size: 1.5rem;
}

.service-name {
  font-weight: 500;
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Founder Introduction */
.founder-intro-section {
  padding: 5rem 0;
  background: #f8f9fa;
}

.founder-intro-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 2rem;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.founder-image-small {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid #667eea;
}

.founder-image-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.founder-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.founder-message h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.founder-message blockquote {
  font-style: italic;
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.founder-signature {
  margin-bottom: 1rem;
}

.signature-name {
  display: block;
  font-weight: 600;
  color: #2c3e50;
}

.signature-title {
  font-size: 0.9rem;
  color: #666;
}

.founder-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.founder-link:hover {
  color: #5a6fd8;
}

/* About Section */
.about-section {
  padding: 5rem 0;
  background: white;
}

.about-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.about-card {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.about-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.about-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.about-card p {
  color: #666;
  line-height: 1.6;
}

.about-vision {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
}

.vision-content h3 {
  margin-bottom: 2rem;
  font-size: 2rem;
}

.mission-text {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mission-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.mission-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-text {
  font-weight: 600;
}

/* Services Section */
.services-section {
  padding: 5rem 0;
  background: #f8f9fa;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.service-category {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.service-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.category-header {
  margin-bottom: 1.5rem;
}

.category-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
}

.category-header h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.category-services {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.service-item {
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #666;
}

.category-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.category-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.services-cta {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.services-cta h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.services-cta p {
  color: #666;
  margin-bottom: 2rem;
}

/* Apps Section */
.apps-section {
  padding: 5rem 0;
  background: white;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-bottom: 4rem;
}

.app-card {
  background: white;
  border-radius: 25px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.app-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.app-card.user-app {
  border-color: #3498db;
}

.app-card.rider-app {
  border-color: #e74c3c;
}

.app-card.seller-app {
  border-color: #27ae60;
}

.app-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5rem;
}

.app-content {
  text-align: center;
}

.app-content h3 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.app-tagline {
  color: #e74c3c;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.app-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.app-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #555;
}

.feature-icon {
  font-size: 1.2rem;
}

.app-download {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.download-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: #333;
  color: white;
  text-decoration: none;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.download-btn:hover {
  background: #555;
  transform: translateY(-2px);
}

.download-btn img {
  height: 20px;
}

.apps-benefits {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white;
  padding: 3rem;
  border-radius: 25px;
  text-align: center;
}

.apps-benefits h3 {
  font-size: 2rem;
  margin-bottom: 2rem;
  font-weight: 700;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.benefit {
  background: rgba(255, 255, 255, 0.15);
  padding: 2rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.benefit-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
}

.benefit h4 {
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.benefit p {
  opacity: 0.9;
  line-height: 1.5;
}

/* Gaming & Wallet Section */
.gaming-wallet-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.gaming-wallet-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.wallet-card,
.gaming-card,
.staking-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.wallet-card:hover,
.gaming-card:hover,
.staking-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.wallet-card {
  border-color: #ff9933;
  background: linear-gradient(135deg, rgba(255, 153, 51, 0.05), white);
}

.gaming-card {
  border-color: #146eb4;
  background: linear-gradient(135deg, rgba(20, 110, 180, 0.05), white);
}

.staking-card {
  border-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), white);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
}

.visibility-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.visibility-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.wallet-balances {
  margin-bottom: 2rem;
}

.balance-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.balance-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.3rem;
}

.balance-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.balance-inr {
  font-size: 0.9rem;
  color: #666;
}

.wallet-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.wallet-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wallet-btn.primary {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white;
}

.wallet-btn.secondary {
  background: #f8f9fa;
  color: #2c3e50;
  border: 2px solid #dee2e6;
}

.wallet-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.conversion-rate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 10px;
}

.rate-text {
  font-weight: 600;
  color: #2c3e50;
}

.rate-status {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Gaming Card Styles */
.game-status {
  display: flex;
  align-items: center;
}

.status-badge {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.wheel-container-mini {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
}

.mini-wheel {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: relative;
  border: 4px solid #ff9933;
  overflow: hidden;
  transition: transform 3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.wheel-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.7rem;
  color: white;
}

.wheel-segment:nth-child(1) { background: #e74c3c; transform: rotate(0deg) skewY(-45deg); }
.wheel-segment:nth-child(2) { background: #f39c12; transform: rotate(45deg) skewY(-45deg); }
.wheel-segment:nth-child(3) { background: #27ae60; transform: rotate(90deg) skewY(-45deg); }
.wheel-segment:nth-child(4) { background: #3498db; transform: rotate(135deg) skewY(-45deg); }
.wheel-segment:nth-child(5) { background: #9b59b6; transform: rotate(180deg) skewY(-45deg); }
.wheel-segment:nth-child(6) { background: #e67e22; transform: rotate(225deg) skewY(-45deg); }
.wheel-segment:nth-child(7) { background: #95a5a6; transform: rotate(270deg) skewY(-45deg); }
.wheel-segment:nth-child(8) { background: #2c3e50; transform: rotate(315deg) skewY(-45deg); }

.mini-pointer {
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 15px solid #dc3545;
  z-index: 10;
}

.game-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.spin-option {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.spin-option h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-size: 1rem;
}

.spin-option p {
  margin-bottom: 1rem;
  color: #666;
  font-size: 0.8rem;
}

.game-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(135deg, #146eb4, #667eea);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-btn.max {
  background: linear-gradient(135deg, #ff9933, #138808);
}

.game-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Staking Card Styles */
.apy-badge {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.staking-info {
  margin-bottom: 2rem;
}

.staked-amount,
.daily-reward {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.3rem;
}

.amount,
.reward {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.value {
  font-size: 0.9rem;
  color: #666;
}

.staking-plans {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.plan {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.8rem 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.plan.active {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.plan-name {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.plan-apy {
  font-size: 0.7rem;
  color: #666;
}

.stake-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Contact Section */
.contact-section {
  padding: 5rem 0;
  background: #f8f9fa;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.contact-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.contact-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.contact-card p {
  color: #666;
  margin-bottom: 0.5rem;
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Footer */
.footer {
  background: #2c3e50;
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.footer-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo .logo-icon {
  font-size: 2rem;
}

.footer-logo .logo-text {
  font-size: 1.5rem;
  font-weight: 700;
}

.footer-tagline {
  color: #bdc3c7;
  margin-bottom: 1rem;
}

.footer-description p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: #34495e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #667eea;
  transform: translateY(-2px);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.footer-column h4 {
  margin-bottom: 1rem;
  font-weight: 600;
  color: white;
}

.footer-column a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-badges {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.badge {
  background: #34495e;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Floating Action Buttons */
.floating-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 1000;
}

.fab {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.fab:hover {
  transform: scale(1.1);
}

.fab.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.fab.secondary {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.fab.admin {
  background: linear-gradient(135deg, #dc3545, #c82333);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .title-main {
    font-size: 2.5rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .phone-mockup {
    width: 250px;
    height: 500px;
  }

  .founder-intro-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .mission-stats {
    flex-direction: column;
    align-items: center;
  }

  .apps-grid {
    grid-template-columns: 1fr;
  }

  .app-features {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .floating-actions {
    bottom: 10px;
    right: 10px;
  }

  .fab {
    width: 50px;
    height: 50px;
  }
}
