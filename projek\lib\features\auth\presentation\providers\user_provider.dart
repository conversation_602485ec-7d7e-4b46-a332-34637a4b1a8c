import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../../domain/models/user.dart';

// User state notifier
class UserNotifier extends StateNotifier<AsyncValue<User?>> {
  UserNotifier() : super(const AsyncValue.loading()) {
    _loadUser();
  }

  static const String _userKey = 'current_user';

  Future<void> _loadUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        final user = User.fromJson(userMap);
        state = AsyncValue.data(user);
      } else {
        // Load sample user for demo
        final sampleUser = SampleUser.defaultUser;
        await _saveUser(sampleUser);
        state = AsyncValue.data(sampleUser);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> _saveUser(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toJson());
      await prefs.setString(_userKey, userJson);
    } catch (e) {
      // Handle save error
      rethrow;
    }
  }

  Future<void> updateUser(User updatedUser) async {
    try {
      state = const AsyncValue.loading();
      
      // Update the user with current timestamp
      final userWithTimestamp = updatedUser.copyWith(
        updatedAt: DateTime.now(),
      );
      
      await _saveUser(userWithTimestamp);
      state = AsyncValue.data(userWithTimestamp);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateProfileImage(String imageUrl) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedUser = currentUser.copyWith(
        profileImageUrl: imageUrl,
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> addAddress(Address address) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedAddresses = [...currentUser.addresses, address];
      final updatedUser = currentUser.copyWith(
        addresses: updatedAddresses,
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> updateAddress(Address updatedAddress) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedAddresses = currentUser.addresses.map((address) {
        return address.id == updatedAddress.id ? updatedAddress : address;
      }).toList();
      
      final updatedUser = currentUser.copyWith(
        addresses: updatedAddresses,
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> removeAddress(String addressId) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedAddresses = currentUser.addresses
          .where((address) => address.id != addressId)
          .toList();
      
      final updatedUser = currentUser.copyWith(
        addresses: updatedAddresses,
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> setDefaultAddress(String addressId) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedAddresses = currentUser.addresses.map((address) {
        return address.copyWith(isDefault: address.id == addressId);
      }).toList();
      
      final updatedUser = currentUser.copyWith(
        addresses: updatedAddresses,
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> updatePreferences(Map<String, dynamic> preferences) async {
    final currentUser = state.value;
    if (currentUser != null) {
      final updatedUser = currentUser.copyWith(
        preferences: {...currentUser.preferences, ...preferences},
        updatedAt: DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// Providers
final userProvider = StateNotifierProvider<UserNotifier, AsyncValue<User?>>((ref) {
  return UserNotifier();
});

// Computed providers
final currentUserProvider = Provider<User?>((ref) {
  final userState = ref.watch(userProvider);
  return userState.value;
});

final isLoggedInProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});

final userAddressesProvider = Provider<List<Address>>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.addresses ?? [];
});

final defaultAddressProvider = Provider<Address?>((ref) {
  final addresses = ref.watch(userAddressesProvider);
  return addresses.where((address) => address.isDefault).firstOrNull;
});

// Form validation providers
final emailValidationProvider = Provider.family<String?, String>((ref, email) {
  if (email.isEmpty) return 'Email is required';
  
  final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  if (!emailRegex.hasMatch(email)) return 'Please enter a valid email';
  
  return null;
});

final phoneValidationProvider = Provider.family<String?, String>((ref, phone) {
  if (phone.isEmpty) return null; // Phone is optional
  
  final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
  if (!phoneRegex.hasMatch(phone)) return 'Please enter a valid phone number';
  
  return null;
});

final nameValidationProvider = Provider.family<String?, String>((ref, name) {
  if (name.isEmpty) return 'Name is required';
  if (name.length < 2) return 'Name must be at least 2 characters';
  if (name.length > 50) return 'Name must be less than 50 characters';
  
  return null;
});
