# Flutter Development Setup for Hot Reload on Android Devices

This guide will help you set up fast reload functionality for testing your Flutter app on physical Android devices.

## Prerequisites

1. **Flutter SDK** installed and configured
2. **Android Studio** or **Android SDK** installed
3. **USB Debugging** enabled on your Android device
4. **ADB (Android Debug Bridge)** accessible from command line

## Quick Setup

### 1. Device Preparation

1. **Enable Developer Options** on your Android device:
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times
   - Developer Options will appear in Settings

2. **Enable USB Debugging**:
   - Go to Settings > Developer Options
   - Enable "USB Debugging"
   - Connect your device via USB
   - Accept the USB debugging prompt

3. **Verify Device Connection**:
   ```bash
   flutter devices
   ```

### 2. Network Configuration

1. **Find Your Development Machine's IP Address**:
   - Run the setup script: `scripts\setup_device.bat`
   - Or manually check: `ipconfig` (Windows) / `ifconfig` (Mac/Linux)

2. **Update Network Security Config**:
   - Open `android\app\src\main\res\xml\network_security_config.xml`
   - Add your development machine's IP address:
   ```xml
   <domain includeSubdomains="true">YOUR_IP_ADDRESS</domain>
   ```

## Development Workflow

### Option 1: Using Development Scripts (Recommended)

1. **Setup Device** (first time only):
   ```bash
   scripts\setup_device.bat
   ```

2. **Run Development App**:
   ```bash
   scripts\dev_run.bat
   ```

3. **Build Development APK**:
   ```bash
   scripts\dev_build.bat
   ```

### Option 2: Manual Commands

1. **Debug Mode** (with hot reload):
   ```bash
   flutter run --debug
   ```

2. **Development Build Variant**:
   ```bash
   flutter run --flavor development --debug
   ```

3. **Profile Mode** (performance testing):
   ```bash
   flutter run --profile
   ```

## Hot Reload Commands

Once your app is running, use these commands in the terminal:

- **`r`** - Hot reload (preserves app state)
- **`R`** - Hot restart (resets app state)
- **`q`** - Quit the development session
- **`h`** - Show help with all available commands

## Build Variants

### Debug Build
- **Purpose**: Development with full debugging capabilities
- **Features**: Hot reload, debugging, logging, cleartext traffic enabled
- **Performance**: Slower, larger APK size
- **Command**: `flutter run --debug`

### Development Build
- **Purpose**: Alternative development configuration
- **Features**: Same as debug but with explicit development settings
- **Performance**: Same as debug build
- **Command**: `flutter run --flavor development --debug`

### Profile Build
- **Purpose**: Performance testing
- **Features**: Limited debugging, performance profiling
- **Performance**: Near-production performance
- **Command**: `flutter run --profile`

## Troubleshooting

### Device Not Detected
1. Check USB connection
2. Verify USB debugging is enabled
3. Try different USB cable/port
4. Run `adb devices` to check ADB connection
5. Restart ADB: `adb kill-server && adb start-server`

### Hot Reload Not Working
1. Ensure device and development machine are on same network
2. Check firewall settings
3. Verify IP address in network security config
4. Try hot restart (`R`) instead of hot reload (`r`)

### Connection Issues
1. Check if cleartext traffic is allowed
2. Verify network security configuration
3. Ensure development machine IP is whitelisted
4. Try using USB connection instead of WiFi

### Build Errors
1. Clean the project: `flutter clean`
2. Get dependencies: `flutter pub get`
3. Rebuild: `flutter run --debug`

## Performance Tips

1. **Use Debug Mode** for active development with frequent changes
2. **Use Profile Mode** for performance testing
3. **Close unnecessary apps** on your device for better performance
4. **Use WiFi connection** for faster hot reload (after initial USB setup)
5. **Keep your device plugged in** to prevent battery drain during development

## Advanced Configuration

### Custom Development Server Port
```bash
flutter run --debug --dart-define=FLUTTER_WEB_PORT=8080
```

### Specific Device Target
```bash
flutter run --debug -d <device_id>
```

### Verbose Logging
```bash
flutter run --debug --verbose
```

## File Structure

```
android/
├── app/
│   ├── src/
│   │   ├── debug/AndroidManifest.xml          # Debug-specific config
│   │   ├── development/AndroidManifest.xml    # Development build config
│   │   └── main/
│   │       ├── AndroidManifest.xml            # Main app config
│   │       └── res/xml/network_security_config.xml  # Network security
│   └── build.gradle.kts                       # Build configuration
scripts/
├── dev_run.bat                                # Development run script
├── dev_build.bat                              # Development build script
└── setup_device.bat                           # Device setup script
```

## Next Steps

1. Run `scripts\setup_device.bat` to verify your setup
2. Start development with `scripts\dev_run.bat`
3. Make changes to your Flutter code and see them instantly with hot reload!

For more information, see the [Flutter documentation](https://flutter.dev/docs/development/tools/hot-reload).
