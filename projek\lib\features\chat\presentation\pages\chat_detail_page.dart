import 'package:flutter/material.dart';
import '../../../../core/models/chat_models.dart';

class ChatDetailPage extends StatelessWidget {
  final Chat chat;
  const ChatDetailPage({super.key, required this.chat});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Chat with ${chat.getChatTitle("", [])}')),
      body: Center(child: Text('Chat detail for chat ID: ${chat.id}')),
    );
  }
}
