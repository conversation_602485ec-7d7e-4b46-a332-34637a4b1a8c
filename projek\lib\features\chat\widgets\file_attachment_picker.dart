import 'package:flutter/material.dart';
import '../services/file_upload_service.dart';

/// File attachment picker widget for chat
class FileAttachmentPicker extends StatelessWidget {
  final String chatId;
  final String userId;
  final Function(ChatFileUploadResult) onFileSelected;
  final VoidCallback? onCancel;

  const FileAttachmentPicker({
    super.key,
    required this.chatId,
    required this.userId,
    required this.onFileSelected,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // Title
          Text(
            'Attach File',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // File options
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildFileOption(
                context,
                icon: Icons.camera_alt,
                label: 'Camera',
                color: Colors.blue,
                onTap: () => _pickFromCamera(context),
              ),
              _buildFileOption(
                context,
                icon: Icons.photo_library,
                label: 'Gallery',
                color: Colors.green,
                onTap: () => _pickFromGallery(context),
              ),
              _buildFileOption(
                context,
                icon: Icons.picture_as_pdf,
                label: 'PDF',
                color: Colors.red,
                onTap: () => _pickPDF(context),
              ),
              _buildFileOption(
                context,
                icon: Icons.description,
                label: 'Document',
                color: Colors.orange,
                onTap: () => _pickDocument(context),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Cancel button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                Navigator.pop(context);
                onCancel?.call();
              },
              child: const Text('Cancel'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _pickFromCamera(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Taking photo...');
      
      final result = await FileUploadService.pickAndUploadImageFromCamera(
        chatId: chatId,
        userId: userId,
      );
      
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      
      if (result != null) {
        onFileSelected(result);
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      _showErrorDialog(context, 'Failed to take photo: $e');
    }
  }

  void _pickFromGallery(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Selecting image...');
      
      final result = await FileUploadService.pickAndUploadImageFromGallery(
        chatId: chatId,
        userId: userId,
      );
      
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      
      if (result != null) {
        onFileSelected(result);
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      _showErrorDialog(context, 'Failed to select image: $e');
    }
  }

  void _pickPDF(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Uploading PDF...');
      
      final result = await FileUploadService.pickAndUploadPDF(
        chatId: chatId,
        userId: userId,
      );
      
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      
      if (result != null) {
        onFileSelected(result);
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      _showErrorDialog(context, 'Failed to upload PDF: $e');
    }
  }

  void _pickDocument(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Uploading document...');
      
      final result = await FileUploadService.pickAndUploadDocument(
        chatId: chatId,
        userId: userId,
      );
      
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      
      if (result != null) {
        onFileSelected(result);
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      Navigator.pop(context); // Close picker
      _showErrorDialog(context, 'Failed to upload document: $e');
    }
  }

  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show file attachment picker as bottom sheet
  static void show(
    BuildContext context, {
    required String chatId,
    required String userId,
    required Function(ChatFileUploadResult) onFileSelected,
    VoidCallback? onCancel,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => FileAttachmentPicker(
        chatId: chatId,
        userId: userId,
        onFileSelected: onFileSelected,
        onCancel: onCancel,
      ),
    );
  }
}

/// File upload progress widget
class FileUploadProgress extends StatelessWidget {
  final String fileName;
  final double progress;
  final VoidCallback? onCancel;

  const FileUploadProgress({
    super.key,
    required this.fileName,
    required this.progress,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.upload_file, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[300],
                ),
                const SizedBox(height: 2),
                Text(
                  '${(progress * 100).toInt()}% uploaded',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          if (onCancel != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onCancel,
              icon: const Icon(Icons.close),
              iconSize: 20,
            ),
          ],
        ],
      ),
    );
  }
}
