// Admin Login JavaScript
document.addEventListener('DOMContentLoaded', function() {
  
  // Admin credentials
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'monuj12@#'
  };
  
  // Initialize login functionality
  initializeLogin();
  
  function initializeLogin() {
    const loginForm = document.getElementById('adminLoginForm');
    const togglePassword = document.getElementById('togglePassword');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    
    // Password toggle functionality
    togglePassword.addEventListener('click', function() {
      const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
      passwordInput.setAttribute('type', type);
      
      const icon = this.querySelector('.material-icons');
      icon.textContent = type === 'password' ? 'visibility' : 'visibility_off';
    });
    
    // Form submission
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();
      handleLogin();
    });
    
    // Input animations
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentNode.classList.add('focused');
      });
      
      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentNode.classList.remove('focused');
        }
      });
    });
    
    // Auto-fill demo credentials (for testing)
    setTimeout(() => {
      emailInput.value = ADMIN_CREDENTIALS.email;
      passwordInput.value = ADMIN_CREDENTIALS.password;
    }, 1000);
  }
  
  function handleLogin() {
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const loginBtn = document.getElementById('loginBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');
    
    // Show loading state
    loginBtn.classList.add('loading');
    loadingOverlay.classList.add('active');
    
    // Simulate authentication delay
    setTimeout(() => {
      if (email === ADMIN_CREDENTIALS.email && password === ADMIN_CREDENTIALS.password) {
        // Successful login
        handleSuccessfulLogin();
      } else {
        // Failed login
        handleFailedLogin();
      }
      
      // Hide loading state
      loginBtn.classList.remove('loading');
      loadingOverlay.classList.remove('active');
    }, 2000);
  }
  
  function handleSuccessfulLogin() {
    // Store admin session
    sessionStorage.setItem('adminLoggedIn', 'true');
    sessionStorage.setItem('adminEmail', ADMIN_CREDENTIALS.email);
    sessionStorage.setItem('loginTime', new Date().toISOString());
    
    // Show success modal
    const successModal = document.getElementById('successModal');
    successModal.classList.add('active');
    
    // Redirect to dashboard after 3 seconds
    setTimeout(() => {
      window.location.href = 'dashboard.html';
    }, 3000);
  }
  
  function handleFailedLogin() {
    // Show error modal
    const errorModal = document.getElementById('errorModal');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = 'Invalid email or password. Please check your credentials and try again.';
    errorModal.classList.add('active');
    
    // Clear password field
    document.getElementById('password').value = '';
    
    // Shake animation for form
    const loginCard = document.querySelector('.login-card');
    loginCard.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
      loginCard.style.animation = '';
    }, 500);
  }
  
  // Close error modal
  window.closeErrorModal = function() {
    const errorModal = document.getElementById('errorModal');
    errorModal.classList.remove('active');
  };
  
  // Add shake animation CSS
  const style = document.createElement('style');
  style.textContent = `
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-10px); }
      75% { transform: translateX(10px); }
    }
  `;
  document.head.appendChild(style);
  
  // Check if already logged in
  if (sessionStorage.getItem('adminLoggedIn') === 'true') {
    // Check if session is still valid (24 hours)
    const loginTime = new Date(sessionStorage.getItem('loginTime'));
    const now = new Date();
    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
    
    if (hoursDiff < 24) {
      // Redirect to dashboard
      window.location.href = 'dashboard.html';
    } else {
      // Clear expired session
      sessionStorage.clear();
    }
  }
  
  // Forgot password functionality
  const forgotPasswordLink = document.querySelector('.forgot-password');
  forgotPasswordLink.addEventListener('click', function(e) {
    e.preventDefault();
    
    // Show forgot password modal
    const modal = document.createElement('div');
    modal.className = 'error-modal active';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <span class="material-icons" style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">help</span>
          <h3>Password Recovery</h3>
        </div>
        <div class="modal-body">
          <p>For security reasons, password recovery must be done through the system administrator.</p>
          <p><strong>Contact:</strong> <EMAIL></p>
          <p><strong>Phone:</strong> +91 1800 123 456</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" onclick="this.closest('.error-modal').remove()">Close</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
  });
  
  // System status simulation
  updateSystemStatus();
  setInterval(updateSystemStatus, 30000); // Update every 30 seconds
  
  function updateSystemStatus() {
    const statusItems = document.querySelectorAll('.status-indicator');
    
    // Simulate random status changes
    statusItems.forEach((indicator, index) => {
      const random = Math.random();
      
      if (index === 3) { // SMS Service - sometimes has issues
        if (random > 0.7) {
          indicator.className = 'status-indicator warning';
        } else {
          indicator.className = 'status-indicator online';
        }
      } else {
        // Other services are usually online
        if (random > 0.95) {
          indicator.className = 'status-indicator warning';
        } else {
          indicator.className = 'status-indicator online';
        }
      }
    });
  }
  
  // Add floating animation to shapes
  const shapes = document.querySelectorAll('.shape');
  shapes.forEach((shape, index) => {
    shape.addEventListener('mouseenter', function() {
      this.style.transform = 'scale(1.2) rotate(45deg)';
      this.style.opacity = '1';
    });
    
    shape.addEventListener('mouseleave', function() {
      this.style.transform = '';
      this.style.opacity = '';
    });
  });
  
  // Keyboard shortcuts
  document.addEventListener('keydown', function(e) {
    // Ctrl + Enter to submit form
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      document.getElementById('adminLoginForm').dispatchEvent(new Event('submit'));
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
      const activeModal = document.querySelector('.error-modal.active, .success-modal.active');
      if (activeModal) {
        activeModal.classList.remove('active');
      }
    }
  });
  
  // Add ripple effect to login button
  const loginBtn = document.getElementById('loginBtn');
  loginBtn.addEventListener('click', function(e) {
    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.6);
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;
    
    const rect = this.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = e.clientX - rect.left - size / 2 + 'px';
    ripple.style.top = e.clientY - rect.top - size / 2 + 'px';
    
    this.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  });
  
  console.log('Admin login system initialized');
  console.log('Demo credentials loaded for testing');

  // Auto login function for quick access
  window.autoLogin = function() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');

    // Fill credentials
    emailInput.value = ADMIN_CREDENTIALS.email;
    passwordInput.value = ADMIN_CREDENTIALS.password;

    // Trigger login
    setTimeout(() => {
      handleLogin();
    }, 500);
  };

  // Quick login function for demo accounts
  window.quickLogin = function(accountType) {
    let email, password, name;

    switch(accountType) {
      case 'admin':
        email = '<EMAIL>';
        password = 'monuj12@#';
        name = 'Super Admin';
        break;
      case 'manager':
        email = '<EMAIL>';
        password = 'manager123';
        name = 'Manager';
        break;
      default:
        email = '<EMAIL>';
        password = 'monuj12@#';
        name = 'Admin';
    }

    // Fill the form
    document.getElementById('email').value = email;
    document.getElementById('password').value = password;

    // Show loading overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingText = loadingOverlay.querySelector('p');
    if (loadingText) {
      loadingText.textContent = `Signing in as ${name}...`;
    }
    loadingOverlay.classList.add('active');

    // Simulate authentication
    setTimeout(() => {
      loadingOverlay.classList.remove('active');

      // Store user info in sessionStorage
      sessionStorage.setItem('adminLoggedIn', 'true');
      sessionStorage.setItem('adminEmail', email);
      sessionStorage.setItem('adminName', name);
      sessionStorage.setItem('adminType', accountType);
      sessionStorage.setItem('loginTime', new Date().toISOString());

      // Show success modal
      const successModal = document.getElementById('successModal');
      const successMessage = successModal.querySelector('.modal-body p');
      if (successMessage) {
        successMessage.textContent = `Welcome back, ${name}! Redirecting to dashboard...`;
      }
      successModal.classList.add('active');

      // Redirect after 2 seconds
      setTimeout(() => {
        window.location.href = 'dashboard.html';
      }, 2000);
    }, 1500);
  };

  // Enhanced notification system
  window.showNotification = function(message, type = 'success') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 2000;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      color: white;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    if (type === 'success') {
      notification.style.background = '#28a745';
    } else if (type === 'error') {
      notification.style.background = '#dc3545';
    } else {
      notification.style.background = '#667eea';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  };
});
