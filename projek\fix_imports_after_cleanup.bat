@echo off
echo ========================================
echo FIXING IMPORTS AFTER CLEANUP
echo ========================================

echo.
echo This script will fix any broken imports after cleanup.
echo.

echo Step 1: Running Flutter clean...
flutter clean

echo Step 2: Getting dependencies...
flutter pub get

echo Step 3: Running dart fix to auto-fix imports...
dart fix --apply

echo Step 4: Checking for compilation errors...
flutter analyze

echo Step 5: Testing build...
flutter build apk --debug --flavor user -t lib/main_user.dart

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SUCCESS! All imports fixed and app builds correctly.
) else (
    echo.
    echo ❌ There are still some issues. Check the output above.
    echo.
    echo Common fixes needed:
    echo 1. Update import paths in router files
    echo 2. Remove references to deleted files
    echo 3. Update main.dart references to main_user.dart
)

echo.
echo ========================================
echo IMPORT FIX COMPLETED
echo ========================================

pause
