import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';

class UserSplashPage extends ConsumerStatefulWidget {
  const UserSplashPage({super.key});

  @override
  ConsumerState<UserSplashPage> createState() => _UserSplashPageState();
}

class _UserSplashPageState extends ConsumerState<UserSplashPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;

  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _logoRotation;
  late Animation<double> _textSlide;
  late Animation<double> _progressAnimation;
  late Animation<Color?> _backgroundGradient;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
    _navigateToNext();
  }

  void _initializeAnimations() {
    // Main animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Progress animation controller
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Fade and scale animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.elasticOut),
      ),
    );

    // Logo rotation animation
    _logoRotation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    // Text slide animation
    _textSlide = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOutBack),
    );

    // Progress animation
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    // Background gradient animation
    _backgroundGradient =
        ColorTween(
          begin: AppColors.primaryBlue,
          end: AppColors.primaryBlueDark,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );
  }

  void _startAnimationSequence() async {
    // Start logo animation
    _logoController.forward();

    // Start main animation after a delay
    await Future.delayed(const Duration(milliseconds: 300));
    _animationController.forward();

    // Start text animation
    await Future.delayed(const Duration(milliseconds: 600));
    _textController.forward();

    // Start progress animation
    await Future.delayed(const Duration(milliseconds: 900));
    _progressController.forward();
  }

  void _navigateToNext() {
    Future.delayed(const Duration(seconds: 4), () async {
      if (!mounted) return;

      // Check if user is already authenticated
      if (AuthService.isLoggedIn) {
        // Check if biometric login is available and enabled
        final shouldUseBiometric = await AuthService.shouldUseBiometricLogin();
        if (!mounted) return;

        if (shouldUseBiometric) {
          // Show biometric prompt
          final biometricSuccess =
              await AuthService.authenticateWithBiometrics();
          if (!mounted) return;

          if (biometricSuccess) {
            context.go('/home');
          } else {
            // Biometric failed, go to login
            context.go('/login');
          }
        } else {
          // User is logged in but no biometric, go to dashboard
          context.go('/home');
        }
      } else {
        // User not logged in, go to login
        context.go('/login');
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _logoController.dispose();
    _textController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: size.width,
            height: size.height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _backgroundGradient.value ?? AppColors.primaryBlue,
                  AppColors.primaryBlueDark,
                  AppColors.accentPurple.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Stack(
              children: [
                // Background particles/dots
                _buildBackgroundParticles(),

                // Main content
                Center(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Animated Logo
                          _buildAnimatedLogo(),

                          const SizedBox(height: 40),

                          // App Name with animation
                          _buildAnimatedAppName(),

                          const SizedBox(height: 12),

                          // Tagline with animation
                          _buildAnimatedTagline(),

                          const SizedBox(height: 60),

                          // Progress indicator
                          _buildProgressIndicator(),

                          const SizedBox(height: 20),

                          // Loading text
                          _buildLoadingText(),
                        ],
                      ),
                    ),
                  ),
                ),

                // Bottom branding
                _buildBottomBranding(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackgroundParticles() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(); // Simplified for now
        },
      ),
    );
  }

  Widget _buildAnimatedLogo() {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _logoRotation.value * 0.1,
          child: Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(32),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                  spreadRadius: 5,
                ),
                BoxShadow(
                  color: AppColors.accentGreen.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Gradient background
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(32),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        AppColors.primaryBlue.withValues(alpha: 0.1),
                      ],
                    ),
                  ),
                ),
                // Main icon
                Icon(
                  Icons.shopping_bag_outlined,
                  size: 70,
                  color: AppColors.primaryBlue,
                ),
                // Overlay icon for depth
                Positioned(
                  top: 35,
                  right: 35,
                  child: Icon(
                    Icons.star,
                    size: 20,
                    color: AppColors.secondaryOrange,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedAppName() {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _textSlide.value),
          child: Text(
            'My India First',
            style: AppTextStyles.headlineLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 32,
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedTagline() {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _textSlide.value * 0.5),
          child: Text(
            'Your Super App for Everything',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  Widget _buildProgressIndicator() {
    return AnimatedBuilder(
      animation: _progressController,
      builder: (context, child) {
        return Column(
          children: [
            Container(
              width: 200,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _progressAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingText() {
    return AnimatedBuilder(
      animation: _progressController,
      builder: (context, child) {
        return Text(
          'Loading amazing features...',
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        );
      },
    );
  }

  Widget _buildBottomBranding() {
    return Positioned(
      bottom: 40,
      left: 0,
      right: 0,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Column(
              children: [
                Text(
                  'Powered by Projek',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'v1.0.0',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
