import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/models/gamification_models.dart';
import '../../../wallet/domain/models/working_wallet_models.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';

// Reward types for gamification system
enum RewardType {
  signup,
  dailyCheckin,
  milestone,
  referral,
  achievement,
  spin,
  purchase,
  review,
}

class GamificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const Uuid _uuid = Uuid();

  static const String _profilesCollection = 'user_profiles';
  static const String _achievementsCollection = 'achievements';
  static const String _leaderboardCollection = 'leaderboard';
  static const String _referralsCollection = 'referral_programs';
  static const String _gameSessionsCollection = 'game_sessions';

  // Initialize user profile
  static Future<UserProfile> initializeUserProfile({
    required String userId,
    required String username,
    String? avatarUrl,
  }) async {
    try {
      final doc = await _firestore
          .collection(_profilesCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserProfile.fromJson(doc.data()!);
      }

      // Create new profile
      final profile = UserProfile(
        userId: userId,
        username: username,
        avatarUrl: avatarUrl,
        lastLoginDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(_profilesCollection)
          .doc(userId)
          .set(profile.toJson());

      // Initialize achievements
      await _initializeAchievements(userId);

      // Initialize referral program
      await _initializeReferralProgram(userId);

      // Award welcome bonus
      await _awardWelcomeBonus(userId);

      return profile;
    } catch (e) {
      debugPrint('❌ Error initializing user profile: $e');
      throw Exception('Failed to initialize user profile');
    }
  }

  // Update user experience and level
  static Future<UserProfile> updateExperience({
    required String userId,
    required int experienceGained,
    String? source,
  }) async {
    try {
      final profileRef = _firestore.collection(_profilesCollection).doc(userId);

      return await _firestore.runTransaction((transaction) async {
        final profileDoc = await transaction.get(profileRef);
        final profile = UserProfile.fromJson(profileDoc.data()!);

        int newExperience = profile.experience + experienceGained;
        int newLevel = profile.level;
        int experienceToNext = profile.experienceToNextLevel;

        // Check for level up
        while (newExperience >= experienceToNext) {
          newExperience -= experienceToNext;
          newLevel++;
          experienceToNext = _calculateExperienceForLevel(newLevel + 1);

          // Award level up bonus
          await _awardLevelUpBonus(userId, newLevel);
        }

        // Create updated profile manually since copyWith isn't defined
        final updatedProfile = UserProfile(
          userId: profile.userId,
          username: profile.username,
          avatarUrl: profile.avatarUrl,
          loyaltyTier: profile.loyaltyTier,
          totalPoints: profile.totalPoints + experienceGained,
          level: newLevel,
          experience: newExperience,
          experienceToNextLevel: experienceToNext,
          totalOrders: profile.totalOrders,
          totalSpent: profile.totalSpent,
          referralCount: profile.referralCount,
          loginStreak: profile.loginStreak,
          maxLoginStreak: profile.maxLoginStreak,
          lastLoginDate: profile.lastLoginDate,
          unlockedAchievements: profile.unlockedAchievements,
          stats: profile.stats,
          createdAt: profile.createdAt,
          updatedAt: DateTime.now(),
        );

        transaction.update(profileRef, updatedProfile.toJson());

        // Log analytics
        await AnalyticsService.logEvent('experience_gained', {
          'user_id': userId,
          'experience_gained': experienceGained,
          'new_level': newLevel,
          'source': source ?? 'unknown',
        });

        return updatedProfile;
      });
    } catch (e) {
      debugPrint('❌ Error updating experience: $e');
      throw Exception('Failed to update experience');
    }
  }

  // Track user action for achievements
  static Future<List<Achievement>> trackUserAction({
    required String userId,
    required AchievementType actionType,
    int value = 1,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final unlockedAchievements = <Achievement>[];

      // Get user's achievements
      final achievementsQuery = await _firestore
          .collection(_achievementsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: actionType.toString())
          .where('isUnlocked', isEqualTo: false)
          .get();

      for (final doc in achievementsQuery.docs) {
        final achievement = Achievement.fromJson(doc.data());
        final newProgress = achievement.currentProgress + value;

        if (newProgress >= achievement.targetValue && !achievement.isUnlocked) {
          // Unlock achievement
          final unlockedAchievement = achievement.copyWith(
            currentProgress: newProgress,
            isUnlocked: true,
            unlockedAt: DateTime.now(),
          );

          await doc.reference.update(unlockedAchievement.toJson());
          unlockedAchievements.add(unlockedAchievement);

          // Award achievement reward
          await WorkingWalletService.addReward(
            userId: userId,
            type: RewardType.milestone,
            amount: achievement.rewardAmount,
            title: 'Achievement Unlocked!',
            description: achievement.title,
            metadata: {'achievementId': achievement.id},
          );

          // Send notification
          await NotificationService.showLocalNotification(
            title: '🏆 Achievement Unlocked!',
            body: '${achievement.title} - ${achievement.formattedReward}',
            payload: achievement.id,
          );

          // Award experience
          await updateExperience(
            userId: userId,
            experienceGained: (achievement.rewardAmount * 0.1).round(),
            source: 'achievement_${achievement.type}',
          );
        } else {
          // Update progress
          await doc.reference.update({'currentProgress': newProgress});
        }
      }

      return unlockedAchievements;
    } catch (e) {
      debugPrint('❌ Error tracking user action: $e');
      return [];
    }
  }

  // Update login streak
  static Future<UserProfile> updateLoginStreak(String userId) async {
    try {
      final profileRef = _firestore.collection(_profilesCollection).doc(userId);

      return await _firestore.runTransaction((transaction) async {
        final profileDoc = await transaction.get(profileRef);
        final profile = UserProfile.fromJson(profileDoc.data()!);

        final now = DateTime.now();
        final lastLogin = profile.lastLoginDate;
        final daysDifference = now.difference(lastLogin).inDays;

        int newStreak = profile.loginStreak;
        int newMaxStreak = profile.maxLoginStreak;

        if (daysDifference == 1) {
          // Consecutive day
          newStreak++;
          newMaxStreak = max(newMaxStreak, newStreak);

          // Award daily login reward
          await _awardDailyLoginReward(userId, newStreak);
        } else if (daysDifference > 1) {
          // Streak broken
          newStreak = 1;
        }
        // If daysDifference == 0, same day login, no change

        // Create updated profile manually since copyWith isn't defined
        final updatedProfile = UserProfile(
          userId: profile.userId,
          username: profile.username,
          avatarUrl: profile.avatarUrl,
          loyaltyTier: profile.loyaltyTier,
          totalPoints: profile.totalPoints,
          level: profile.level,
          experience: profile.experience,
          experienceToNextLevel: profile.experienceToNextLevel,
          totalOrders: profile.totalOrders,
          totalSpent: profile.totalSpent,
          referralCount: profile.referralCount,
          loginStreak: newStreak,
          maxLoginStreak: newMaxStreak,
          lastLoginDate: now,
          unlockedAchievements: profile.unlockedAchievements,
          stats: profile.stats,
          createdAt: profile.createdAt,
          updatedAt: now,
        );

        transaction.update(profileRef, updatedProfile.toJson());

        // Track achievement
        await trackUserAction(
          userId: userId,
          actionType: AchievementType.loginStreak,
          value: newStreak,
        );

        return updatedProfile;
      });
    } catch (e) {
      debugPrint('❌ Error updating login streak: $e');
      throw Exception('Failed to update login streak');
    }
  }

  // Generate referral code
  static Future<ReferralProgram> generateReferralCode(String userId) async {
    try {
      final doc = await _firestore
          .collection(_referralsCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return ReferralProgram.fromJson(doc.data()!);
      }

      final referralCode = _generateUniqueReferralCode(userId);

      final referralProgram = ReferralProgram(
        userId: userId,
        referralCode: referralCode,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(_referralsCollection)
          .doc(userId)
          .set(referralProgram.toJson());

      return referralProgram;
    } catch (e) {
      debugPrint('❌ Error generating referral code: $e');
      throw Exception('Failed to generate referral code');
    }
  }

  // Process referral
  static Future<bool> processReferral({
    required String referralCode,
    required String newUserId,
    required String newUsername,
  }) async {
    try {
      // Find referrer by code
      final referrerQuery = await _firestore
          .collection(_referralsCollection)
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (referrerQuery.docs.isEmpty) {
        return false; // Invalid referral code
      }

      final referrerDoc = referrerQuery.docs.first;
      final referralProgram = ReferralProgram.fromJson(referrerDoc.data());
      final referrerId = referralProgram.userId;

      // Check if user already used a referral
      final existingReferral = await _firestore
          .collection(_referralsCollection)
          .where('referrals', arrayContains: {'referredUserId': newUserId})
          .get();

      if (existingReferral.docs.isNotEmpty) {
        return false; // User already referred
      }

      // Create referral entry
      final referralEntry = ReferralEntry(
        referredUserId: newUserId,
        referredUsername: newUsername,
        referredAt: DateTime.now(),
        isSuccessful: true,
        rewardEarned: 50.0, // ₹50 referral bonus
      );

      // Update referrer's program
      await referrerDoc.reference.update({
        'totalReferrals': FieldValue.increment(1),
        'successfulReferrals': FieldValue.increment(1),
        'totalEarnings': FieldValue.increment(50.0),
        'referrals': FieldValue.arrayUnion([referralEntry.toJson()]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Award referral bonus to referrer
      await WorkingWalletService.addReward(
        userId: referrerId,
        type: RewardType.referral,
        amount: 50.0,
        title: 'Referral Bonus',
        description: 'You earned ₹50 for referring $newUsername!',
        metadata: {'referredUserId': newUserId},
      );

      // Award welcome bonus to new user
      await WorkingWalletService.addReward(
        userId: newUserId,
        type: RewardType.signup,
        amount: 25.0,
        title: 'Welcome Bonus',
        description: 'Welcome to Projek! You got ₹25 for joining via referral.',
        metadata: {'referrerId': referrerId},
      );

      // Track achievements
      await trackUserAction(
        userId: referrerId,
        actionType: AchievementType.referralMilestone,
        value: 1,
      );

      return true;
    } catch (e) {
      debugPrint('❌ Error processing referral: $e');
      return false;
    }
  }

  // Get leaderboard
  static Future<List<LeaderboardEntry>> getLeaderboard({
    String period = 'monthly',
    int limit = 50,
  }) async {
    try {
      final query = await _firestore
          .collection(_leaderboardCollection)
          .where('period', isEqualTo: period)
          .orderBy('score', descending: true)
          .limit(limit)
          .get();

      return query.docs
          .map((doc) => LeaderboardEntry.fromJson(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting leaderboard: $e');
      return [];
    }
  }

  // Record game session
  static Future<GameSession> recordGameSession({
    required String userId,
    required GameType gameType,
    required double entryFee,
    required double winAmount,
    Map<String, dynamic>? gameData,
  }) async {
    try {
      final session = GameSession(
        id: _uuid.v4(),
        userId: userId,
        gameType: gameType,
        entryFee: entryFee,
        winAmount: winAmount,
        isWin: winAmount > entryFee,
        playedAt: DateTime.now(),
        gameData: gameData ?? {},
      );

      await _firestore
          .collection(_gameSessionsCollection)
          .doc(session.id)
          .set(session.toJson());

      // Award experience for playing
      await updateExperience(
        userId: userId,
        experienceGained: 5,
        source: 'game_${gameType.toString()}',
      );

      // Track achievements
      if (session.isWin) {
        await trackUserAction(
          userId: userId,
          actionType: AchievementType.gameWins,
          value: 1,
        );
      }

      return session;
    } catch (e) {
      debugPrint('❌ Error recording game session: $e');
      throw Exception('Failed to record game session');
    }
  }

  // Helper methods
  static Future<void> _initializeAchievements(String userId) async {
    final achievements = _getDefaultAchievements(userId);

    final batch = _firestore.batch();
    for (final achievement in achievements) {
      final ref = _firestore
          .collection(_achievementsCollection)
          .doc(achievement.id);
      batch.set(ref, achievement.toJson());
    }
    await batch.commit();
  }

  static Future<void> _initializeReferralProgram(String userId) async {
    final referralCode = _generateUniqueReferralCode(userId);

    final referralProgram = ReferralProgram(
      userId: userId,
      referralCode: referralCode,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _firestore
        .collection(_referralsCollection)
        .doc(userId)
        .set(referralProgram.toJson());
  }

  static Future<void> _awardWelcomeBonus(String userId) async {
    await WorkingWalletService.addReward(
      userId: userId,
      type: RewardType.signup,
      amount: 100.0,
      title: 'Welcome to Projek!',
      description: 'Enjoy 100 ProjekCoins as a welcome gift!',
    );
  }

  static Future<void> _awardLevelUpBonus(String userId, int newLevel) async {
    final bonus = newLevel * 10.0; // 10 PC per level

    await WorkingWalletService.addReward(
      userId: userId,
      type: RewardType.milestone,
      amount: bonus,
      title: 'Level Up!',
      description: 'Congratulations on reaching Level $newLevel!',
      metadata: {'level': newLevel},
    );

    await NotificationService.showLocalNotification(
      title: '🎉 Level Up!',
      body:
          'You reached Level $newLevel! Earned ${bonus.toStringAsFixed(0)} PC',
      payload: 'level_up_$newLevel',
    );
  }

  static Future<void> _awardDailyLoginReward(String userId, int streak) async {
    final bonus = min(streak * 5.0, 50.0); // Max 50 PC per day

    await WorkingWalletService.addReward(
      userId: userId,
      type: RewardType.dailyCheckin,
      amount: bonus,
      title: 'Daily Login Bonus',
      description: 'Day $streak login streak! Keep it up!',
      metadata: {'streak': streak},
    );
  }

  static int _calculateExperienceForLevel(int level) {
    return (level * 100 * 1.2).round(); // Exponential growth
  }

  static String _generateUniqueReferralCode(String userId) {
    final hash = sha256.convert(
      utf8.encode(userId + DateTime.now().toString()),
    );
    return hash.toString().substring(0, 8).toUpperCase();
  }

  static List<Achievement> _getDefaultAchievements(String userId) {
    return [
      Achievement(
        id: '${userId}_first_order',
        type: AchievementType.firstOrder,
        title: 'First Order',
        description: 'Place your first order',
        icon: '🛍️',
        rewardAmount: 50.0,
        targetValue: 1,
      ),
      Achievement(
        id: '${userId}_order_milestone_10',
        type: AchievementType.orderMilestone,
        title: 'Regular Customer',
        description: 'Place 10 orders',
        icon: '📦',
        rewardAmount: 100.0,
        targetValue: 10,
      ),
      Achievement(
        id: '${userId}_spending_1000',
        type: AchievementType.spendingMilestone,
        title: 'Big Spender',
        description: 'Spend ₹1000 in total',
        icon: '💰',
        rewardAmount: 200.0,
        targetValue: 1000,
      ),
      Achievement(
        id: '${userId}_referral_5',
        type: AchievementType.referralMilestone,
        title: 'Social Butterfly',
        description: 'Refer 5 friends',
        icon: '👥',
        rewardAmount: 250.0,
        targetValue: 5,
      ),
      Achievement(
        id: '${userId}_login_streak_7',
        type: AchievementType.loginStreak,
        title: 'Week Warrior',
        description: 'Login for 7 consecutive days',
        icon: '🔥',
        rewardAmount: 150.0,
        targetValue: 7,
      ),
    ];
  }
}

class WorkingWalletService {
  static Future<void> addReward({
    required String userId,
    required RewardType type,
    required double amount,
    required String title,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    // Implement reward addition to user's wallet
   userId.isNotEmpty ? userId : FirebaseAuth.instance.currentUser!.uid;
      
      final FirebaseFirestore firestore = FirebaseFirestore.instance; try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null && userId.isEmpty) throw Exception('User not authenticated');
      
      final uid = userId;
      
      await firestore.runTransaction((transaction) async {
        final walletRef = firestore.collection('wallets').doc(uid);
        final walletDoc = await transaction.get(walletRef);
        
        final currentBalance = walletDoc.exists 
            ? (walletDoc.data()?['projekCoinBalance'] ?? 0.0).toDouble()
            : 0.0;
        
        final newBalance = currentBalance + amount;
        
        transaction.set(walletRef, {
          'userId': uid,
          'projekCoinBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = firestore.collection('transactions').doc();
        transaction.set(transactionRef, {
          'userId': uid,
          'type': 'credit',
          'walletType': 'projekCoin',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'title': title,
          'rewardType': type.toString(),
          'metadata': metadata,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });
    } catch (e) {
      debugPrint('❌ Error adding reward: $e');
      throw Exception('Failed to add reward');
    }
  }
}















