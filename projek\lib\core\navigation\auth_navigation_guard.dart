import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';

import '../providers/auth_provider.dart';
import '../utils/app_routes.dart';

/// Enhanced navigation guard that properly handles Firebase authentication state
class AuthNavigationGuard {
  /// Check if user is authenticated
  static bool isAuthenticated() {
    return FirebaseAuth.instance.currentUser != null;
  }

  /// Check if the current route requires authentication
  static bool requiresAuth(String path) {
    const protectedPaths = [
      '/home',
      '/profile',
      '/wallet',
      '/bookings',
      '/orders',
      '/cart',
      '/wishlist',
      '/edit-profile',
      '/settings',
    ];

    return protectedPaths.any(
      (protectedPath) => path.startsWith(protectedPath),
    );
  }

  /// Check if the current route is an authentication page
  static bool isAuthPage(String path) {
    const authPaths = [
      '/login',
      '/register',
      '/forgot-password',
      '/auth/phone',
      '/uid-registration',
      '/otp-verification',
    ];

    return authPaths.any((authPath) => path.startsWith(authPath));
  }

  /// Check if the current route is a public page
  static bool isPublicPage(String path) {
    const publicPaths = [
      '/',
      '/splash',
      '/onboarding',
      '/marketplace',
      '/search',
      '/categories',
      '/product',
      '/help',
      '/about',
    ];

    return publicPaths.any(
          (publicPath) => path == publicPath || path.startsWith(publicPath),
        ) ||
        isAuthPage(path);
  }

  /// Handle navigation redirect based on auth state
  static String? handleRedirect(String path) {
    final isLoggedIn = isAuthenticated();
    final needsAuth = requiresAuth(path);
    final isAuth = isAuthPage(path);

    // If not logged in and trying to access protected page, redirect to login
    if (!isLoggedIn && needsAuth) {
      return AppRoutes.login;
    }

    // If logged in and on auth page, redirect to home
    if (isLoggedIn && isAuth) {
      return AppRoutes.home;
    }

    // If accessing root, redirect appropriately
    if (path == '/') {
      return isLoggedIn ? AppRoutes.home : AppRoutes.splash;
    }

    return null; // No redirect needed
  }

  /// Navigate to login page
  static void navigateToLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }

  /// Navigate to home page
  static void navigateToHome(BuildContext context) {
    context.go(AppRoutes.home);
  }

  /// Navigate after successful authentication
  static void navigateAfterAuth(BuildContext context, {String? returnTo}) {
    if (returnTo != null && isPublicPage(returnTo)) {
      context.go(returnTo);
    } else {
      context.go(AppRoutes.home);
    }
  }

  /// Handle sign out navigation
  static void handleSignOut(BuildContext context) {
    context.go(AppRoutes.login);
  }
}

/// Widget that listens to auth state changes and handles navigation
class AuthNavigationListener extends ConsumerWidget {
  final Widget child;

  const AuthNavigationListener({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to auth state changes
    ref.listen<AsyncValue<User?>>(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          final currentPath = GoRouterState.of(context).uri.path;

          if (user == null) {
            // User signed out, redirect to login if on protected page
            if (AuthNavigationGuard.requiresAuth(currentPath)) {
              AuthNavigationGuard.navigateToLogin(context);
            }
          } else {
            // User signed in, redirect from auth pages to home
            if (AuthNavigationGuard.isAuthPage(currentPath)) {
              AuthNavigationGuard.navigateToHome(context);
            }
          }
        },
        loading: () {
          // Handle loading state if needed
        },
        error: (error, stack) {
          // Handle auth errors
          debugPrint('Auth error: $error');
          if (AuthNavigationGuard.requiresAuth(
            GoRouterState.of(context).uri.path,
          )) {
            AuthNavigationGuard.navigateToLogin(context);
          }
        },
      );
    });

    return child;
  }
}

/// Enhanced router provider with proper Firebase auth integration
final enhancedRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      return authState.when(
        data: (user) {
          final isLoggedIn = user != null;
          final path = state.uri.path;

          // Handle redirect logic
          if (!isLoggedIn && AuthNavigationGuard.requiresAuth(path)) {
            return AppRoutes.login;
          }

          if (isLoggedIn && AuthNavigationGuard.isAuthPage(path)) {
            return AppRoutes.home;
          }

          if (path == '/') {
            return isLoggedIn ? AppRoutes.home : AppRoutes.splash;
          }

          return null;
        },
        loading: () {
          // Show splash while loading auth state
          return state.uri.path == AppRoutes.splash ? null : AppRoutes.splash;
        },
        error: (error, stack) {
          // On auth error, redirect to login
          return AppRoutes.login;
        },
      );
    },
    refreshListenable: GoRouterRefreshStream(
      FirebaseAuth.instance.authStateChanges(),
    ),
    routes: [
      // Add your routes here - they will be properly protected
    ],
  );
});

/// Helper class to refresh router on auth state changes
class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen(
      (dynamic _) => notifyListeners(),
    );
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

/// Extension methods for easier navigation
extension AuthNavigationExtension on BuildContext {
  /// Navigate to login with return path
  void goToLogin({String? returnTo}) {
    if (returnTo != null) {
      go('${AppRoutes.login}?returnTo=${Uri.encodeComponent(returnTo)}');
    } else {
      go(AppRoutes.login);
    }
  }

  /// Navigate to home
  void goToHome() {
    go(AppRoutes.home);
  }

  /// Navigate after authentication
  void goAfterAuth({String? returnTo}) {
    AuthNavigationGuard.navigateAfterAuth(this, returnTo: returnTo);
  }

  /// Check if current user is authenticated
  bool get isAuthenticated => AuthNavigationGuard.isAuthenticated();

  /// Get current route path
  String get currentPath => GoRouterState.of(this).uri.path;
}
