import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/user/presentation/pages/splash_page.dart';
import '../../features/user/presentation/pages/onboarding_page.dart';
import '../../features/user/presentation/pages/auth/login_page.dart';
import '../../features/user/presentation/pages/auth/register_page.dart';
import '../../features/user/presentation/pages/auth/otp_verification_page.dart';
import '../../features/user/presentation/pages/home/<USER>';
import '../../features/user/presentation/pages/home/<USER>';
import '../../features/user/presentation/pages/wallet/wallet_page.dart';
import '../../features/user/presentation/pages/marketplace/marketplace_page.dart';
import '../../features/user/presentation/pages/profile/profile_page.dart';
import '../../demo/wallet_demo_page.dart';
import '../../demo/demo_launcher.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      // Splash & Onboarding
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const UserSplashPage(),
      ),
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const UserOnboardingPage(),
      ),

      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const UserLoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const UserRegisterPage(),
      ),
      GoRoute(
        path: '/otp-verification',
        name: 'otp-verification',
        builder: (context, state) {
          final phoneNumber = state.extra as String? ?? '';
          return UserOTPVerificationPage(phoneNumber: phoneNumber);
        },
      ),

      // Main App
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const UserHomePage(),
        routes: [
          GoRoute(
            path: 'dashboard',
            name: 'dashboard',
            builder: (context, state) => const UserDashboardPage(),
          ),
          GoRoute(
            path: 'wallet',
            name: 'wallet',
            builder: (context, state) => const UserWalletPage(),
          ),
          GoRoute(
            path: 'marketplace',
            name: 'marketplace',
            builder: (context, state) => const UserMarketplacePage(),
          ),
          GoRoute(
            path: 'profile',
            name: 'profile',
            builder: (context, state) => const UserProfilePage(),
          ),
        ],
      ),

      // Wallet Routes
      GoRoute(
        path: '/wallet',
        name: 'wallet-standalone',
        builder: (context, state) => const UserWalletPage(),
        routes: [
          GoRoute(
            path: 'topup',
            name: 'wallet-topup',
            builder: (context, state) =>
                const Placeholder(), // UserWalletTopupPage(),
          ),
          GoRoute(
            path: 'withdraw',
            name: 'wallet-withdraw',
            builder: (context, state) =>
                const Placeholder(), // UserWalletWithdrawPage(),
          ),
          GoRoute(
            path: 'history',
            name: 'wallet-history',
            builder: (context, state) =>
                const Placeholder(), // UserTransactionHistoryPage(),
          ),
          GoRoute(
            path: 'demo',
            name: 'wallet-demo',
            builder: (context, state) => const WalletDemoPage(),
          ),
        ],
      ),

      // Marketplace Routes
      GoRoute(
        path: '/marketplace',
        name: 'marketplace-standalone',
        builder: (context, state) => const UserMarketplacePage(),
        routes: [
          GoRoute(
            path: 'product/:productId',
            name: 'product-detail',
            builder: (context, state) {
              // final productId = state.pathParameters['productId']!;
              return const Placeholder(); // UserProductDetailPage will be implemented
            },
          ),
          GoRoute(
            path: 'cart',
            name: 'cart',
            builder: (context, state) => const Placeholder(), // UserCartPage(),
          ),
          GoRoute(
            path: 'checkout',
            name: 'checkout',
            builder: (context, state) =>
                const Placeholder(), // UserCheckoutPage(),
          ),
        ],
      ),

      // Bookings Routes
      GoRoute(
        path: '/bookings',
        name: 'bookings',
        builder: (context, state) => const Placeholder(), // UserBookingsPage(),
        routes: [
          GoRoute(
            path: 'cab',
            name: 'cab-booking',
            builder: (context, state) =>
                const Placeholder(), // UserCabBookingPage(),
          ),
          GoRoute(
            path: 'food',
            name: 'food-booking',
            builder: (context, state) =>
                const Placeholder(), // UserFoodBookingPage(),
          ),
          GoRoute(
            path: 'service',
            name: 'service-booking',
            builder: (context, state) =>
                const Placeholder(), // UserServiceBookingPage(),
          ),
          GoRoute(
            path: 'history',
            name: 'booking-history',
            builder: (context, state) =>
                const Placeholder(), // UserBookingHistoryPage(),
          ),
        ],
      ),

      // Education Routes
      GoRoute(
        path: '/education',
        name: 'education',
        builder: (context, state) =>
            const Placeholder(), // UserEducationPage(),
        routes: [
          GoRoute(
            path: 'courses',
            name: 'courses',
            builder: (context, state) =>
                const Placeholder(), // UserCoursesPage(),
          ),
          GoRoute(
            path: 'course/:courseId',
            name: 'course-detail',
            builder: (context, state) {
              // final courseId = state.pathParameters['courseId']!;
              return const Placeholder(); // UserCourseDetailPage will be implemented
            },
          ),
          GoRoute(
            path: 'my-courses',
            name: 'my-courses',
            builder: (context, state) =>
                const Placeholder(), // UserMyCoursesPage(),
          ),
        ],
      ),

      // Bills & Recharge Routes
      GoRoute(
        path: '/bills',
        name: 'bills',
        builder: (context, state) => const Placeholder(), // UserBillsPage(),
        routes: [
          GoRoute(
            path: 'payment',
            name: 'bill-payment',
            builder: (context, state) =>
                const Placeholder(), // UserBillPaymentPage(),
          ),
          GoRoute(
            path: 'recharge',
            name: 'recharge',
            builder: (context, state) =>
                const Placeholder(), // UserRechargePage(),
          ),
          GoRoute(
            path: 'history',
            name: 'payment-history',
            builder: (context, state) =>
                const Placeholder(), // UserPaymentHistoryPage(),
          ),
        ],
      ),

      // Travel Routes
      GoRoute(
        path: '/travel',
        name: 'travel',
        builder: (context, state) => const Placeholder(), // UserTravelPage(),
        routes: [
          GoRoute(
            path: 'booking',
            name: 'travel-booking',
            builder: (context, state) =>
                const Placeholder(), // UserTravelBookingPage(),
          ),
          GoRoute(
            path: 'history',
            name: 'travel-history',
            builder: (context, state) =>
                const Placeholder(), // UserTravelHistoryPage(),
          ),
        ],
      ),

      // Insurance Routes
      GoRoute(
        path: '/insurance',
        name: 'insurance',
        builder: (context, state) =>
            const Placeholder(), // UserInsurancePage(),
        routes: [
          GoRoute(
            path: 'policy/:policyId',
            name: 'policy-detail',
            builder: (context, state) {
              // final policyId = state.pathParameters['policyId']!;
              return const Placeholder(); // UserPolicyDetailPage will be implemented
            },
          ),
        ],
      ),

      // Demo Routes
      GoRoute(
        path: '/demo',
        name: 'demo',
        builder: (context, state) => const DemoLauncher(),
      ),

      // Profile & Settings Routes
      GoRoute(
        path: '/profile',
        name: 'profile-standalone',
        builder: (context, state) => const UserProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) =>
                const Placeholder(), // UserSettingsPage(),
          ),
          GoRoute(
            path: 'help',
            name: 'help',
            builder: (context, state) => const Placeholder(), // UserHelpPage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Page not found: ${state.uri}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods
class AppNavigation {
  static void goToSplash(BuildContext context) => context.go('/splash');
  static void goToOnboarding(BuildContext context) => context.go('/onboarding');
  static void goToLogin(BuildContext context) => context.go('/login');
  static void goToRegister(BuildContext context) => context.go('/register');
  static void goToOTPVerification(BuildContext context, String phoneNumber) =>
      context.go('/otp-verification', extra: phoneNumber);
  static void goToHome(BuildContext context) => context.go('/home');
  static void goToDashboard(BuildContext context) =>
      context.go('/home/<USER>');
  static void goToWallet(BuildContext context) => context.go('/wallet');
  static void goToMarketplace(BuildContext context) =>
      context.go('/marketplace');
  static void goToProfile(BuildContext context) => context.go('/profile');
  static void goToWalletDemo(BuildContext context) =>
      context.go('/wallet/demo');
  static void goToDemo(BuildContext context) => context.go('/demo');
}

