import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:projek/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('E-commerce App Integration Tests', () {
    testWidgets('App launches and shows home page', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Verify app launches
      expect(find.text('Projek'), findsOneWidget);
    });

    testWidgets('Navigation between pages works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test navigation to cart
      final cartIcon = find.byIcon(Icons.shopping_cart);
      if (cartIcon.evaluate().isNotEmpty) {
        await tester.tap(cartIcon);
        await tester.pumpAndSettle();
      }

      // Test navigation to wishlist
      final wishlistIcon = find.byIcon(Icons.favorite);
      if (wishlistIcon.evaluate().isNotEmpty) {
        await tester.tap(wishlistIcon);
        await tester.pumpAndSettle();
      }

      // Test navigation to profile
      final profileIcon = find.byIcon(Icons.person);
      if (profileIcon.evaluate().isNotEmpty) {
        await tester.tap(profileIcon);
        await tester.pumpAndSettle();
      }
    });

    testWidgets('Cart functionality works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to home and look for add to cart buttons
      final addToCartButtons = find.text('Add to Cart');
      if (addToCartButtons.evaluate().isNotEmpty) {
        await tester.tap(addToCartButtons.first);
        await tester.pumpAndSettle();

        // Verify cart badge updates
        expect(find.text('1'), findsWidgets);
      }
    });

    testWidgets('Wishlist functionality works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Look for wishlist/favorite buttons
      final favoriteButtons = find.byIcon(Icons.favorite_border);
      if (favoriteButtons.evaluate().isNotEmpty) {
        await tester.tap(favoriteButtons.first);
        await tester.pumpAndSettle();

        // Verify wishlist badge updates
        expect(find.byIcon(Icons.favorite), findsWidgets);
      }
    });

    testWidgets('Product detail page navigation works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Look for product cards or images to tap
      final productCards = find.byType(Card);
      if (productCards.evaluate().isNotEmpty) {
        await tester.tap(productCards.first);
        await tester.pumpAndSettle();

        // Verify we're on product detail page
        expect(find.text('Product Details'), findsOneWidget);
      }
    });

    testWidgets('Payment methods are available', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to cart if it has items
      final cartIcon = find.byIcon(Icons.shopping_cart);
      if (cartIcon.evaluate().isNotEmpty) {
        await tester.tap(cartIcon);
        await tester.pumpAndSettle();

        // Look for checkout or payment buttons
        final checkoutButtons = find.text('Checkout');
        if (checkoutButtons.evaluate().isNotEmpty) {
          await tester.tap(checkoutButtons.first);
          await tester.pumpAndSettle();

          // Verify payment methods are shown
          expect(find.text('Google Pay'), findsWidgets);
          expect(find.text('PhonePe'), findsWidgets);
          expect(find.text('Paytm'), findsWidgets);
          expect(find.text('Cash on Delivery'), findsWidgets);
        }
      }
    });
  });
}
