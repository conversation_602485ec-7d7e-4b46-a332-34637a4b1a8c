@echo off
title Quick Deploy - Projek User App
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    QUICK DEPLOY - USER APP                  ║
echo ║              Fast Build & Install to Device                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Quick environment setup
set PUB_CACHE=E:\Appdata\flutter_pub_cache

echo 📱 Quick device check...
adb -s 1397182984001HG shell echo "Device ready" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Device not ready. Running full setup...
    call build_and_deploy_user.bat
    exit /b
)
echo ✅ Device V2130 ready

echo.
echo 🚀 Quick build and deploy...
flutter build apk --target lib/main_user.dart --debug
if %errorlevel% neq 0 (
    echo ❌ Build failed. Running full setup...
    call build_and_deploy_user.bat
    exit /b
)

echo 📱 Installing...
adb -s 1397182984001HG install -r build\app\outputs\flutter-apk\app-debug.apk
if %errorlevel% neq 0 (
    echo ❌ Install failed. Trying full process...
    call build_and_deploy_user.bat
    exit /b
)

echo.
echo ✅ SUCCESS! Projek User app installed on V2130
echo 📱 Launch the app from your device home screen
echo.
pause
