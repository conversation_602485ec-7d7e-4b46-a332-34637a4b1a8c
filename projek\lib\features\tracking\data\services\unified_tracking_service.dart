import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import '../../domain/models/unified_order.dart';
import '../../../../core/services/multi_app_integration_service.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';

class UnifiedTrackingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _ordersCollection = 'unified_orders';
  static const String _trackingCollection = 'real_time_tracking';
  
  static Timer? _locationUpdateTimer;
  static StreamSubscription<Position>? _positionSubscription;
  
  // Create unified order from marketplace order
  static Future<UnifiedOrder> createFromMarketplaceOrder({
    required String orderId,
    required Map<String, dynamic> orderData,
    required String sellerId,
    required String sellerName,
    required LocationPoint pickupLocation,
    required LocationPoint deliveryLocation,
  }) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final user = OrderParticipant(
        id: currentUserId,
        name: AuthService.currentUserEmail ?? 'User',
        role: 'user',
      );

      final seller = OrderParticipant(
        id: sellerId,
        name: sellerName,
        role: 'seller',
      );

      final unifiedOrder = UnifiedOrder(
        id: orderId,
        orderNumber: _generateOrderNumber(),
        type: OrderType.marketplace,
        status: OrderStatus.created,
        user: user,
        seller: seller,
        pickupLocation: pickupLocation,
        deliveryLocation: deliveryLocation,
        totalAmount: orderData['totalAmount']?.toDouble() ?? 0.0,
        deliveryFee: orderData['deliveryFee']?.toDouble() ?? 0.0,
        taxes: orderData['taxes']?.toDouble() ?? 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        orderData: orderData,
        milestones: [
          OrderMilestone(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            status: OrderStatus.created,
            timestamp: DateTime.now(),
            description: 'Order placed successfully',
            performedBy: currentUserId,
          ),
        ],
      );

      await _firestore.collection(_ordersCollection).doc(orderId).set(unifiedOrder.toJson());

      // Send cross-app event to seller
      await MultiAppIntegrationService.sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.orderCreated,
        sourceApp: 'user',
        targetApp: 'seller',
        userId: currentUserId,
        sellerId: sellerId,
        data: {'orderId': orderId, 'orderData': orderData},
        timestamp: DateTime.now(),
        isUrgent: true,
      ));

      await AnalyticsService.logEvent('unified_order_created', {
        'order_id': orderId,
        'order_type': 'marketplace',
        'total_amount': unifiedOrder.totalAmount,
      });

      return unifiedOrder;
    } catch (e) {
      debugPrint('❌ Error creating unified order: $e');
      throw Exception('Failed to create unified order');
    }
  }

  // Create unified order from service booking
  static Future<UnifiedOrder> createFromServiceBooking({
    required String bookingId,
    required Map<String, dynamic> bookingData,
    required String providerId,
    required String providerName,
    required LocationPoint serviceLocation,
  }) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final user = OrderParticipant(
        id: currentUserId,
        name: AuthService.currentUserEmail ?? 'User',
        role: 'user',
      );

      final seller = OrderParticipant(
        id: providerId,
        name: providerName,
        role: 'seller',
      );

      final unifiedOrder = UnifiedOrder(
        id: bookingId,
        orderNumber: _generateOrderNumber(),
        type: OrderType.service,
        status: OrderStatus.created,
        user: user,
        seller: seller,
        pickupLocation: serviceLocation, // Service provider location
        deliveryLocation: serviceLocation, // Service delivery location
        totalAmount: bookingData['totalAmount']?.toDouble() ?? 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        orderData: bookingData,
        milestones: [
          OrderMilestone(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            status: OrderStatus.created,
            timestamp: DateTime.now(),
            description: 'Service booked successfully',
            performedBy: currentUserId,
          ),
        ],
      );

      await _firestore.collection(_ordersCollection).doc(bookingId).set(unifiedOrder.toJson());

      // Send cross-app event to service provider
      await MultiAppIntegrationService.sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.serviceBooked,
        sourceApp: 'user',
        targetApp: 'seller',
        userId: currentUserId,
        sellerId: providerId,
        data: {'bookingId': bookingId, 'bookingData': bookingData},
        timestamp: DateTime.now(),
        isUrgent: true,
      ));

      return unifiedOrder;
    } catch (e) {
      debugPrint('❌ Error creating service order: $e');
      throw Exception('Failed to create service order');
    }
  }

  // Update order status
  static Future<UnifiedOrder> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? performedBy,
    String? description,
    LocationPoint? location,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final orderDoc = await _firestore.collection(_ordersCollection).doc(orderId).get();
      if (!orderDoc.exists) throw Exception('Order not found');

      final currentOrder = UnifiedOrder.fromJson(orderDoc.data()!);
      
      // Create new milestone
      final milestone = OrderMilestone(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        status: newStatus,
        timestamp: DateTime.now(),
        description: description ?? _getDefaultStatusDescription(newStatus),
        performedBy: performedBy ?? AuthService.currentUserId,
        location: location,
      );

      final updatedMilestones = [...currentOrder.milestones, milestone];
      
      final updateData = <String, Object>{
        'status': newStatus.toString(),
        'milestones': updatedMilestones.map((m) => m.toJson()).toList(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (additionalData != null) {
        updateData.addAll(additionalData.cast<String, Object>());
      }

      await _firestore.collection(_ordersCollection).doc(orderId).update(updateData);

      final updatedOrder = currentOrder.copyWith(
        status: newStatus,
        milestones: updatedMilestones,
        updatedAt: DateTime.now(),
      );

      // Send cross-app notifications
      await _sendStatusUpdateNotifications(updatedOrder, newStatus);

      await AnalyticsService.logEvent('order_status_updated', {
        'order_id': orderId,
        'new_status': newStatus.toString(),
        'order_type': currentOrder.type.toString(),
      });

      return updatedOrder;
    } catch (e) {
      debugPrint('❌ Error updating order status: $e');
      throw Exception('Failed to update order status');
    }
  }

  // Assign rider to order
  static Future<UnifiedOrder> assignRider({
    required String orderId,
    required String riderId,
    required String riderName,
    String? riderPhone,
    String? vehicleNumber,
  }) async {
    try {
      final rider = OrderParticipant(
        id: riderId,
        name: riderName,
        phone: riderPhone,
        role: 'rider',
        additionalInfo: {'vehicleNumber': vehicleNumber},
      );

      final tracking = DeliveryTracking(
        riderId: riderId,
        riderName: riderName,
        riderPhone: riderPhone,
        vehicleNumber: vehicleNumber,
        deliveryType: DeliveryType.standard,
      );

      await _firestore.collection(_ordersCollection).doc(orderId).update({
        'rider': rider.toJson(),
        'tracking': tracking.toJson(),
        'status': OrderStatus.assigned.toString(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      final updatedOrder = await getOrderById(orderId);

      // Send notifications to user and rider
      await _sendRiderAssignmentNotifications(updatedOrder);

      return updatedOrder;
    } catch (e) {
      debugPrint('❌ Error assigning rider: $e');
      throw Exception('Failed to assign rider');
    }
  }

  // Update rider location
  static Future<void> updateRiderLocation({
    required String orderId,
    required double latitude,
    required double longitude,
    double? speed,
    double? heading,
  }) async {
    try {
      final locationPoint = LocationPoint(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
      );

      // Update in tracking collection for real-time updates
      await _firestore.collection(_trackingCollection).doc(orderId).set({
        'currentLocation': locationPoint.toJson(),
        'speed': speed,
        'heading': heading,
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // Update in unified order
      await _firestore.collection(_ordersCollection).doc(orderId).update({
        'tracking.currentLocation': locationPoint.toJson(),
        'tracking.route': FieldValue.arrayUnion([locationPoint.toJson()]),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // Send real-time update to user
      await MultiAppIntegrationService.sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.riderLocationUpdate,
        sourceApp: 'rider',
        targetApp: 'user',
        userId: '', // Will be filled from order data
        data: {
          'orderId': orderId,
          'location': locationPoint.toJson(),
          'speed': speed,
          'heading': heading,
        },
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('❌ Error updating rider location: $e');
    }
  }

  // Start location tracking for rider
  static Future<void> startLocationTracking(String orderId) async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Start location stream
      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen((Position position) {
        updateRiderLocation(
          orderId: orderId,
          latitude: position.latitude,
          longitude: position.longitude,
          speed: position.speed,
          heading: position.heading,
        );
      });

      debugPrint('✅ Location tracking started for order: $orderId');
    } catch (e) {
      debugPrint('❌ Error starting location tracking: $e');
    }
  }

  // Stop location tracking
  static void stopLocationTracking() {
    _positionSubscription?.cancel();
    _locationUpdateTimer?.cancel();
    debugPrint('🛑 Location tracking stopped');
  }

  // Get order by ID
  static Future<UnifiedOrder> getOrderById(String orderId) async {
    try {
      final doc = await _firestore.collection(_ordersCollection).doc(orderId).get();
      if (!doc.exists) throw Exception('Order not found');
      
      return UnifiedOrder.fromJson(doc.data()!);
    } catch (e) {
      debugPrint('❌ Error getting order: $e');
      throw Exception('Failed to get order');
    }
  }

  // Get orders stream for user
  static Stream<List<UnifiedOrder>> getUserOrdersStream({
    String? userId,
    List<OrderStatus>? statusFilter,
    int limit = 20,
  }) {
    final currentUserId = userId ?? AuthService.currentUserId;
    if (currentUserId == null) return Stream.value([]);

    Query query = _firestore
        .collection(_ordersCollection)
        .where('user.id', isEqualTo: currentUserId)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => UnifiedOrder.fromJson(doc.data() as Map<String, dynamic>))
          .where((order) {
            if (statusFilter == null || statusFilter.isEmpty) return true;
            return statusFilter.contains(order.status);
          })
          .toList();
    });
  }

  // Get real-time tracking stream
  static Stream<DocumentSnapshot> getTrackingStream(String orderId) {
    return _firestore.collection(_trackingCollection).doc(orderId).snapshots();
  }

  // Get order stream
  static Stream<DocumentSnapshot> getOrderStream(String orderId) {
    return _firestore.collection(_ordersCollection).doc(orderId).snapshots();
  }

  // Helper methods
  static String _generateOrderNumber() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    return 'ORD${now.year}${now.month.toString().padLeft(2, '0')}$timestamp';
  }

  static String _getDefaultStatusDescription(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return 'Order confirmed by seller';
      case OrderStatus.preparing:
        return 'Order is being prepared';
      case OrderStatus.ready:
        return 'Order is ready for pickup';
      case OrderStatus.assigned:
        return 'Rider assigned for delivery';
      case OrderStatus.pickedUp:
        return 'Order picked up by rider';
      case OrderStatus.inTransit:
        return 'Order is on the way';
      case OrderStatus.nearDestination:
        return 'Rider is near delivery location';
      case OrderStatus.delivered:
        return 'Order delivered successfully';
      case OrderStatus.completed:
        return 'Order completed';
      default:
        return 'Order status updated';
    }
  }

  static Future<void> _sendStatusUpdateNotifications(
    UnifiedOrder order,
    OrderStatus newStatus,
  ) async {
    // Send to user
    await MultiAppIntegrationService.sendEvent(CrossAppEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: _getEventTypeFromStatus(newStatus),
      sourceApp: _getSourceAppFromStatus(newStatus),
      targetApp: 'user',
      userId: order.user.id,
      sellerId: order.seller.id,
      riderId: order.rider?.id,
      data: {
        'orderId': order.id,
        'orderNumber': order.orderNumber,
        'status': newStatus.toString(),
        'statusText': order.statusDisplayText,
      },
      timestamp: DateTime.now(),
      isUrgent: _isStatusUrgent(newStatus),
    ));
  }

  static Future<void> _sendRiderAssignmentNotifications(UnifiedOrder order) async {
    // Notify user
    await MultiAppIntegrationService.sendEvent(CrossAppEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: CrossAppEventType.orderAccepted,
      sourceApp: 'seller',
      targetApp: 'user',
      userId: order.user.id,
      riderId: order.rider?.id,
      data: {
        'orderId': order.id,
        'riderName': order.rider?.name,
        'riderPhone': order.rider?.phone,
      },
      timestamp: DateTime.now(),
      isUrgent: true,
    ));

    // Notify rider
    if (order.rider != null) {
      await MultiAppIntegrationService.sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.orderCreated,
        sourceApp: 'seller',
        targetApp: 'rider',
        userId: order.user.id,
        riderId: order.rider!.id,
        data: {
          'orderId': order.id,
          'pickupLocation': order.pickupLocation.toJson(),
          'deliveryLocation': order.deliveryLocation.toJson(),
        },
        timestamp: DateTime.now(),
        isUrgent: true,
      ));
    }
  }

  static CrossAppEventType _getEventTypeFromStatus(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return CrossAppEventType.orderAccepted;
      case OrderStatus.pickedUp:
        return CrossAppEventType.orderPickedUp;
      case OrderStatus.delivered:
        return CrossAppEventType.orderDelivered;
      default:
        return CrossAppEventType.orderCreated;
    }
  }

  static String _getSourceAppFromStatus(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
      case OrderStatus.preparing:
      case OrderStatus.ready:
        return 'seller';
      case OrderStatus.assigned:
      case OrderStatus.pickedUp:
      case OrderStatus.inTransit:
      case OrderStatus.nearDestination:
      case OrderStatus.delivered:
        return 'rider';
      default:
        return 'user';
    }
  }

  static bool _isStatusUrgent(OrderStatus status) {
    return [
      OrderStatus.confirmed,
      OrderStatus.assigned,
      OrderStatus.pickedUp,
      OrderStatus.nearDestination,
      OrderStatus.delivered,
    ].contains(status);
  }
}


