import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/config/app_config.dart';
import '../games/spin_earn_page.dart';
import '../help/help_center_page.dart';
import '../chat/chat_list_page.dart';
import '../../../../../core/theme/app_colors.dart';
import '../enhanced_dashboard.dart';

class UserHomePage extends ConsumerStatefulWidget {
  const UserHomePage({super.key});

  @override
  ConsumerState<UserHomePage> createState() => _UserHomePageState();
}

class _UserHomePageState extends ConsumerState<UserHomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const EnhancedUserDashboard(), // Replaced with enhanced dashboard
    const UserMarketplaceTab(),
    const UserWalletTab(),
    const ChatListPage(),
    const UserProfileTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: AppConfig.primaryColor,
        unselectedItemColor: AppColors.grey500,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag),
            label: 'Shop',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Wallet',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Messages'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

// Old UserDashboardTab removed - now using EnhancedUserDashboard

class UserMarketplaceTab extends ConsumerWidget {
  const UserMarketplaceTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marketplace'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Marketplace - Coming Soon!')),
    );
  }
}

class UserWalletTab extends ConsumerWidget {
  const UserWalletTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ProjekCoin Wallet'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Wallet - Coming Soon!')),
    );
  }
}

class UserProfileTab extends ConsumerWidget {
  const UserProfileTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Profile - Coming Soon!')),
    );
  }
}
