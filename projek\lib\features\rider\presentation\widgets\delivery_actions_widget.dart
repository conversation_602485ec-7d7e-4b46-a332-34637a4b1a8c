import 'package:flutter/material.dart';
import '../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class DeliveryActionsWidget extends StatelessWidget {
  final RealTimeTracking tracking;
  final VoidCallback? onStartDelivery;
  final VoidCallback? onPauseDelivery;
  final VoidCallback? onCompleteDelivery;
  final VoidCallback? onReportIssue;

  const DeliveryActionsWidget({
    super.key,
    required this.tracking,
    this.onStartDelivery,
    this.onPauseDelivery,
    this.onCompleteDelivery,
    this.onReportIssue,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Primary Action Button
        _buildPrimaryActionButton(),
        
        const SizedBox(height: 12),
        
        // Secondary Actions
        Row(
          children: [
            Expanded(
              child: _buildSecondaryButton(
                icon: Icons.pause,
                label: tracking.status == TrackingStatus.active ? 'Pause' : 'Resume',
                onPressed: tracking.status == TrackingStatus.active 
                    ? onPauseDelivery 
                    : onStartDelivery,
                color: AppColors.warning,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSecondaryButton(
                icon: Icons.report_problem,
                label: 'Report Issue',
                onPressed: onReportIssue,
                color: AppColors.error,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrimaryActionButton() {
    String label;
    IconData icon;
    Color color;
    VoidCallback? onPressed;

    switch (tracking.status) {
      case TrackingStatus.inactive:
        label = 'Start Delivery';
        icon = Icons.play_arrow;
        color = AppColors.success;
        onPressed = onStartDelivery;
        break;
      case TrackingStatus.active:
        label = 'Complete Delivery';
        icon = Icons.check_circle;
        color = AppColors.success;
        onPressed = onCompleteDelivery;
        break;
      case TrackingStatus.paused:
        label = 'Resume Delivery';
        icon = Icons.play_arrow;
        color = AppColors.primaryBlue;
        onPressed = onStartDelivery;
        break;
      case TrackingStatus.completed:
        label = 'Delivery Completed';
        icon = Icons.check_circle;
        color = AppColors.textSecondary;
        onPressed = null;
        break;
      case TrackingStatus.failed:
        label = 'Retry Delivery';
        icon = Icons.refresh;
        color = AppColors.warning;
        onPressed = onStartDelivery;
        break;
    }

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          disabledBackgroundColor: AppColors.textSecondary.withValues(alpha: 0.3),
          disabledForegroundColor: AppColors.textSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  Widget _buildSecondaryButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return SizedBox(
      height: 40,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
