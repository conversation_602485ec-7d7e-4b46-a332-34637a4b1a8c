import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../wallet/data/repositories/wallet_repository.dart';
import '../../data/services/spin_wheel_service.dart';
import '../../domain/models/game_transaction.dart';

// Wallet Repository Provider
final walletRepositoryProvider = Provider<WalletRepository>((ref) {
  return WalletRepository();
});

// Spin Wheel Service Provider
final spinWheelServiceProvider = Provider<SpinWheelService>((ref) {
  final walletRepository = ref.watch(walletRepositoryProvider);
  return SpinWheelService(walletRepository: walletRepository);
});

// Current Balance Provider
final currentBalanceProvider = FutureProvider<double>((ref) async {
  final service = ref.watch(spinWheelServiceProvider);
  return await service.getCurrentBalance();
});

// Can Afford Spin Provider
final canAffordSpinProvider = FutureProvider.family<bool, SpinType>((ref, spinType) async {
  final service = ref.watch(spinWheelServiceProvider);
  return await service.canAffordSpin(spinType);
});

// Game History Provider
final gameHistoryProvider = FutureProvider<List<GameTransaction>>((ref) async {
  final service = ref.watch(spinWheelServiceProvider);
  return await service.getGameHistory();
});

// Game Statistics Provider
final gameStatisticsProvider = FutureProvider<GameStatistics>((ref) async {
  final service = ref.watch(spinWheelServiceProvider);
  return await service.getGameStatistics();
});

// Spin Wheel State Provider
final spinWheelStateProvider = StateNotifierProvider<SpinWheelStateNotifier, SpinWheelState>((ref) {
  final service = ref.watch(spinWheelServiceProvider);
  return SpinWheelStateNotifier(service);
});

// Spin Wheel State
class SpinWheelState {
  final bool isSpinning;
  final GameTransaction? lastTransaction;
  final String? errorMessage;
  final bool isInitialized;

  const SpinWheelState({
    this.isSpinning = false,
    this.lastTransaction,
    this.errorMessage,
    this.isInitialized = false,
  });

  SpinWheelState copyWith({
    bool? isSpinning,
    GameTransaction? lastTransaction,
    String? errorMessage,
    bool? isInitialized,
  }) {
    return SpinWheelState(
      isSpinning: isSpinning ?? this.isSpinning,
      lastTransaction: lastTransaction ?? this.lastTransaction,
      errorMessage: errorMessage ?? this.errorMessage,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }
}

// Spin Wheel State Notifier
class SpinWheelStateNotifier extends StateNotifier<SpinWheelState> {
  final SpinWheelService _service;

  SpinWheelStateNotifier(this._service) : super(const SpinWheelState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _service.initialize();
      state = state.copyWith(isInitialized: true);
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to initialize game service: $e',
      );
    }
  }

  /// Play a spin with the specified type
  Future<void> playSpin(SpinType spinType) async {
    if (state.isSpinning) return;

    state = state.copyWith(
      isSpinning: true,
      errorMessage: null,
    );

    try {
      final result = await _service.playSpin(spinType: spinType);
      
      if (result.isSuccess) {
        state = state.copyWith(
          isSpinning: false,
          lastTransaction: result.transaction,
        );
      } else {
        state = state.copyWith(
          isSpinning: false,
          errorMessage: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSpinning: false,
        errorMessage: 'Unexpected error: $e',
      );
    }
  }

  /// Clear any error messages
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Reset the last transaction
  void clearLastTransaction() {
    state = state.copyWith(lastTransaction: null);
  }

  /// Check if user can afford a specific spin type
  Future<bool> canAffordSpin(SpinType spinType) async {
    try {
      return await _service.canAffordSpin(spinType);
    } catch (e) {
      return false;
    }
  }

  /// Get current wallet balance
  Future<double> getCurrentBalance() async {
    try {
      return await _service.getCurrentBalance();
    } catch (e) {
      return 0.0;
    }
  }
}

// Spin Animation Provider
final spinAnimationProvider = StateNotifierProvider<SpinAnimationNotifier, SpinAnimationState>((ref) {
  return SpinAnimationNotifier();
});

// Spin Animation State
class SpinAnimationState {
  final bool isAnimating;
  final double rotationAngle;
  final int? winningSegmentIndex;
  final Duration animationDuration;

  const SpinAnimationState({
    this.isAnimating = false,
    this.rotationAngle = 0.0,
    this.winningSegmentIndex,
    this.animationDuration = const Duration(seconds: 4),
  });

  SpinAnimationState copyWith({
    bool? isAnimating,
    double? rotationAngle,
    int? winningSegmentIndex,
    Duration? animationDuration,
  }) {
    return SpinAnimationState(
      isAnimating: isAnimating ?? this.isAnimating,
      rotationAngle: rotationAngle ?? this.rotationAngle,
      winningSegmentIndex: winningSegmentIndex ?? this.winningSegmentIndex,
      animationDuration: animationDuration ?? this.animationDuration,
    );
  }
}

// Spin Animation Notifier
class SpinAnimationNotifier extends StateNotifier<SpinAnimationState> {
  SpinAnimationNotifier() : super(const SpinAnimationState());

  /// Start spin animation with target segment
  void startSpin(int targetSegmentIndex) {
    // Calculate rotation angle to land on target segment
    final segmentAngle = 360.0 / SpinWheelConfig.segments.length;
    final targetAngle = targetSegmentIndex * segmentAngle;
    
    // Add multiple full rotations for dramatic effect
    final fullRotations = 8; // 8 full spins
    final finalAngle = (fullRotations * 360.0) + (360.0 - targetAngle);

    state = state.copyWith(
      isAnimating: true,
      rotationAngle: finalAngle,
      winningSegmentIndex: targetSegmentIndex,
    );
  }

  /// Stop animation
  void stopSpin() {
    state = state.copyWith(
      isAnimating: false,
    );
  }

  /// Reset animation
  void resetSpin() {
    state = const SpinAnimationState();
  }
}

// Recent Winners Provider (for display purposes)
final recentWinnersProvider = Provider<List<Map<String, dynamic>>>((ref) {
  // This would typically come from a real-time database
  // For now, return sample data
  return [
    {
      'name': 'Rahul K.',
      'amount': 250,
      'time': '2 min ago',
      'avatar': 'R',
    },
    {
      'name': 'Priya S.',
      'amount': 100,
      'time': '5 min ago',
      'avatar': 'P',
    },
    {
      'name': 'Amit P.',
      'amount': 500,
      'time': '8 min ago',
      'avatar': 'A',
    },
    {
      'name': 'Neha G.',
      'amount': 50,
      'time': '12 min ago',
      'avatar': 'N',
    },
    {
      'name': 'Rohit V.',
      'amount': 25,
      'time': '15 min ago',
      'avatar': 'R',
    },
  ];
});

// Game Rules Provider
final gameRulesProvider = Provider<List<String>>((ref) {
  return [
    'Regular Spin: ₹10 entry fee, win up to ₹500',
    'Max Spin: ₹50 entry fee, win up to ₹1500 (3x multiplier)',
    'Winnings are added to your wallet instantly',
    'Entry fees are deducted immediately when you spin',
    'Fair play guaranteed with transparent odds',
    'All transactions are recorded in your wallet history',
    'Minimum age requirement: 18 years',
    'Play responsibly and within your limits',
    'Customer support available 24/7 for any issues',
  ];
});

// Spin Wheel Configuration Provider
final spinWheelConfigProvider = Provider<List<SpinWheelSegment>>((ref) {
  return SpinWheelConfig.segments;
});

// Entry Cost Provider
final entryCostProvider = Provider.family<double, SpinType>((ref, spinType) {
  return SpinWheelConfig.getEntryAmount(spinType);
});

// Multiplier Provider
final multiplierProvider = Provider.family<int, SpinType>((ref, spinType) {
  return SpinWheelConfig.getMultiplier(spinType);
});

// Max Win Amount Provider
final maxWinAmountProvider = Provider.family<double, SpinType>((ref, spinType) {
  final maxSegmentAmount = SpinWheelConfig.segments
      .map((s) => s.amount)
      .reduce((a, b) => a > b ? a : b);
  final multiplier = SpinWheelConfig.getMultiplier(spinType);
  return maxSegmentAmount.toDouble() * multiplier;
});
