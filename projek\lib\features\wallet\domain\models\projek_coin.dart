import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

part 'projek_coin.g.dart';

@HiveType(typeId: 15)
enum ProjekCoinTransactionType {
  @HiveField(0)
  earn,
  @HiveField(1)
  spend,
  @HiveField(2)
  transfer,
}

@HiveType(typeId: 10)
enum TransactionType {
  @HiveField(0)
  purchase,
  @HiveField(1)
  refund,
  @HiveField(2)
  reward,
  @HiveField(3)
  transfer,
  @HiveField(4)
  topup,
  @HiveField(5)
  withdrawal,
  @HiveField(6)
  cashback,
  @HiveField(7)
  referral,
}

@HiveType(typeId: 11)
enum TransactionStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  completed,
  @HiveField(2)
  failed,
  @HiveField(3)
  cancelled,
}

@HiveType(typeId: 12)
@JsonSerializable()
class ProjekCoinTransaction extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final TransactionType type;

  @HiveField(2)
  final double amount;

  @HiveField(3)
  final String description;

  @HiveField(4)
  final DateTime timestamp;

  @HiveField(5)
  final TransactionStatus status;

  @HiveField(6)
  final String? orderId;

  @HiveField(7)
  final String? referenceId;

  @HiveField(8)
  final Map<String, dynamic>? metadata;

  @HiveField(9)
  final String? category;

  const ProjekCoinTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    this.status = TransactionStatus.completed,
    this.orderId,
    this.referenceId,
    this.metadata,
    this.category,
  });

  /// Factory method for creating game-related transactions
  factory ProjekCoinTransaction.create({
    required double amount,
    required ProjekCoinTransactionType type,
    required String description,
    String? category,
    Map<String, dynamic>? metadata,
  }) {
    return ProjekCoinTransaction(
      id: const Uuid().v4(),
      type: _mapToTransactionType(type),
      amount: amount.abs(), // Always store as positive, use type for direction
      description: description,
      timestamp: DateTime.now(),
      status: TransactionStatus.completed,
      category: category,
      metadata: metadata,
    );
  }

  static TransactionType _mapToTransactionType(ProjekCoinTransactionType type) {
    switch (type) {
      case ProjekCoinTransactionType.earn:
        return TransactionType.reward;
      case ProjekCoinTransactionType.spend:
        return TransactionType.purchase;
      case ProjekCoinTransactionType.transfer:
        return TransactionType.transfer;
    }
  }

  factory ProjekCoinTransaction.fromJson(Map<String, dynamic> json) =>
      _$ProjekCoinTransactionFromJson(json);

  Map<String, dynamic> toJson() => _$ProjekCoinTransactionToJson(this);

  String get formattedAmount => '${amount.toStringAsFixed(2)} PC';
  String get formattedAmountWithSign =>
      '${type == TransactionType.purchase || type == TransactionType.transfer || type == TransactionType.withdrawal ? '-' : '+'}${amount.toStringAsFixed(2)} PC';

  bool get isCredit =>
      type == TransactionType.refund ||
      type == TransactionType.reward ||
      type == TransactionType.topup ||
      type == TransactionType.cashback ||
      type == TransactionType.referral;

  bool get isDebit => !isCredit;

  @override
  List<Object?> get props => [
    id,
    type,
    amount,
    description,
    timestamp,
    status,
    orderId,
    referenceId,
    metadata,
    category,
  ];
}

@HiveType(typeId: 13)
@JsonSerializable()
class ProjekCoinWallet extends Equatable {
  @HiveField(0)
  final double balance;

  @HiveField(1)
  final List<ProjekCoinTransaction> transactions;

  @HiveField(2)
  final DateTime lastUpdated;

  @HiveField(3)
  final double totalEarned;

  @HiveField(4)
  final double totalSpent;

  @HiveField(5)
  final int transactionCount;

  const ProjekCoinWallet({
    this.balance = 0.0,
    this.transactions = const [],
    required this.lastUpdated,
    this.totalEarned = 0.0,
    this.totalSpent = 0.0,
    this.transactionCount = 0,
  });

  factory ProjekCoinWallet.fromJson(Map<String, dynamic> json) =>
      _$ProjekCoinWalletFromJson(json);

  Map<String, dynamic> toJson() => _$ProjekCoinWalletToJson(this);

  String get formattedBalance => '${balance.toStringAsFixed(2)} PC';
  String get formattedTotalEarned => '${totalEarned.toStringAsFixed(2)} PC';
  String get formattedTotalSpent => '${totalSpent.toStringAsFixed(2)} PC';

  // Exchange rate: 1 Projek Coin = ₹1
  double get balanceInINR => balance;
  String get formattedBalanceInINR => '₹${balance.toStringAsFixed(2)}';

  List<ProjekCoinTransaction> get recentTransactions =>
      transactions.take(10).toList();

  ProjekCoinWallet copyWith({
    double? balance,
    List<ProjekCoinTransaction>? transactions,
    DateTime? lastUpdated,
    double? totalEarned,
    double? totalSpent,
    int? transactionCount,
  }) {
    return ProjekCoinWallet(
      balance: balance ?? this.balance,
      transactions: transactions ?? this.transactions,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      totalEarned: totalEarned ?? this.totalEarned,
      totalSpent: totalSpent ?? this.totalSpent,
      transactionCount: transactionCount ?? this.transactionCount,
    );
  }

  @override
  List<Object?> get props => [
    balance,
    transactions,
    lastUpdated,
    totalEarned,
    totalSpent,
    transactionCount,
  ];
}

@HiveType(typeId: 14)
@JsonSerializable()
class WalletStatistics extends Equatable {
  @HiveField(0)
  final double monthlyEarnings;

  @HiveField(1)
  final double monthlySpending;

  @HiveField(2)
  final int monthlyTransactions;

  @HiveField(3)
  final double pendingAmount;

  @HiveField(4)
  final DateTime calculatedAt;

  const WalletStatistics({
    this.monthlyEarnings = 0.0,
    this.monthlySpending = 0.0,
    this.monthlyTransactions = 0,
    this.pendingAmount = 0.0,
    required this.calculatedAt,
  });

  factory WalletStatistics.fromJson(Map<String, dynamic> json) =>
      _$WalletStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$WalletStatisticsToJson(this);

  String get formattedMonthlyEarnings =>
      '${monthlyEarnings.toStringAsFixed(2)} PC';
  String get formattedMonthlySpending =>
      '${monthlySpending.toStringAsFixed(2)} PC';
  String get formattedPendingAmount => '${pendingAmount.toStringAsFixed(2)} PC';

  @override
  List<Object?> get props => [
    monthlyEarnings,
    monthlySpending,
    monthlyTransactions,
    pendingAmount,
    calculatedAt,
  ];
}
