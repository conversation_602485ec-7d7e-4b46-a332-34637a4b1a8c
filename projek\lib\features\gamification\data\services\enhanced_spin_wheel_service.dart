import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/gamification_models.dart';
import 'gamification_service.dart';
import '../../../wallet/data/services/working_wallet_service.dart'
    as wallet_service;
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';

class EnhancedSpinWheelService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _spinDataCollection = 'spin_wheel_data';

  // Spin wheel rewards with probabilities
  static const List<SpinReward> _rewards = [
    SpinReward(name: '5 PC', amount: 5.0, probability: 0.30, color: 0xFF4CAF50),
    SpinReward(
      name: '10 PC',
      amount: 10.0,
      probability: 0.25,
      color: 0xFF2196F3,
    ),
    SpinReward(
      name: '25 PC',
      amount: 25.0,
      probability: 0.20,
      color: 0xFFFF9800,
    ),
    <PERSON><PERSON><PERSON><PERSON>(
      name: '50 PC',
      amount: 50.0,
      probability: 0.15,
      color: 0xFF9C27B0,
    ),
    SpinReward(
      name: '100 PC',
      amount: 100.0,
      probability: 0.08,
      color: 0xFFE91E63,
    ),
    SpinReward(
      name: '250 PC',
      amount: 250.0,
      probability: 0.015,
      color: 0xFFFF5722,
    ),
    SpinReward(
      name: '500 PC',
      amount: 500.0,
      probability: 0.004,
      color: 0xFFFFD700,
    ),
    SpinReward(
      name: 'JACKPOT',
      amount: 1000.0,
      probability: 0.001,
      color: 0xFFFF0000,
    ),
  ];

  // Get user's spin data
  static Future<SpinWheelData> getUserSpinData(String userId) async {
    try {
      final doc = await _firestore
          .collection(_spinDataCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return SpinWheelData.fromJson(doc.data()!);
      }

      // Create new spin data
      final spinData = SpinWheelData(
        userId: userId,
        spinsLeft: 5, // 5 free spins daily
        lastResetDate: DateTime.now(),
        totalSpins: 0,
        totalWinnings: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(_spinDataCollection)
          .doc(userId)
          .set(spinData.toJson());
      return spinData;
    } catch (e) {
      debugPrint('❌ Error getting spin data: $e');
      throw Exception('Failed to get spin data');
    }
  }

  // Play spin wheel
  static Future<SpinResult> playSpin({
    required String userId,
    bool isPaidSpin = false,
  }) async {
    try {
      final spinData = await getUserSpinData(userId);

      // Check if user has spins left or is paying
      if (!isPaidSpin && spinData.spinsLeft <= 0) {
        return SpinResult(
          success: false,
          message: 'No free spins left. Come back tomorrow or buy spins!',
          reward: null,
          newBalance: 0.0,
        );
      }

      // If paid spin, deduct cost from wallet
      if (isPaidSpin) {
        final wallet =
            await wallet_service.WorkingWalletService.getOrCreateWallet();
        const spinCost = 10.0; // 10 PC per paid spin

        if (wallet.projekCoinBalance < spinCost) {
          return SpinResult(
            success: false,
            message: 'Insufficient ProjekCoins. Need 10 PC to spin.',
            reward: null,
            newBalance: wallet.projekCoinBalance,
          );
        }

        // Deduct spin cost
        final success =
            await wallet_service.WorkingWalletService.spendProjekCoin(
              amount: spinCost,
              description: 'Spin Wheel Entry Fee',
            );

        if (!success) {
          return SpinResult(
            success: false,
            message: 'Failed to deduct spin cost.',
            reward: null,
            newBalance: wallet.projekCoinBalance,
          );
        }
      }

      // Determine reward based on probability
      final reward = _determineReward();

      // Award the reward
      if (reward.amount > 0) {
        await wallet_service.WorkingWalletService.addProjekCoin(
          amount: reward.amount,
          description: 'Spin Wheel Win: ${reward.name}',
        );
      }

      // Update spin data
      await _updateSpinData(userId, spinData, reward, isPaidSpin);

      // Record game session
      await GamificationService.recordGameSession(
        userId: userId,
        gameType: GameType.spinWheel,
        entryFee: isPaidSpin ? 10.0 : 0.0,
        winAmount: reward.amount,
        gameData: {
          'reward_name': reward.name,
          'is_paid_spin': isPaidSpin,
          'spins_left': isPaidSpin
              ? spinData.spinsLeft
              : spinData.spinsLeft - 1,
        },
      );

      // Track achievements
      await GamificationService.trackUserAction(
        userId: userId,
        actionType: AchievementType.gameWins,
        value: reward.amount > 0 ? 1 : 0,
      );

      // Get updated wallet balance
      final updatedWallet =
          await wallet_service.WorkingWalletService.getOrCreateWallet();

      // Send notification for big wins
      if (reward.amount >= 100) {
        await NotificationService.showLocalNotification(
          title: '🎉 Big Win!',
          body: 'You won ${reward.name} from the spin wheel!',
          payload: 'spin_win_${reward.amount}',
        );
      }

      // Log analytics
      await AnalyticsService.logEvent('spin_wheel_played', {
        'user_id': userId,
        'reward_amount': reward.amount,
        'reward_name': reward.name,
        'is_paid_spin': isPaidSpin,
        'total_spins': spinData.totalSpins + 1,
      });

      return SpinResult(
        success: true,
        message: reward.amount > 0
            ? 'Congratulations! You won ${reward.name}!'
            : 'Better luck next time!',
        reward: reward,
        newBalance: updatedWallet.projekCoinBalance,
      );
    } catch (e) {
      debugPrint('❌ Error playing spin: $e');
      return SpinResult(
        success: false,
        message: 'Failed to play spin: ${e.toString()}',
        reward: null,
        newBalance: 0.0,
      );
    }
  }

  // Reset daily spins
  static Future<void> resetDailySpins(String userId) async {
    try {
      final spinData = await getUserSpinData(userId);
      final now = DateTime.now();
      final lastReset = spinData.lastResetDate;

      // Check if 24 hours have passed
      if (now.difference(lastReset).inHours >= 24) {
        await _firestore.collection(_spinDataCollection).doc(userId).update({
          'spinsLeft': 5,
          'lastResetDate': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      debugPrint('❌ Error resetting daily spins: $e');
    }
  }

  // Get spin statistics
  static Future<SpinStatistics> getSpinStatistics(String userId) async {
    try {
      final spinData = await getUserSpinData(userId);

      // Get game sessions for detailed stats
      final sessionsQuery = await _firestore
          .collection('game_sessions')
          .where('userId', isEqualTo: userId)
          .where('gameType', isEqualTo: GameType.spinWheel.toString())
          .orderBy('playedAt', descending: true)
          .limit(100)
          .get();

      final sessions = sessionsQuery.docs
          .map((doc) => GameSession.fromJson(doc.data()))
          .toList();

      final totalWins = sessions.where((s) => s.isWin).length;
      final totalLosses = sessions.length - totalWins;
      final winRate = sessions.isNotEmpty ? (totalWins / sessions.length) : 0.0;
      final biggestWin = sessions.isNotEmpty
          ? sessions.map((s) => s.winAmount).reduce(max)
          : 0.0;

      return SpinStatistics(
        totalSpins: spinData.totalSpins,
        totalWinnings: spinData.totalWinnings,
        totalWins: totalWins,
        totalLosses: totalLosses,
        winRate: winRate,
        biggestWin: biggestWin,
        spinsLeft: spinData.spinsLeft,
        lastResetDate: spinData.lastResetDate,
      );
    } catch (e) {
      debugPrint('❌ Error getting spin statistics: $e');
      return SpinStatistics(
        totalSpins: 0,
        totalWinnings: 0.0,
        totalWins: 0,
        totalLosses: 0,
        winRate: 0.0,
        biggestWin: 0.0,
        spinsLeft: 0,
        lastResetDate: DateTime.now(),
      );
    }
  }

  // Helper methods
  static SpinReward _determineReward() {
    final random = Random();
    final randomValue = random.nextDouble();

    double cumulativeProbability = 0.0;
    for (final reward in _rewards) {
      cumulativeProbability += reward.probability;
      if (randomValue <= cumulativeProbability) {
        return reward;
      }
    }

    // Fallback to smallest reward
    return _rewards.first;
  }

  static Future<void> _updateSpinData(
    String userId,
    SpinWheelData spinData,
    SpinReward reward,
    bool isPaidSpin,
  ) async {
    final updates = <String, dynamic>{
      'totalSpins': FieldValue.increment(1),
      'totalWinnings': FieldValue.increment(reward.amount),
      'updatedAt': FieldValue.serverTimestamp(),
    };

    if (!isPaidSpin) {
      updates['spinsLeft'] = FieldValue.increment(-1);
    }

    await _firestore
        .collection(_spinDataCollection)
        .doc(userId)
        .update(updates);
  }

  // Get available rewards for display
  static List<SpinReward> getAvailableRewards() => _rewards;
}

// Models for spin wheel
class SpinReward {
  final String name;
  final double amount;
  final double probability;
  final int color;

  const SpinReward({
    required this.name,
    required this.amount,
    required this.probability,
    required this.color,
  });
}

class SpinResult {
  final bool success;
  final String message;
  final SpinReward? reward;
  final double newBalance;

  const SpinResult({
    required this.success,
    required this.message,
    this.reward,
    required this.newBalance,
  });
}

class SpinWheelData {
  final String userId;
  final int spinsLeft;
  final DateTime lastResetDate;
  final int totalSpins;
  final double totalWinnings;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SpinWheelData({
    required this.userId,
    required this.spinsLeft,
    required this.lastResetDate,
    required this.totalSpins,
    required this.totalWinnings,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SpinWheelData.fromJson(Map<String, dynamic> json) {
    return SpinWheelData(
      userId: json['userId'],
      spinsLeft: json['spinsLeft'],
      lastResetDate: (json['lastResetDate'] as Timestamp).toDate(),
      totalSpins: json['totalSpins'],
      totalWinnings: json['totalWinnings'].toDouble(),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: (json['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'spinsLeft': spinsLeft,
      'lastResetDate': Timestamp.fromDate(lastResetDate),
      'totalSpins': totalSpins,
      'totalWinnings': totalWinnings,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
}

class SpinStatistics {
  final int totalSpins;
  final double totalWinnings;
  final int totalWins;
  final int totalLosses;
  final double winRate;
  final double biggestWin;
  final int spinsLeft;
  final DateTime lastResetDate;

  const SpinStatistics({
    required this.totalSpins,
    required this.totalWinnings,
    required this.totalWins,
    required this.totalLosses,
    required this.winRate,
    required this.biggestWin,
    required this.spinsLeft,
    required this.lastResetDate,
  });

  String get formattedWinRate => '${(winRate * 100).toStringAsFixed(1)}%';
  String get formattedTotalWinnings => '${totalWinnings.toStringAsFixed(0)} PC';
  String get formattedBiggestWin => '${biggestWin.toStringAsFixed(0)} PC';
}
