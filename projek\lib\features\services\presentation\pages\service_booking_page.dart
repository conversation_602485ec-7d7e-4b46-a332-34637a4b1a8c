import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../domain/models/service.dart';
import '../../domain/models/booking.dart';
import '../../data/services/booking_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';

class ServiceBookingPage extends ConsumerStatefulWidget {
  final Service service;

  const ServiceBookingPage({
    super.key,
    required this.service,
  });

  @override
  ConsumerState<ServiceBookingPage> createState() => _ServiceBookingPageState();
}

class _ServiceBookingPageState extends ConsumerState<ServiceBookingPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Scheduling variables
  DateTime _selectedDate = DateTime.now();
  String? _selectedTimeSlot;
  int _selectedDuration = 2;
  List<String> _availableTimeSlots = [];
  bool _isLoadingSlots = false;

  // Address variables
  final _addressController = TextEditingController();
  final _landmarkController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  // Requirements
  final List<String> _selectedRequirements = [];
  final List<String> _availableRequirements = [
    'Bring own tools',
    'Cleaning supplies needed',
    'Parking available',
    'Pet-friendly service',
    'Emergency service',
    'Weekend service',
  ];

  bool _isBooking = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAvailableTimeSlots();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _addressController.dispose();
    _landmarkController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableTimeSlots() async {
    setState(() => _isLoadingSlots = true);

    try {
      final slots = await BookingService.getAvailableTimeSlots(
        serviceId: widget.service.id,
        date: _selectedDate,
        durationHours: _selectedDuration,
      );

      setState(() {
        _availableTimeSlots = slots;
        _selectedTimeSlot = null;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load available time slots'),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    } finally {
      setState(() => _isLoadingSlots = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Book ${widget.service.title}'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Schedule', icon: Icon(Icons.calendar_today)),
            Tab(text: 'Details', icon: Icon(Icons.location_on)),
            Tab(text: 'Confirm', icon: Icon(Icons.check_circle)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildScheduleTab(),
          _buildDetailsTab(),
          _buildConfirmTab(),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service info card
          _buildServiceInfoCard(),
          const SizedBox(height: 24),

          // Duration selection
          _buildDurationSelection(),
          const SizedBox(height: 24),

          // Calendar
          _buildCalendar(),
          const SizedBox(height: 24),

          // Time slots
          _buildTimeSlots(),
        ],
      ),
    );
  }

  Widget _buildServiceInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Service image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getServiceIcon(widget.service.type as String),
              size: 40,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(width: 16),

          // Service details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.service.title,
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.service.providerName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.service.rating} (${widget.service.reviewCount})',
                      style: AppTextStyles.bodySmall,
                    ),
                    const Spacer(),
                    Text(
                      '₹${widget.service.basePrice}/${_getPricingUnit(widget.service.pricingType as String)}',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Duration',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          children: [1, 2, 3, 4, 6, 8].map((hours) {
            final isSelected = _selectedDuration == hours;
            return GestureDetector(
              onTap: () {
                setState(() => _selectedDuration = hours);
                _loadAvailableTimeSlots();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primaryBlue : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppColors.primaryBlue : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  '$hours ${hours == 1 ? 'hour' : 'hours'}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isSelected ? Colors.white : AppColors.textPrimary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCalendar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TableCalendar<String>(
        firstDay: DateTime.now(),
        lastDay: DateTime.now().add(const Duration(days: 30)),
        focusedDay: _selectedDate,
        selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
        onDaySelected: (selectedDay, focusedDay) {
          setState(() => _selectedDate = selectedDay);
          _loadAvailableTimeSlots();
        },
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          selectedDecoration: BoxDecoration(
            color: AppColors.primaryBlue,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppColors.secondaryOrange,
            shape: BoxShape.circle,
          ),
        ),
        headerStyle: const HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
        ),
      ),
    );
  }

  Widget _buildTimeSlots() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Time Slots',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        if (_isLoadingSlots)
          const Center(child: CircularProgressIndicator())
        else if (_availableTimeSlots.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600]),
                const SizedBox(width: 12),
                Text(
                  'No available slots for selected date',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _availableTimeSlots.map((slot) {
              final isSelected = _selectedTimeSlot == slot;
              return GestureDetector(
                onTap: () => setState(() => _selectedTimeSlot = slot),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primaryBlue : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? AppColors.primaryBlue : Colors.grey[300]!,
                    ),
                  ),
                  child: Text(
                    _formatTimeSlot(slot),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isSelected ? Colors.white : AppColors.textPrimary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address section
          _buildAddressSection(),
          const SizedBox(height: 24),

          // Requirements section
          _buildRequirementsSection(),
          const SizedBox(height: 24),

          // Special instructions
          _buildSpecialInstructions(),
        ],
      ),
    );
  }

  Widget _buildAddressSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Service Address',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: 'Complete Address',
              hintText: 'Enter your complete address',
              prefixIcon: Icon(Icons.home, color: AppColors.primaryBlue),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _landmarkController,
            decoration: InputDecoration(
              labelText: 'Landmark (Optional)',
              hintText: 'Nearby landmark for easy location',
              prefixIcon: Icon(Icons.place, color: AppColors.primaryBlue),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.checklist, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Service Requirements',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableRequirements.map((requirement) {
              final isSelected = _selectedRequirements.contains(requirement);
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedRequirements.remove(requirement);
                    } else {
                      _selectedRequirements.add(requirement);
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primaryBlue.withValues(alpha: 0.1) : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? AppColors.primaryBlue : Colors.grey[300]!,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: AppColors.primaryBlue,
                        ),
                      if (isSelected) const SizedBox(width: 4),
                      Text(
                        requirement,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isSelected ? AppColors.primaryBlue : AppColors.textSecondary,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note_add, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Special Instructions',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _specialInstructionsController,
            decoration: InputDecoration(
              labelText: 'Additional Instructions (Optional)',
              hintText: 'Any specific requirements or instructions for the service provider',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Booking summary
          _buildBookingSummary(),
          const SizedBox(height: 24),
          
          // Payment details
          _buildPaymentDetails(),
          const SizedBox(height: 24),
          
          // Terms and conditions
          _buildTermsAndConditions(),
        ],
      ),
    );
  }

  Widget _buildBookingSummary() {
    return _buildSectionCard(
      title: 'Booking Summary',
      icon: Icons.receipt_long,
      child: Column(
        children: [
          _buildSummaryRow('Service', widget.service.title),
          _buildSummaryRow('Provider', widget.service.providerName),
          _buildSummaryRow('Date', _formatDate(_selectedDate)),
          if (_selectedTimeSlot != null)
            _buildSummaryRow('Time', _formatTimeSlot(_selectedTimeSlot!)),
          _buildSummaryRow('Duration', '$_selectedDuration hours'),
          if (_addressController.text.isNotEmpty)
            _buildSummaryRow('Address', _addressController.text),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails() {
    final serviceAmount = widget.service.basePrice * _selectedDuration;
    final platformFee = serviceAmount * 0.05; // 5% platform fee
    final taxes = serviceAmount * 0.18; // 18% GST
    final totalAmount = serviceAmount + platformFee + taxes;

    return _buildSectionCard(
      title: 'Payment Details',
      icon: Icons.payment,
      child: Column(
        children: [
          _buildSummaryRow(
            'Service Amount',
            '₹${serviceAmount.toStringAsFixed(2)}',
          ),
          _buildSummaryRow(
            'Platform Fee',
            '₹${platformFee.toStringAsFixed(2)}',
          ),
          _buildSummaryRow('Taxes', '₹${taxes.toStringAsFixed(2)}'),
          const Divider(),
          _buildSummaryRow(
            'Total Amount',
            '₹${totalAmount.toStringAsFixed(2)}',
            isHighlighted: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Terms & Conditions',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'By confirming this booking, you agree to our terms of service and cancellation policy.',
            style: AppTextStyles.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isHighlighted = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              color: isHighlighted ? AppColors.primaryBlue : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatTimeSlot(String timeSlot) {
    final parts = timeSlot.split('-');
    return '${_formatTime(parts[0])} - ${_formatTime(parts[1])}';
  }

  String _formatTime(String time) {
    final parts = time.trim().split(':');
    final hour = int.parse(parts[0]);
    final minute = parts[1];
    
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$displayHour:$minute $period';
  }

  String _getPricingUnit(String pricingType) {
    switch (pricingType) {
      case 'hourly':
        return 'hour';
      case 'daily':
        return 'day';
      case 'fixed':
        return 'service';
      default:
        return 'hour';
    }
  }

  IconData _getServiceIcon(String type) {
    switch (type) {
      case 'cleaning':
        return Icons.cleaning_services;
      case 'repair':
        return Icons.build;
      case 'plumbing':
        return Icons.plumbing;
      case 'electrical':
        return Icons.electrical_services;
      case 'painting':
        return Icons.format_paint;
      case 'moving':
        return Icons.local_shipping;
      case 'gardening':
        return Icons.yard;
      default:
        return Icons.miscellaneous_services;
    }
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceed() ? _proceedToNextStep : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(_getButtonText()),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceed() {
    if (_tabController.index == 0) {
      return _selectedTimeSlot != null;
    } else if (_tabController.index == 1) {
      return _addressController.text.isNotEmpty;
    }
    return true;
  }

  String _getButtonText() {
    if (_tabController.index == 0) {
      return 'Continue to Details';
    } else if (_tabController.index == 1) {
      return 'Continue to Confirmation';
    } else {
      return 'Confirm Booking';
    }
  }

  void _proceedToNextStep() {
    if (_tabController.index < 2) {
      _tabController.animateTo(_tabController.index + 1);
    } else {
      _bookService();
    }
  }

  Future<void> _bookService() async {
    if (_isBooking) return;
    
    setState(() => _isBooking = true);
    
    try {
      final serviceAmount = widget.service.basePrice * _selectedDuration;
      final platformFee = serviceAmount * 0.05;
      final taxes = serviceAmount * 0.18;
      final totalAmount = serviceAmount + platformFee + taxes;
      
      final schedule = BookingSchedule(
        scheduledDate: _selectedDate,
        startTime: _selectedTimeSlot!.split('-')[0].trim(),
        endTime: _selectedTimeSlot!.split('-')[1].trim(),
        durationHours: _selectedDuration,
      );
      
      final address = BookingAddress(
        street: _addressController.text,
        city: "User's City", // This should be collected from the user
        state: "User's State", // This should be collected from the user
        pincode: "000000", // This should be collected from the user
        landmark: _landmarkController.text,
      );
      
      final booking = await BookingService.createBooking(
        serviceId: widget.service.id,
        service: widget.service,
        schedule: schedule,
        address: address,
        specialInstructions: _specialInstructionsController.text,
        requirements: _selectedRequirements,
      );
      
      if (mounted) {
        context.go('/bookings/confirmation/${booking.id}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create booking: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isBooking = false);
      }
    }
  }

}











