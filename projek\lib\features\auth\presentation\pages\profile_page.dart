import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/utils/app_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../providers/user_provider.dart';

class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push(AppRoutes.settings),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Profile Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Profile Picture
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: colorScheme.primary,
                        backgroundImage: user?.profileImageUrl != null
                            ? NetworkImage(user!.profileImageUrl!)
                            : null,
                        child: user?.profileImageUrl == null
                            ? Text(
                                user?.initials ?? 'GU',
                                style: theme.textTheme.headlineMedium?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.camera_alt,
                              color: colorScheme.onPrimary,
                              size: 20,
                            ),
                            onPressed: _handleChangeProfilePicture,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // User Info
                  Text(
                    user?.fullName ?? 'Guest User',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user?.email ?? '<EMAIL>',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    user?.phone ?? 'No phone number',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Menu Items
            _buildMenuSection(context, 'Account', [
              _MenuItem(
                icon: Icons.edit,
                title: 'Edit Profile',
                onTap: () => _handleEditProfile(context),
              ),
              _MenuItem(
                icon: Icons.location_on,
                title: 'Addresses',
                onTap: () => _handleAddresses(context),
              ),
              _MenuItem(
                icon: Icons.payment,
                title: 'Payment Methods',
                onTap: () => _handlePaymentMethods(context),
              ),
              _MenuItem(
                icon: Icons.security,
                title: 'Security',
                onTap: () => _handleSecurity(context),
              ),
            ]),
            const SizedBox(height: 16),

            _buildMenuSection(context, 'Orders & Activity', [
              _MenuItem(
                icon: Icons.shopping_bag,
                title: 'My Orders',
                onTap: () => context.push(AppRoutes.orders),
              ),
              _MenuItem(
                icon: Icons.favorite,
                title: 'Wishlist',
                onTap: () => _handleWishlist(context),
              ),
              _MenuItem(
                icon: Icons.history,
                title: 'Order History',
                onTap: () => _handleOrderHistory(context),
              ),
              _MenuItem(
                icon: Icons.star,
                title: 'Reviews & Ratings',
                onTap: () => _handleReviews(context),
              ),
            ]),
            const SizedBox(height: 16),

            _buildMenuSection(context, 'Support', [
              _MenuItem(
                icon: Icons.help,
                title: 'Help Center',
                onTap: () => context.push(AppRoutes.help),
              ),
              _MenuItem(
                icon: Icons.chat,
                title: 'Contact Support',
                onTap: () => _handleContactSupport(context),
              ),
              _MenuItem(
                icon: Icons.info,
                title: 'About',
                onTap: () => context.push(AppRoutes.about),
              ),
            ]),
            const SizedBox(height: 24),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _handleLogout(context),
                icon: const Icon(Icons.logout),
                label: const Text('Sign Out'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: colorScheme.error,
                  side: BorderSide(color: colorScheme.error),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuSection(
    BuildContext context,
    String title,
    List<_MenuItem> items,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isLast = index == items.length - 1;

              return Column(
                children: [
                  ListTile(
                    leading: Icon(item.icon),
                    title: Text(item.title),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: item.onTap,
                  ),
                  if (!isLast) const Divider(height: 1),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _handleChangeProfilePicture() {
    // TODO: Implement profile picture change
  }

  void _handleEditProfile(BuildContext context) {
    context.push(AppRoutes.editProfile);
  }

  void _handleAddresses(BuildContext context) {
    // TODO: Navigate to addresses page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Addresses not implemented yet')),
    );
  }

  void _handlePaymentMethods(BuildContext context) {
    // TODO: Navigate to payment methods page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Payment methods not implemented yet')),
    );
  }

  void _handleSecurity(BuildContext context) {
    // TODO: Navigate to security page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Security settings not implemented yet')),
    );
  }

  void _handleWishlist(BuildContext context) {
    context.go(AppRoutes.wishlist);
  }

  void _handleOrderHistory(BuildContext context) {
    // TODO: Navigate to order history page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Order history not implemented yet')),
    );
  }

  void _handleReviews(BuildContext context) {
    // TODO: Navigate to reviews page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reviews not implemented yet')),
    );
  }

  void _handleContactSupport(BuildContext context) {
    // TODO: Navigate to contact support page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Contact support not implemented yet')),
    );
  }

  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual logout logic
              context.go(AppRoutes.login);
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}

class _MenuItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
  });
}
