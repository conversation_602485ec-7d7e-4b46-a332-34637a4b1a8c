document.addEventListener('DOMContentLoaded', function() {
  // Mobile menu toggle
  const menuToggle = document.querySelector('.menu-toggle');
  const navLinks = document.querySelector('.nav-links');
  
  if (menuToggle) {
    menuToggle.addEventListener('click', function() {
      navLinks.classList.toggle('show');
    });
  }
  
  // Admin login modal
  const adminLinks = document.querySelectorAll('.admin-link');
  const adminModal = document.querySelector('#adminModal');
  const closeModal = document.querySelector('.close-modal');
  
  if (adminLinks.length > 0) {
    adminLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        adminModal.classList.add('show');
      });
    });
  }
  
  if (closeModal) {
    closeModal.addEventListener('click', function() {
      adminModal.classList.remove('show');
    });
  }
  
  // Close modal when clicking outside
  window.addEventListener('click', function(e) {
    if (e.target === adminModal) {
      adminModal.classList.remove('show');
    }
  });
  
  // Admin login form submission
  const adminForm = document.querySelector('#adminLoginForm');
  
  if (adminForm) {
    adminForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const userId = document.querySelector('#userId').value;
      const password = document.querySelector('#password').value;
      const role = document.querySelector('#role').value;
      
      // Here you would typically send this data to a server for authentication
      // For demo purposes, we'll just log it and show a success message
      console.log('Login attempt:', { userId, password, role });
      
      // Simulate successful login
      alert(`Login successful as ${role}!`);
      adminModal.classList.remove('show');
      
      // In a real application, you would redirect to the appropriate dashboard
      // window.location.href = `/admin-dashboard/${role.toLowerCase()}`;
    });
  }
  
  // Smooth scrolling for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const href = this.getAttribute('href');
      
      if (href !== '#') {
        e.preventDefault();
        const targetElement = document.querySelector(href);
        
        if (targetElement) {
          window.scrollTo({
            top: targetElement.offsetTop - 80, // Adjust for fixed header
            behavior: 'smooth'
          });
          
          // Close mobile menu if open
          if (navLinks.classList.contains('show')) {
            navLinks.classList.remove('show');
          }
        }
      }
    });
  });
  
  // Add floating animation to phone mockups
  const phoneMockups = document.querySelectorAll('.phone-mockup');
  
  phoneMockups.forEach((mockup, index) => {
    mockup.classList.add('float-animation');
    mockup.style.animationDelay = `${index * 0.5}s`;
  });
  
  // Intersection Observer for fade-in animations
  const fadeElements = document.querySelectorAll('.fade-in');
  
  const fadeObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        fadeObserver.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });
  
  fadeElements.forEach(element => {
    fadeObserver.observe(element);
  });
});