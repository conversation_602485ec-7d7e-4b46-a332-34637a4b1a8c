import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Working Firebase Firestore Chat Models

class WorkingChat extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String type;
  final List<String> participantIds;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final String? lastMessageSenderId;
  final bool isActive;
  final Map<String, int> unreadCounts;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? orderId;
  final String? serviceId;

  const WorkingChat({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.participantIds,
    this.lastMessage,
    this.lastMessageTime,
    this.lastMessageSenderId,
    this.isActive = true,
    this.unreadCounts = const {},
    required this.createdAt,
    required this.updatedAt,
    this.orderId,
    this.serviceId,
  });

  factory WorkingChat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkingChat(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      type: data['type'] ?? 'direct',
      participantIds: List<String>.from(data['participantIds'] ?? []),
      lastMessage: data['lastMessage'],
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate(),
      lastMessageSenderId: data['lastMessageSenderId'],
      isActive: data['isActive'] ?? true,
      unreadCounts: Map<String, int>.from(data['unreadCounts'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      orderId: data['orderId'],
      serviceId: data['serviceId'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'type': type,
      'participantIds': participantIds,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime != null ? Timestamp.fromDate(lastMessageTime!) : null,
      'lastMessageSenderId': lastMessageSenderId,
      'isActive': isActive,
      'unreadCounts': unreadCounts,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'orderId': orderId,
      'serviceId': serviceId,
    };
  }

  Map<String, dynamic> toJson() => toFirestore();

  @override
  List<Object?> get props => [
    id, name, description, type, participantIds, lastMessage,
    lastMessageTime, lastMessageSenderId, isActive, unreadCounts,
    createdAt, updatedAt, orderId, serviceId,
  ];
}

class WorkingChatMessage extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String? senderAvatarUrl;
  final String type;
  final String content;
  final List<String> attachmentUrls;
  final String? replyToMessageId;
  final DateTime timestamp;
  final DateTime? editedAt;
  final String status;
  final List<String> readBy;
  final bool isDeleted;

  const WorkingChatMessage({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    this.senderAvatarUrl,
    required this.type,
    required this.content,
    this.attachmentUrls = const [],
    this.replyToMessageId,
    required this.timestamp,
    this.editedAt,
    this.status = 'sending',
    this.readBy = const [],
    this.isDeleted = false,
  });

  factory WorkingChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkingChatMessage(
      id: doc.id,
      chatId: data['chatId'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? '',
      senderAvatarUrl: data['senderAvatarUrl'],
      type: data['type'] ?? 'text',
      content: data['content'] ?? '',
      attachmentUrls: List<String>.from(data['attachmentUrls'] ?? []),
      replyToMessageId: data['replyToMessageId'],
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      editedAt: (data['editedAt'] as Timestamp?)?.toDate(),
      status: data['status'] ?? 'sending',
      readBy: List<String>.from(data['readBy'] ?? []),
      isDeleted: data['isDeleted'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'chatId': chatId,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatarUrl': senderAvatarUrl,
      'type': type,
      'content': content,
      'attachmentUrls': attachmentUrls,
      'replyToMessageId': replyToMessageId,
      'timestamp': Timestamp.fromDate(timestamp),
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'status': status,
      'readBy': readBy,
      'isDeleted': isDeleted,
    };
  }

  Map<String, dynamic> toJson() => toFirestore();

  WorkingChatMessage copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? senderAvatarUrl,
    String? type,
    String? content,
    List<String>? attachmentUrls,
    String? replyToMessageId,
    DateTime? timestamp,
    DateTime? editedAt,
    String? status,
    List<String>? readBy,
    bool? isDeleted,
  }) {
    return WorkingChatMessage(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatarUrl: senderAvatarUrl ?? this.senderAvatarUrl,
      type: type ?? this.type,
      content: content ?? this.content,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      timestamp: timestamp ?? this.timestamp,
      editedAt: editedAt ?? this.editedAt,
      status: status ?? this.status,
      readBy: readBy ?? this.readBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  @override
  List<Object?> get props => [
    id, chatId, senderId, senderName, senderAvatarUrl, type, content,
    attachmentUrls, replyToMessageId, timestamp, editedAt, status, readBy, isDeleted,
  ];
}

class WorkingChatParticipant extends Equatable {
  final String userId;
  final String name;
  final String? email;
  final String? avatarUrl;
  final String role;
  final DateTime joinedAt;
  final DateTime? lastSeenAt;
  final bool isOnline;
  final bool isMuted;

  const WorkingChatParticipant({
    required this.userId,
    required this.name,
    this.email,
    this.avatarUrl,
    this.role = 'member',
    required this.joinedAt,
    this.lastSeenAt,
    this.isOnline = false,
    this.isMuted = false,
  });

  factory WorkingChatParticipant.fromMap(Map<String, dynamic> data) {
    return WorkingChatParticipant(
      userId: data['userId'] ?? '',
      name: data['name'] ?? '',
      email: data['email'],
      avatarUrl: data['avatarUrl'],
      role: data['role'] ?? 'member',
      joinedAt: (data['joinedAt'] as Timestamp).toDate(),
      lastSeenAt: (data['lastSeenAt'] as Timestamp?)?.toDate(),
      isOnline: data['isOnline'] ?? false,
      isMuted: data['isMuted'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'email': email,
      'avatarUrl': avatarUrl,
      'role': role,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'lastSeenAt': lastSeenAt != null ? Timestamp.fromDate(lastSeenAt!) : null,
      'isOnline': isOnline,
      'isMuted': isMuted,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  @override
  List<Object?> get props => [
    userId, name, email, avatarUrl, role, joinedAt, lastSeenAt, isOnline, isMuted,
  ];
}
