import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/seller/presentation/pages/splash_page.dart';
import '../../features/seller/presentation/pages/auth/login_page.dart';
import '../../features/seller/presentation/pages/auth/register_page.dart';
import '../../features/seller/presentation/pages/kyc/kyc_page.dart';
import '../../features/seller/presentation/pages/dashboard/dashboard_page.dart';
import '../../features/seller/presentation/pages/products/products_page.dart';
import '../../features/seller/presentation/pages/orders/orders_page.dart';
import '../../features/seller/presentation/pages/profile/profile_page.dart';

final sellerRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      // Splash
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SellerSplashPage(),
      ),

      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const SellerLoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const SellerRegisterPage(),
      ),
      GoRoute(
        path: '/business-verification',
        name: 'business-verification',
        builder: (context, state) =>
            const Placeholder(), // SellerBusinessVerificationPage(),
      ),

      // KYC Process
      GoRoute(
        path: '/kyc',
        name: 'kyc',
        builder: (context, state) => const SellerKYCPage(),
        routes: [
          GoRoute(
            path: 'business-documents',
            name: 'kyc-business-documents',
            builder: (context, state) =>
                const Placeholder(), // SellerBusinessDocumentsPage(),
          ),
          GoRoute(
            path: 'gst-registration',
            name: 'kyc-gst',
            builder: (context, state) =>
                const Placeholder(), // SellerGSTRegistrationPage(),
          ),
          GoRoute(
            path: 'bank-details',
            name: 'kyc-bank',
            builder: (context, state) =>
                const Placeholder(), // SellerBankDetailsPage(),
          ),
        ],
      ),

      // Main Dashboard
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const SellerDashboardPage(),
        routes: [
          GoRoute(
            path: 'analytics',
            name: 'analytics',
            builder: (context, state) =>
                const Placeholder(), // SellerAnalyticsPage(),
          ),
          GoRoute(
            path: 'revenue',
            name: 'revenue',
            builder: (context, state) =>
                const Placeholder(), // SellerRevenuePage(),
          ),
          GoRoute(
            path: 'performance',
            name: 'performance',
            builder: (context, state) =>
                const Placeholder(), // SellerPerformancePage(),
          ),
        ],
      ),

      // Product Management
      GoRoute(
        path: '/products',
        name: 'products',
        builder: (context, state) => const SellerProductsPage(),
        routes: [
          GoRoute(
            path: 'add',
            name: 'add-product',
            builder: (context, state) =>
                const Placeholder(), // SellerAddProductPage(),
          ),
          GoRoute(
            path: 'edit/:productId',
            name: 'edit-product',
            builder: (context, state) {
              // final productId = state.pathParameters['productId']!;
              return const Placeholder(); // SellerEditProductPage will be implemented
            },
          ),
          GoRoute(
            path: 'inventory',
            name: 'inventory',
            builder: (context, state) =>
                const Placeholder(), // SellerInventoryPage(),
          ),
          GoRoute(
            path: 'categories',
            name: 'categories',
            builder: (context, state) =>
                const Placeholder(), // SellerCategoriesPage(),
          ),
        ],
      ),

      // Order Management
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const SellerOrdersPage(),
        routes: [
          GoRoute(
            path: 'detail/:orderId',
            name: 'order-detail',
            builder: (context, state) {
              // final orderId = state.pathParameters['orderId']!;
              return const Placeholder(); // SellerOrderDetailPage will be implemented
            },
          ),
          GoRoute(
            path: 'management',
            name: 'order-management',
            builder: (context, state) =>
                const Placeholder(), // SellerOrderManagementPage(),
          ),
          GoRoute(
            path: 'history',
            name: 'order-history',
            builder: (context, state) =>
                const Placeholder(), // SellerOrderHistoryPage(),
          ),
        ],
      ),

      // Course Management (for education providers)
      GoRoute(
        path: '/courses',
        name: 'courses',
        builder: (context, state) =>
            const Placeholder(), // SellerCoursesPage(),
        routes: [
          GoRoute(
            path: 'add',
            name: 'add-course',
            builder: (context, state) =>
                const Placeholder(), // SellerAddCoursePage(),
          ),
          GoRoute(
            path: 'edit/:courseId',
            name: 'edit-course',
            builder: (context, state) {
              // final courseId = state.pathParameters['courseId']!;
              return const Placeholder(); // SellerEditCoursePage will be implemented
            },
          ),
          GoRoute(
            path: 'schedule',
            name: 'course-schedule',
            builder: (context, state) =>
                const Placeholder(), // SellerCourseSchedulePage(),
          ),
          GoRoute(
            path: 'students',
            name: 'students',
            builder: (context, state) =>
                const Placeholder(), // SellerStudentsPage(),
          ),
        ],
      ),

      // Service Management (for service providers)
      GoRoute(
        path: '/services',
        name: 'services',
        builder: (context, state) =>
            const Placeholder(), // SellerServicesPage(),
        routes: [
          GoRoute(
            path: 'add',
            name: 'add-service',
            builder: (context, state) =>
                const Placeholder(), // SellerAddServicePage(),
          ),
          GoRoute(
            path: 'bookings',
            name: 'service-bookings',
            builder: (context, state) =>
                const Placeholder(), // SellerBookingsPage(),
          ),
          GoRoute(
            path: 'areas',
            name: 'service-areas',
            builder: (context, state) =>
                const Placeholder(), // SellerServiceAreasPage(),
          ),
        ],
      ),

      // Shop Management
      GoRoute(
        path: '/shop',
        name: 'shop',
        builder: (context, state) =>
            const Placeholder(), // SellerShopProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'shop-settings',
            builder: (context, state) =>
                const Placeholder(), // SellerShopSettingsPage(),
          ),
          GoRoute(
            path: 'timings',
            name: 'shop-timings',
            builder: (context, state) =>
                const Placeholder(), // SellerShopTimingsPage(),
          ),
        ],
      ),

      // Financial Management
      GoRoute(
        path: '/finance',
        name: 'finance',
        builder: (context, state) =>
            const Placeholder(), // SellerFinancePage(),
        routes: [
          GoRoute(
            path: 'payouts',
            name: 'payouts',
            builder: (context, state) =>
                const Placeholder(), // SellerPayoutsPage(),
          ),
          GoRoute(
            path: 'taxes',
            name: 'taxes',
            builder: (context, state) =>
                const Placeholder(), // SellerTaxesPage(),
          ),
        ],
      ),

      // Marketing & Promotions
      GoRoute(
        path: '/marketing',
        name: 'marketing',
        builder: (context, state) =>
            const Placeholder(), // SellerMarketingPage(),
        routes: [
          GoRoute(
            path: 'promotions',
            name: 'promotions',
            builder: (context, state) =>
                const Placeholder(), // SellerPromotionsPage(),
          ),
          GoRoute(
            path: 'coupons',
            name: 'coupons',
            builder: (context, state) =>
                const Placeholder(), // SellerCouponsPage(),
          ),
        ],
      ),

      // Profile & Settings
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const SellerProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) =>
                const Placeholder(), // SellerSettingsPage(),
          ),
          GoRoute(
            path: 'help',
            name: 'help',
            builder: (context, state) =>
                const Placeholder(), // SellerHelpPage(),
          ),
          GoRoute(
            path: 'support',
            name: 'support',
            builder: (context, state) =>
                const Placeholder(), // SellerSupportPage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Page not found: ${state.uri}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods for Seller app
class SellerNavigation {
  static void goToSplash(BuildContext context) => context.go('/splash');
  static void goToLogin(BuildContext context) => context.go('/login');
  static void goToRegister(BuildContext context) => context.go('/register');
  static void goToBusinessVerification(BuildContext context) =>
      context.go('/business-verification');
  static void goToKYC(BuildContext context) => context.go('/kyc');
  static void goToDashboard(BuildContext context) => context.go('/dashboard');
  static void goToProducts(BuildContext context) => context.go('/products');
  static void goToAddProduct(BuildContext context) =>
      context.go('/products/add');
  static void goToEditProduct(BuildContext context, String productId) =>
      context.go('/products/edit/$productId');
  static void goToOrders(BuildContext context) => context.go('/orders');
  static void goToOrderDetail(BuildContext context, String orderId) =>
      context.go('/orders/detail/$orderId');
  static void goToCourses(BuildContext context) => context.go('/courses');
  static void goToServices(BuildContext context) => context.go('/services');
  static void goToShop(BuildContext context) => context.go('/shop');
  static void goToFinance(BuildContext context) => context.go('/finance');
  static void goToMarketing(BuildContext context) => context.go('/marketing');
  static void goToProfile(BuildContext context) => context.go('/profile');
}
