import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

final supportTicketProvider = StateNotifierProvider<SupportTicketNotifier, SupportTicketState>((ref) {
  return SupportTicketNotifier();
});

class SupportTicketState {
  final bool isSubmitting;
  final String? ticketId;
  final String status;

  SupportTicketState({
    this.isSubmitting = false,
    this.ticketId,
    this.status = 'draft',
  });

  SupportTicketState copyWith({
    bool? isSubmitting,
    String? ticketId,
    String? status,
  }) {
    return SupportTicketState(
      isSubmitting: isSubmitting ?? this.isSubmitting,
      ticketId: ticketId ?? this.ticketId,
      status: status ?? this.status,
    );
  }
}

class SupportTicketNotifier extends StateNotifier<SupportTicketState> {
  SupportTicketNotifier() : super(SupportTicketState());

  Future<void> submitTicket({
    required String category,
    required String subject,
    required String description,
    required String priority,
    String? orderId,
  }) async {
    state = state.copyWith(isSubmitting: true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Generate ticket ID
      final ticketId = 'TKT${DateTime.now().millisecondsSinceEpoch}';
      
      state = state.copyWith(
        isSubmitting: false,
        ticketId: ticketId,
        status: 'submitted',
      );

      // Log analytics
      await AnalyticsService.logEvent('support_ticket_submitted', {
        'category': category,
        'priority': priority,
        'has_order_id': orderId != null,
      });
    } catch (e) {
      state = state.copyWith(isSubmitting: false);
      rethrow;
    }
  }
}

class ContactSupportPage extends ConsumerStatefulWidget {
  const ContactSupportPage({super.key});

  @override
  ConsumerState<ContactSupportPage> createState() => _ContactSupportPageState();
}

class _ContactSupportPageState extends ConsumerState<ContactSupportPage> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderIdController = TextEditingController();

  String selectedCategory = 'General Inquiry';
  String selectedPriority = 'Medium';

  final List<String> categories = [
    'General Inquiry',
    'Order Issue',
    'Payment Problem',
    'Delivery Issue',
    'Account Problem',
    'Technical Issue',
    'Refund Request',
    'Feature Request',
    'Bug Report',
  ];

  final List<String> priorities = [
    'Low',
    'Medium',
    'High',
    'Urgent',
  ];

  @override
  Widget build(BuildContext context) {
    final ticketState = ref.watch(supportTicketProvider);
    final ticketNotifier = ref.read(supportTicketProvider.notifier);

    if (ticketState.status == 'submitted') {
      return _buildSuccessPage(ticketState.ticketId!);
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('📧 Contact Support'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.support_agent,
                        size: 48,
                        color: AppColors.userPrimary,
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'We\'re here to help!',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Fill out the form below and we\'ll get back to you within 24 hours',
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Category Selection
              _buildSectionTitle('Category'),
              Card(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DropdownButtonFormField<String>(
                    value: selectedCategory,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: categories.map((category) => DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    )).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedCategory = value!;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Priority Selection
              _buildSectionTitle('Priority'),
              Card(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DropdownButtonFormField<String>(
                    value: selectedPriority,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Icon(Icons.priority_high),
                    ),
                    items: priorities.map((priority) => DropdownMenuItem(
                      value: priority,
                      child: Row(
                        children: [
                          _getPriorityIcon(priority),
                          const SizedBox(width: 8),
                          Text(priority),
                        ],
                      ),
                    )).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedPriority = value!;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Order ID (Optional)
              _buildSectionTitle('Order ID (Optional)'),
              Card(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextFormField(
                    controller: _orderIdController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Icon(Icons.receipt),
                      hintText: 'Enter order ID if related to an order',
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Subject
              _buildSectionTitle('Subject'),
              Card(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextFormField(
                    controller: _subjectController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Icon(Icons.subject),
                      hintText: 'Brief description of your issue',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a subject';
                      }
                      return null;
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Description
              _buildSectionTitle('Description'),
              Card(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextFormField(
                    controller: _descriptionController,
                    maxLines: 5,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Icon(Icons.description),
                      hintText: 'Please provide detailed information about your issue...',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please describe your issue';
                      }
                      if (value.trim().length < 10) {
                        return 'Please provide more details (at least 10 characters)';
                      }
                      return null;
                    },
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: ticketState.isSubmitting
                      ? null
                      : () => _submitTicket(ticketNotifier),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.userPrimary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: ticketState.isSubmitting
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text('Submitting...'),
                          ],
                        )
                      : const Text(
                          'Submit Support Request',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 16),

              // Alternative Contact Methods
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Other ways to reach us:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildContactMethod(
                        Icons.email,
                        'Email',
                        '<EMAIL>',
                        AppColors.info,
                      ),
                      _buildContactMethod(
                        Icons.phone,
                        'Phone',
                        '+91 1800-123-4567',
                        AppColors.success,
                      ),
                      _buildContactMethod(
                        Icons.chat,
                        'Live Chat',
                        'Available 9 AM - 9 PM',
                        AppColors.warning,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _getPriorityIcon(String priority) {
    switch (priority) {
      case 'Low':
        return const Icon(Icons.keyboard_arrow_down, color: AppColors.success);
      case 'Medium':
        return const Icon(Icons.remove, color: AppColors.warning);
      case 'High':
        return const Icon(Icons.keyboard_arrow_up, color: AppColors.secondaryOrange);
      case 'Urgent':
        return const Icon(Icons.priority_high, color: AppColors.error);
      default:
        return const Icon(Icons.help);
    }
  }

  Widget _buildContactMethod(IconData icon, String title, String subtitle, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _submitTicket(SupportTicketNotifier notifier) {
    if (_formKey.currentState!.validate()) {
      notifier.submitTicket(
        category: selectedCategory,
        subject: _subjectController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: selectedPriority,
        orderId: _orderIdController.text.trim().isEmpty 
            ? null 
            : _orderIdController.text.trim(),
      );
    }
  }

  Widget _buildSuccessPage(String ticketId) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('✅ Request Submitted'),
        backgroundColor: AppColors.success,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.check_circle,
                size: 80,
                color: AppColors.success,
              ),
              const SizedBox(height: 24),
              const Text(
                'Support Request Submitted!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Your ticket ID is: $ticketId',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColors.userPrimary,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'We\'ll get back to you within 24 hours. You can track your request in the "My Tickets" section.',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.userPrimary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: const Text('Back to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
