@echo off
echo ========================================
echo PROJEK USER APP - QUICK RUN
echo ========================================
echo.

echo Checking Flutter setup...
flutter doctor --android-licenses
echo.

echo Getting dependencies...
flutter pub get
echo.

echo Building and running User App...
echo.
echo Choose your platform:
echo 1. Android Device/Emulator
echo 2. Web Browser (Chrome)
echo 3. Windows Desktop
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo Starting User App on Android...
    flutter run --target lib/main_user.dart
) else if "%choice%"=="2" (
    echo Starting User App on Web...
    flutter run --target lib/main_user.dart -d chrome
) else if "%choice%"=="3" (
    echo Starting User App on Windows...
    flutter run --target lib/main_user.dart -d windows
) else (
    echo Invalid choice. Starting on default device...
    flutter run --target lib/main_user.dart
)

echo.
echo User App finished running.
pause
