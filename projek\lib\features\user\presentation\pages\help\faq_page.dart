import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

class FAQPage extends ConsumerStatefulWidget {
  const FAQPage({super.key});

  @override
  ConsumerState<FAQPage> createState() => _FAQPageState();
}

class _FAQPageState extends ConsumerState<FAQPage> {
  String selectedCategory = 'All';
  String searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  final List<String> categories = [
    'All',
    'Games & Rewards',
    'Shopping',
    'Delivery',
    'Wallet',
    'Account',
    'Technical',
  ];

  final List<FAQItem> faqs = [
    // Games & Rewards
    FAQItem(
      'How do I earn ProjekCoins?',
      'You can earn ProjekCoins by:\n• Playing the daily Spin & Earn game (5 spins per day)\n• Completing daily login streaks (7-day cycle)\n• Making purchases on the marketplace\n• Referring friends to the app\n• Participating in special events and promotions',
      'Games & Rewards',
    ),
    FAQItem(
      'How many times can I spin the wheel daily?',
      'You get 5 free spins every day. The spins reset at midnight (12:00 AM) every day. You can purchase additional spins through in-app purchases.',
      'Games & Rewards',
    ),
    FAQItem(
      'What happens if I miss a day in the login streak?',
      'If you miss a day, your login streak will reset back to Day 1. However, you won\'t lose any ProjekCoins you\'ve already earned. The streak rewards start fresh from the beginning.',
      'Games & Rewards',
    ),

    // Shopping
    FAQItem(
      'How do I place an order?',
      'To place an order:\n1. Browse products in the Marketplace\n2. Add items to your cart\n3. Review your order and apply any coupons\n4. Choose payment method (ProjekCoins, UPI, Cards)\n5. Confirm your delivery address\n6. Place the order and track it in real-time',
      'Shopping',
    ),
    FAQItem(
      'What payment methods are accepted?',
      'We accept:\n• ProjekCoins (our digital currency)\n• UPI payments (Google Pay, PhonePe, Paytm)\n• Credit/Debit cards (Visa, Mastercard, RuPay)\n• Net Banking\n• Digital wallets (Paytm, Amazon Pay)',
      'Shopping',
    ),
    FAQItem(
      'Can I cancel my order?',
      'Yes, you can cancel your order:\n• Before restaurant/store confirmation: Full refund\n• After confirmation but before pickup: 50% refund\n• After pickup by rider: No refund (contact support for special cases)\n\nRefunds are processed within 3-5 business days.',
      'Shopping',
    ),

    // Delivery
    FAQItem(
      'How do I track my delivery?',
      'You can track your delivery by:\n1. Going to "My Orders" section\n2. Selecting your active order\n3. Viewing real-time location of your rider\n4. Getting live updates via push notifications\n5. Calling the rider directly if needed',
      'Delivery',
    ),
    FAQItem(
      'What if my order is delayed?',
      'If your order is delayed:\n• Check the tracking page for updates\n• Contact the rider through the app\n• If delay exceeds 30 minutes, you\'ll get automatic compensation\n• Contact support for immediate assistance\n• You can cancel and get a full refund if delay is excessive',
      'Delivery',
    ),

    // Wallet
    FAQItem(
      'How do I add money to my ProjekCoin wallet?',
      'To add money to your wallet:\n1. Go to Wallet section\n2. Tap "Add Money"\n3. Enter the amount (minimum ₹10)\n4. Choose payment method\n5. Complete the payment\n6. Money will be added instantly as ProjekCoins (1 Rupee = 1 ProjekCoin)',
      'Wallet',
    ),
    FAQItem(
      'Can I transfer ProjekCoins to bank account?',
      'Currently, ProjekCoins can only be used within the Projek ecosystem for:\n• Making purchases\n• Paying for deliveries\n• Buying premium features\n• Gifting to other users\n\nDirect bank transfer feature is coming soon!',
      'Wallet',
    ),

    // Account
    FAQItem(
      'How do I update my profile information?',
      'To update your profile:\n1. Go to Profile section\n2. Tap "Edit Profile"\n3. Update your name, phone, email, or address\n4. Upload a new profile picture if needed\n5. Save changes\n\nNote: Phone number changes require OTP verification.',
      'Account',
    ),
    FAQItem(
      'How do I delete my account?',
      'To delete your account:\n1. Go to Settings > Account Settings\n2. Tap "Delete Account"\n3. Enter your password for confirmation\n4. Choose reason for deletion\n5. Confirm deletion\n\nWarning: This action is irreversible and you\'ll lose all ProjekCoins.',
      'Account',
    ),

    // Technical
    FAQItem(
      'The app is not working properly. What should I do?',
      'Try these troubleshooting steps:\n1. Close and restart the app\n2. Check your internet connection\n3. Update the app to the latest version\n4. Clear app cache (Android: Settings > Apps > Projek > Storage > Clear Cache)\n5. Restart your device\n6. If problem persists, contact support with error details',
      'Technical',
    ),
    FAQItem(
      'How do I enable push notifications?',
      'To enable notifications:\n\nAndroid:\n1. Settings > Apps > Projek > Notifications > Allow\n\niOS:\n1. Settings > Projek > Notifications > Allow\n\nIn-app:\n1. Profile > Settings > Notifications\n2. Toggle on the notifications you want to receive',
      'Technical',
    ),
  ];

  List<FAQItem> get filteredFAQs {
    List<FAQItem> filtered = faqs;
    
    if (selectedCategory != 'All') {
      filtered = filtered.where((faq) => faq.category == selectedCategory).toList();
    }
    
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((faq) => 
        faq.question.toLowerCase().contains(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().contains(searchQuery.toLowerCase())
      ).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('❓ Frequently Asked Questions'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColors.userPrimary.withOpacity(0.1),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search FAQs...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
                AnalyticsService.logEvent('faq_search', {'query': value});
              },
            ),
          ),

          // Category Filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = selectedCategory == category;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        selectedCategory = category;
                      });
                      AnalyticsService.logEvent('faq_category_selected', {'category': category});
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.userPrimary.withOpacity(0.2),
                    checkmarkColor: AppColors.userPrimary,
                  ),
                );
              },
            ),
          ),

          // FAQ List
          Expanded(
            child: filteredFAQs.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredFAQs.length,
                    itemBuilder: (context, index) {
                      final faq = filteredFAQs[index];
                      return _buildFAQCard(faq);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQCard(FAQItem faq) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        leading: CircleAvatar(
          backgroundColor: AppColors.userPrimary.withOpacity(0.1),
          child: Text(
            _getCategoryIcon(faq.category),
            style: const TextStyle(fontSize: 16),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  faq.answer,
                  style: TextStyle(
                    color: Colors.grey[700],
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Chip(
                      label: Text(
                        faq.category,
                        style: const TextStyle(fontSize: 12),
                      ),
                      backgroundColor: AppColors.userPrimary.withOpacity(0.1),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('👍 Thanks for your feedback!')),
                        );
                        AnalyticsService.logEvent('faq_helpful', {'question': faq.question});
                      },
                      icon: const Icon(Icons.thumb_up, size: 16),
                      label: const Text('Helpful'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
        onExpansionChanged: (expanded) {
          if (expanded) {
            AnalyticsService.logEvent('faq_expanded', {'question': faq.question});
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No FAQs found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or category filter',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                searchQuery = '';
                selectedCategory = 'All';
              });
            },
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  String _getCategoryIcon(String category) {
    switch (category) {
      case 'Games & Rewards':
        return '🎮';
      case 'Shopping':
        return '🛒';
      case 'Delivery':
        return '🚗';
      case 'Wallet':
        return '💰';
      case 'Account':
        return '👤';
      case 'Technical':
        return '⚙️';
      default:
        return '❓';
    }
  }
}

class FAQItem {
  final String question;
  final String answer;
  final String category;

  FAQItem(this.question, this.answer, this.category);
}
