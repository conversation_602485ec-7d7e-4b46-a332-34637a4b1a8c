// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'booking.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BookingAddressAdapter extends TypeAdapter<BookingAddress> {
  @override
  final int typeId = 43;

  @override
  BookingAddress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingAddress(
      street: fields[0] as String,
      city: fields[1] as String,
      state: fields[2] as String,
      pincode: fields[3] as String,
      landmark: fields[4] as String?,
      latitude: fields[5] as double?,
      longitude: fields[6] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, BookingAddress obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.street)
      ..writeByte(1)
      ..write(obj.city)
      ..writeByte(2)
      ..write(obj.state)
      ..writeByte(3)
      ..write(obj.pincode)
      ..writeByte(4)
      ..write(obj.landmark)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingAddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingScheduleAdapter extends TypeAdapter<BookingSchedule> {
  @override
  final int typeId = 44;

  @override
  BookingSchedule read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingSchedule(
      scheduledDate: fields[0] as DateTime,
      startTime: fields[1] as String,
      endTime: fields[2] as String,
      durationHours: fields[3] as int,
      isFlexible: fields[4] as bool,
      notes: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BookingSchedule obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.scheduledDate)
      ..writeByte(1)
      ..write(obj.startTime)
      ..writeByte(2)
      ..write(obj.endTime)
      ..writeByte(3)
      ..write(obj.durationHours)
      ..writeByte(4)
      ..write(obj.isFlexible)
      ..writeByte(5)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingScheduleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingCancellationAdapter extends TypeAdapter<BookingCancellation> {
  @override
  final int typeId = 45;

  @override
  BookingCancellation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingCancellation(
      reason: fields[0] as CancellationReason,
      description: fields[1] as String?,
      cancelledAt: fields[2] as DateTime,
      cancelledBy: fields[3] as String,
      refundAmount: fields[4] as double,
      cancellationFee: fields[5] as double,
    );
  }

  @override
  void write(BinaryWriter writer, BookingCancellation obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.reason)
      ..writeByte(1)
      ..write(obj.description)
      ..writeByte(2)
      ..write(obj.cancelledAt)
      ..writeByte(3)
      ..write(obj.cancelledBy)
      ..writeByte(4)
      ..write(obj.refundAmount)
      ..writeByte(5)
      ..write(obj.cancellationFee);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingCancellationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingConfirmationAdapter extends TypeAdapter<BookingConfirmation> {
  @override
  final int typeId = 46;

  @override
  BookingConfirmation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingConfirmation(
      confirmedAt: fields[0] as DateTime,
      confirmedBy: fields[1] as String,
      confirmationMessage: fields[2] as String?,
      additionalDetails: (fields[3] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, BookingConfirmation obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.confirmedAt)
      ..writeByte(1)
      ..write(obj.confirmedBy)
      ..writeByte(2)
      ..write(obj.confirmationMessage)
      ..writeByte(3)
      ..write(obj.additionalDetails);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingConfirmationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingAdapter extends TypeAdapter<Booking> {
  @override
  final int typeId = 47;

  @override
  Booking read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Booking(
      id: fields[0] as String,
      serviceId: fields[1] as String,
      userId: fields[2] as String,
      providerId: fields[3] as String,
      serviceName: fields[4] as String,
      providerName: fields[5] as String,
      providerPhone: fields[6] as String,
      providerEmail: fields[7] as String?,
      schedule: fields[8] as BookingSchedule,
      address: fields[9] as BookingAddress,
      status: fields[10] as BookingStatus,
      paymentStatus: fields[11] as PaymentStatus,
      totalAmount: fields[12] as double,
      serviceAmount: fields[13] as double,
      platformFee: fields[14] as double,
      taxes: fields[15] as double,
      currency: fields[16] as String,
      paymentId: fields[17] as String?,
      specialInstructions: fields[18] as String?,
      requirements: (fields[19] as List).cast<String>(),
      confirmation: fields[20] as BookingConfirmation?,
      cancellation: fields[21] as BookingCancellation?,
      createdAt: fields[22] as DateTime,
      updatedAt: fields[23] as DateTime,
      startedAt: fields[24] as DateTime?,
      completedAt: fields[25] as DateTime?,
      rating: fields[26] as double?,
      review: fields[27] as String?,
      attachments: (fields[28] as List).cast<String>(),
      metadata: (fields[29] as Map).cast<String, dynamic>(),
      bookingNumber: fields[30] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Booking obj) {
    writer
      ..writeByte(31)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.serviceId)
      ..writeByte(2)
      ..write(obj.userId)
      ..writeByte(3)
      ..write(obj.providerId)
      ..writeByte(4)
      ..write(obj.serviceName)
      ..writeByte(5)
      ..write(obj.providerName)
      ..writeByte(6)
      ..write(obj.providerPhone)
      ..writeByte(7)
      ..write(obj.providerEmail)
      ..writeByte(8)
      ..write(obj.schedule)
      ..writeByte(9)
      ..write(obj.address)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.paymentStatus)
      ..writeByte(12)
      ..write(obj.totalAmount)
      ..writeByte(13)
      ..write(obj.serviceAmount)
      ..writeByte(14)
      ..write(obj.platformFee)
      ..writeByte(15)
      ..write(obj.taxes)
      ..writeByte(16)
      ..write(obj.currency)
      ..writeByte(17)
      ..write(obj.paymentId)
      ..writeByte(18)
      ..write(obj.specialInstructions)
      ..writeByte(19)
      ..write(obj.requirements)
      ..writeByte(20)
      ..write(obj.confirmation)
      ..writeByte(21)
      ..write(obj.cancellation)
      ..writeByte(22)
      ..write(obj.createdAt)
      ..writeByte(23)
      ..write(obj.updatedAt)
      ..writeByte(24)
      ..write(obj.startedAt)
      ..writeByte(25)
      ..write(obj.completedAt)
      ..writeByte(26)
      ..write(obj.rating)
      ..writeByte(27)
      ..write(obj.review)
      ..writeByte(28)
      ..write(obj.attachments)
      ..writeByte(29)
      ..write(obj.metadata)
      ..writeByte(30)
      ..write(obj.bookingNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingStatusAdapter extends TypeAdapter<BookingStatus> {
  @override
  final int typeId = 40;

  @override
  BookingStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BookingStatus.pending;
      case 1:
        return BookingStatus.confirmed;
      case 2:
        return BookingStatus.inProgress;
      case 3:
        return BookingStatus.completed;
      case 4:
        return BookingStatus.cancelled;
      case 5:
        return BookingStatus.rejected;
      case 6:
        return BookingStatus.rescheduled;
      case 7:
        return BookingStatus.noShow;
      default:
        return BookingStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, BookingStatus obj) {
    switch (obj) {
      case BookingStatus.pending:
        writer.writeByte(0);
        break;
      case BookingStatus.confirmed:
        writer.writeByte(1);
        break;
      case BookingStatus.inProgress:
        writer.writeByte(2);
        break;
      case BookingStatus.completed:
        writer.writeByte(3);
        break;
      case BookingStatus.cancelled:
        writer.writeByte(4);
        break;
      case BookingStatus.rejected:
        writer.writeByte(5);
        break;
      case BookingStatus.rescheduled:
        writer.writeByte(6);
        break;
      case BookingStatus.noShow:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 41;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.pending;
      case 1:
        return PaymentStatus.paid;
      case 2:
        return PaymentStatus.failed;
      case 3:
        return PaymentStatus.refunded;
      case 4:
        return PaymentStatus.partialRefund;
      default:
        return PaymentStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.pending:
        writer.writeByte(0);
        break;
      case PaymentStatus.paid:
        writer.writeByte(1);
        break;
      case PaymentStatus.failed:
        writer.writeByte(2);
        break;
      case PaymentStatus.refunded:
        writer.writeByte(3);
        break;
      case PaymentStatus.partialRefund:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CancellationReasonAdapter extends TypeAdapter<CancellationReason> {
  @override
  final int typeId = 42;

  @override
  CancellationReason read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CancellationReason.userCancelled;
      case 1:
        return CancellationReason.providerCancelled;
      case 2:
        return CancellationReason.emergencyIssue;
      case 3:
        return CancellationReason.weatherConditions;
      case 4:
        return CancellationReason.technicalIssue;
      case 5:
        return CancellationReason.noShow;
      case 6:
        return CancellationReason.other;
      default:
        return CancellationReason.userCancelled;
    }
  }

  @override
  void write(BinaryWriter writer, CancellationReason obj) {
    switch (obj) {
      case CancellationReason.userCancelled:
        writer.writeByte(0);
        break;
      case CancellationReason.providerCancelled:
        writer.writeByte(1);
        break;
      case CancellationReason.emergencyIssue:
        writer.writeByte(2);
        break;
      case CancellationReason.weatherConditions:
        writer.writeByte(3);
        break;
      case CancellationReason.technicalIssue:
        writer.writeByte(4);
        break;
      case CancellationReason.noShow:
        writer.writeByte(5);
        break;
      case CancellationReason.other:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CancellationReasonAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BookingAddress _$BookingAddressFromJson(Map<String, dynamic> json) =>
    BookingAddress(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      pincode: json['pincode'] as String,
      landmark: json['landmark'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$BookingAddressToJson(BookingAddress instance) =>
    <String, dynamic>{
      'street': instance.street,
      'city': instance.city,
      'state': instance.state,
      'pincode': instance.pincode,
      'landmark': instance.landmark,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

BookingSchedule _$BookingScheduleFromJson(Map<String, dynamic> json) =>
    BookingSchedule(
      scheduledDate: DateTime.parse(json['scheduledDate'] as String),
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      durationHours: (json['durationHours'] as num).toInt(),
      isFlexible: json['isFlexible'] as bool? ?? false,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$BookingScheduleToJson(BookingSchedule instance) =>
    <String, dynamic>{
      'scheduledDate': instance.scheduledDate.toIso8601String(),
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'durationHours': instance.durationHours,
      'isFlexible': instance.isFlexible,
      'notes': instance.notes,
    };

BookingCancellation _$BookingCancellationFromJson(Map<String, dynamic> json) =>
    BookingCancellation(
      reason: $enumDecode(_$CancellationReasonEnumMap, json['reason']),
      description: json['description'] as String?,
      cancelledAt: DateTime.parse(json['cancelledAt'] as String),
      cancelledBy: json['cancelledBy'] as String,
      refundAmount: (json['refundAmount'] as num?)?.toDouble() ?? 0.0,
      cancellationFee: (json['cancellationFee'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$BookingCancellationToJson(
        BookingCancellation instance) =>
    <String, dynamic>{
      'reason': _$CancellationReasonEnumMap[instance.reason]!,
      'description': instance.description,
      'cancelledAt': instance.cancelledAt.toIso8601String(),
      'cancelledBy': instance.cancelledBy,
      'refundAmount': instance.refundAmount,
      'cancellationFee': instance.cancellationFee,
    };

const _$CancellationReasonEnumMap = {
  CancellationReason.userCancelled: 'userCancelled',
  CancellationReason.providerCancelled: 'providerCancelled',
  CancellationReason.emergencyIssue: 'emergencyIssue',
  CancellationReason.weatherConditions: 'weatherConditions',
  CancellationReason.technicalIssue: 'technicalIssue',
  CancellationReason.noShow: 'noShow',
  CancellationReason.other: 'other',
};

BookingConfirmation _$BookingConfirmationFromJson(Map<String, dynamic> json) =>
    BookingConfirmation(
      confirmedAt: DateTime.parse(json['confirmedAt'] as String),
      confirmedBy: json['confirmedBy'] as String,
      confirmationMessage: json['confirmationMessage'] as String?,
      additionalDetails:
          json['additionalDetails'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$BookingConfirmationToJson(
        BookingConfirmation instance) =>
    <String, dynamic>{
      'confirmedAt': instance.confirmedAt.toIso8601String(),
      'confirmedBy': instance.confirmedBy,
      'confirmationMessage': instance.confirmationMessage,
      'additionalDetails': instance.additionalDetails,
    };

Booking _$BookingFromJson(Map<String, dynamic> json) => Booking(
      id: json['id'] as String,
      serviceId: json['serviceId'] as String,
      userId: json['userId'] as String,
      providerId: json['providerId'] as String,
      serviceName: json['serviceName'] as String,
      providerName: json['providerName'] as String,
      providerPhone: json['providerPhone'] as String,
      providerEmail: json['providerEmail'] as String?,
      schedule:
          BookingSchedule.fromJson(json['schedule'] as Map<String, dynamic>),
      address: BookingAddress.fromJson(json['address'] as Map<String, dynamic>),
      status: $enumDecode(_$BookingStatusEnumMap, json['status']),
      paymentStatus: $enumDecode(_$PaymentStatusEnumMap, json['paymentStatus']),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      serviceAmount: (json['serviceAmount'] as num).toDouble(),
      platformFee: (json['platformFee'] as num?)?.toDouble() ?? 0.0,
      taxes: (json['taxes'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'INR',
      paymentId: json['paymentId'] as String?,
      specialInstructions: json['specialInstructions'] as String?,
      requirements: (json['requirements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      confirmation: json['confirmation'] == null
          ? null
          : BookingConfirmation.fromJson(
              json['confirmation'] as Map<String, dynamic>),
      cancellation: json['cancellation'] == null
          ? null
          : BookingCancellation.fromJson(
              json['cancellation'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      rating: (json['rating'] as num?)?.toDouble(),
      review: json['review'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      bookingNumber: json['bookingNumber'] as String,
    );

Map<String, dynamic> _$BookingToJson(Booking instance) => <String, dynamic>{
      'id': instance.id,
      'serviceId': instance.serviceId,
      'userId': instance.userId,
      'providerId': instance.providerId,
      'serviceName': instance.serviceName,
      'providerName': instance.providerName,
      'providerPhone': instance.providerPhone,
      'providerEmail': instance.providerEmail,
      'schedule': instance.schedule,
      'address': instance.address,
      'status': _$BookingStatusEnumMap[instance.status]!,
      'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus]!,
      'totalAmount': instance.totalAmount,
      'serviceAmount': instance.serviceAmount,
      'platformFee': instance.platformFee,
      'taxes': instance.taxes,
      'currency': instance.currency,
      'paymentId': instance.paymentId,
      'specialInstructions': instance.specialInstructions,
      'requirements': instance.requirements,
      'confirmation': instance.confirmation,
      'cancellation': instance.cancellation,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'startedAt': instance.startedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'rating': instance.rating,
      'review': instance.review,
      'attachments': instance.attachments,
      'metadata': instance.metadata,
      'bookingNumber': instance.bookingNumber,
    };

const _$BookingStatusEnumMap = {
  BookingStatus.pending: 'pending',
  BookingStatus.confirmed: 'confirmed',
  BookingStatus.inProgress: 'inProgress',
  BookingStatus.completed: 'completed',
  BookingStatus.cancelled: 'cancelled',
  BookingStatus.rejected: 'rejected',
  BookingStatus.rescheduled: 'rescheduled',
  BookingStatus.noShow: 'noShow',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.paid: 'paid',
  PaymentStatus.failed: 'failed',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.partialRefund: 'partialRefund',
};
