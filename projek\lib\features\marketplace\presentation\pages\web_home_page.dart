import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/utils/app_router.dart';
import '../../../../core/utils/responsive_layout.dart';

import '../../../../core/theme/app_colors.dart';
import '../../domain/models/product.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../wishlist/presentation/providers/wishlist_provider.dart';

class WebHomePage extends ConsumerStatefulWidget {
  const WebHomePage({super.key});

  @override
  ConsumerState<WebHomePage> createState() => _WebHomePageState();
}

class _WebHomePageState extends ConsumerState<WebHomePage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: _buildWebAppBar(theme, colorScheme),
      body: SingleChildScrollView(
        child: ResponsiveContainer(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hero Section
              _buildHeroSection(),

              const SizedBox(height: 32),

              // Search Bar
              _buildSearchBar(),

              const SizedBox(height: 32),

              // Categories Section
              _buildCategoriesSection(),

              const SizedBox(height: 32),

              // Featured Products
              _buildFeaturedProducts(),

              const SizedBox(height: 32),

              // Popular Products
              _buildPopularProducts(),

              const SizedBox(height: 32),

              // Services Section
              _buildServicesSection(),

              const SizedBox(height: 48),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildWebAppBar(
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return AppBar(
      backgroundColor: colorScheme.surface,
      elevation: 0,
      scrolledUnderElevation: 1,
      title: Row(
        children: [
          Icon(Icons.shopping_bag, color: colorScheme.primary, size: 28),
          const SizedBox(width: 12),
          Text(
            'Projek Marketplace',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
        ],
      ),
      actions: [
        // Search Icon (for mobile)
        if (ResponsiveLayout.isMobile(context))
          IconButton(
            onPressed: () => context.push(AppRoutes.search),
            icon: const Icon(Icons.search),
          ),

        // Cart Icon
        Consumer(
          builder: (context, ref, child) {
            final cartItemCount = ref.watch(cartItemCountProvider);
            return IconButton(
              onPressed: () => context.push(AppRoutes.cart),
              icon: Badge(
                isLabelVisible: cartItemCount > 0,
                label: Text(cartItemCount.toString()),
                child: const Icon(Icons.shopping_cart_outlined),
              ),
            );
          },
        ),

        // Wishlist Icon
        Consumer(
          builder: (context, ref, child) {
            final wishlistItemCount = ref.watch(wishlistItemCountProvider);
            return IconButton(
              onPressed: () => context.push(AppRoutes.wishlist),
              icon: Badge(
                isLabelVisible: wishlistItemCount > 0,
                label: Text(wishlistItemCount.toString()),
                child: const Icon(Icons.favorite_border),
              ),
            );
          },
        ),

        // Profile Icon
        IconButton(
          onPressed: () => context.push(AppRoutes.profile),
          icon: const Icon(Icons.person_outline),
        ),

        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildHeroSection() {
    return ResponsiveBuilder(
      builder: (context, isMobile, isTablet, isDesktop) {
        return Container(
          height: ResponsiveLayout.getResponsiveValue(
            context,
            mobile: 200.0,
            tablet: 300.0,
            desktop: 400.0,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: ResponsiveLayout.getResponsivePadding(context),
            child: Row(
              children: [
                Expanded(
                  flex: isDesktop ? 2 : 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Welcome to Projek',
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: ResponsiveLayout.getResponsiveFontSize(
                                context,
                                baseFontSize: 28,
                              ),
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Your one-stop marketplace for everything you need',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: ResponsiveLayout.getResponsiveFontSize(
                            context,
                            baseFontSize: 16,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => context.push(AppRoutes.categories),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Theme.of(
                            context,
                          ).colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: const Text('Shop Now'),
                      ),
                    ],
                  ),
                ),
                if (isDesktop || isTablet)
                  Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.shopping_cart,
                        size: ResponsiveLayout.getResponsiveValue(
                          context,
                          mobile: 80.0,
                          tablet: 120.0,
                          desktop: 150.0,
                        ),
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchBar() {
    if (ResponsiveLayout.isMobile(context)) {
      return const SizedBox.shrink(); // Hide on mobile, use app bar search
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 600),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search products, categories, brands...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            onPressed: () => _handleSearch(),
            icon: const Icon(Icons.arrow_forward),
          ),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
        ),
        onSubmitted: (_) => _handleSearch(),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Shop by Category',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => context.push(AppRoutes.categories),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ResponsiveGridView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: _buildCategoryCards(),
        ),
      ],
    );
  }

  List<Widget> _buildCategoryCards() {
    final categories = [
      {
        'name': 'Electronics',
        'icon': Icons.devices,
        'color': AppColors.categoryColors['Electronics']!,
      },
      {
        'name': 'Fashion',
        'icon': Icons.checkroom,
        'color': AppColors.categoryColors['Fashion']!,
      },
      {
        'name': 'Home & Garden',
        'icon': Icons.home,
        'color': AppColors.categoryColors['Home & Garden']!,
      },
      {
        'name': 'Sports',
        'icon': Icons.sports,
        'color': AppColors.categoryColors['Sports']!,
      },
      {
        'name': 'Books',
        'icon': Icons.book,
        'color': AppColors.categoryColors['Books & Media']!,
      },
      {
        'name': 'Health',
        'icon': Icons.health_and_safety,
        'color': AppColors.categoryColors['Health & Beauty']!,
      },
    ];

    return categories
        .map(
          (category) => _buildCategoryCard(
            category['name'] as String,
            category['icon'] as IconData,
            category['color'] as Color,
          ),
        )
        .toList();
  }

  Widget _buildCategoryCard(String name, IconData icon, Color color) {
    return GestureDetector(
      onTap: () => context.push(
        '${AppRoutes.categoriesFiltered}?type=${name.toLowerCase()}&title=$name',
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              name,
              style: TextStyle(fontWeight: FontWeight.w600, color: color),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedProducts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Products',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => context.push(AppRoutes.search),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: _getFeaturedProducts().length,
            itemBuilder: (context, index) =>
                _buildProductCard(_getFeaturedProducts()[index]),
          ),
        ),
      ],
    );
  }

  Widget _buildPopularProducts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Products',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ResponsiveGridView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: _getPopularProducts()
              .map((product) => _buildProductCard(product))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildServicesSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Our Services',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ResponsiveWrap(
            children: [
              _buildServiceCard(
                'Fast Delivery',
                Icons.local_shipping,
                'Get your orders delivered quickly',
              ),
              _buildServiceCard(
                '24/7 Support',
                Icons.support_agent,
                'Round-the-clock customer support',
              ),
              _buildServiceCard(
                'Secure Payment',
                Icons.security,
                'Safe and secure payment options',
              ),
              _buildServiceCard(
                'Easy Returns',
                Icons.keyboard_return,
                'Hassle-free return policy',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(String title, IconData icon, String description) {
    return Container(
      width: ResponsiveLayout.getResponsiveValue(
        context,
        mobile: double.infinity,
        tablet: 250.0,
        desktop: 280.0,
      ),
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Theme.of(context).colorScheme.primary, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return Container(
      width: ResponsiveLayout.getCardWidth(context),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Card(
        elevation: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Icon(Icons.image, size: 48, color: Colors.grey.shade400),
              ),
            ),
            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '₹${product.price.toStringAsFixed(0)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => _addToCart(product),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                            ),
                            child: const Text('Add to Cart'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Consumer(
                          builder: (context, ref, child) {
                            final isInWishlist = ref.watch(
                              isProductInWishlistProvider(product.id),
                            );
                            return IconButton(
                              onPressed: () => _toggleWishlist(product),
                              icon: Icon(
                                isInWishlist
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: isInWishlist ? Colors.red : null,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      context.push('${AppRoutes.search}?q=$query');
    }
  }

  Future<void> _addToCart(Product product) async {
    try {
      await ref.read(cartProvider.notifier).addProduct(product);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${product.name} added to cart'),
            action: SnackBarAction(
              label: 'View Cart',
              onPressed: () => context.push(AppRoutes.cart),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleWishlist(Product product) async {
    try {
      final isInWishlist = ref.read(isProductInWishlistProvider(product.id));
      if (isInWishlist) {
        await ref.read(wishlistProvider.notifier).removeProduct(product.id);
      } else {
        await ref.read(wishlistProvider.notifier).addProduct(product);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  List<Product> _getFeaturedProducts() {
    return [
      Product(
        id: '1',
        name: 'Wireless Headphones',
        description: 'High-quality wireless headphones with noise cancellation',
        price: 2999.0,
        originalPrice: 3999.0,
        images: [''],
        category: 'Electronics',
        subcategory: 'Audio',
        brand: 'TechStore',
        rating: 4.5,
        reviewCount: 128,
        stockQuantity: 50,
        vendorId: 'vendor1',
        vendorName: 'TechStore',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Product(
        id: '2',
        name: 'Smart Watch',
        description: 'Feature-rich smartwatch with health monitoring',
        price: 4999.0,
        originalPrice: 5999.0,
        images: [''],
        category: 'Electronics',
        subcategory: 'Wearables',
        brand: 'GadgetHub',
        rating: 4.3,
        reviewCount: 89,
        stockQuantity: 30,
        vendorId: 'vendor2',
        vendorName: 'GadgetHub',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Product(
        id: '3',
        name: 'Running Shoes',
        description: 'Comfortable running shoes for daily workouts',
        price: 1999.0,
        originalPrice: 2499.0,
        images: [''],
        category: 'Sports',
        subcategory: 'Footwear',
        brand: 'SportsMart',
        rating: 4.7,
        reviewCount: 156,
        stockQuantity: 75,
        vendorId: 'vendor3',
        vendorName: 'SportsMart',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  List<Product> _getPopularProducts() {
    return [
      Product(
        id: '4',
        name: 'Bluetooth Speaker',
        description: 'Portable Bluetooth speaker with excellent sound quality',
        price: 1499.0,
        originalPrice: 1999.0,
        images: [''],
        category: 'Electronics',
        subcategory: 'Audio',
        brand: 'AudioWorld',
        rating: 4.4,
        reviewCount: 203,
        stockQuantity: 40,
        vendorId: 'vendor4',
        vendorName: 'AudioWorld',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Product(
        id: '5',
        name: 'Coffee Maker',
        description: 'Automatic coffee maker for perfect morning brew',
        price: 3499.0,
        originalPrice: 4299.0,
        images: [''],
        category: 'Home & Garden',
        subcategory: 'Kitchen',
        brand: 'KitchenPro',
        rating: 4.6,
        reviewCount: 67,
        stockQuantity: 25,
        vendorId: 'vendor5',
        vendorName: 'KitchenPro',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Product(
        id: '6',
        name: 'Yoga Mat',
        description: 'Premium yoga mat for comfortable workouts',
        price: 899.0,
        originalPrice: 1199.0,
        images: [''],
        category: 'Sports',
        subcategory: 'Fitness',
        brand: 'FitnessCo',
        rating: 4.5,
        reviewCount: 94,
        stockQuantity: 60,
        vendorId: 'vendor6',
        vendorName: 'FitnessCo',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Product(
        id: '7',
        name: 'Desk Lamp',
        description: 'LED desk lamp with adjustable brightness',
        price: 1299.0,
        originalPrice: 1599.0,
        images: [''],
        category: 'Home & Garden',
        subcategory: 'Lighting',
        brand: 'LightingPlus',
        rating: 4.2,
        reviewCount: 45,
        stockQuantity: 35,
        vendorId: 'vendor7',
        vendorName: 'LightingPlus',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}
