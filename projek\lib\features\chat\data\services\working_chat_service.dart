import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/working_chat_models.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';

class WorkingChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static const Uuid _uuid = Uuid();

  static const String _chatsCollection = 'working_chats';
  static const String _messagesCollection = 'working_messages';

  // Create a new chat
  static Future<WorkingChat> createChat({
    required String name,
    required String type,
    required List<String> participantIds,
    String? description,
    String? orderId,
    String? serviceId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final chatId = _uuid.v4();
      final now = DateTime.now();

      final chat = WorkingChat(
        id: chatId,
        name: name,
        description: description,
        type: type,
        participantIds: participantIds,
        createdAt: now,
        updatedAt: now,
        orderId: orderId,
        serviceId: serviceId,
      );

      // Save to Firestore
      await _firestore.collection(_chatsCollection).doc(chatId).set(chat.toFirestore());

      // Log analytics
      await AnalyticsService.logEvent('working_chat_created', {
        'chat_type': type,
        'participant_count': participantIds.length,
        'has_order': orderId != null,
        'has_service': serviceId != null,
      });

      return chat;
    } catch (e) {
      debugPrint('❌ Error creating chat: $e');
      throw Exception('Failed to create chat: ${e.toString()}');
    }
  }

  // Send a message
  static Future<WorkingChatMessage> sendMessage({
    required String chatId,
    required String content,
    String type = 'text',
    List<String> attachmentUrls = const [],
    String? replyToMessageId, required Map<String, double> extraData,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final messageId = _uuid.v4();
      final now = DateTime.now();

      // Get user info
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      final userData = userDoc.data() ?? {};

      final message = WorkingChatMessage(
        id: messageId,
        chatId: chatId,
        senderId: currentUser.uid,
        senderName: userData['name'] ?? currentUser.displayName ?? 'User',
        senderAvatarUrl: userData['avatarUrl'] ?? currentUser.photoURL,
        type: type,
        content: content,
        attachmentUrls: attachmentUrls,
        replyToMessageId: replyToMessageId,
        timestamp: now,
        status: 'sending',
      );

      // Save message
      await _firestore.collection(_messagesCollection).doc(messageId).set(message.toFirestore());

      // Update chat last message
      await _updateChatLastMessage(chatId, message);

      // Update message status to sent
      await _updateMessageStatus(messageId, 'sent');

      // Send notifications to other participants
      await _sendMessageNotifications(chatId, message);

      return message;
    } catch (e) {
      debugPrint('❌ Error sending message: $e');
      throw Exception('Failed to send message: ${e.toString()}');
    }
  }

  // Send message with media
  static Future<WorkingChatMessage> sendMessageWithMedia({
    required String chatId,
    required String content,
    required List<File> mediaFiles,
    String type = 'image',
    String? replyToMessageId,
  }) async {
    try {
      // Upload media files
      final attachmentUrls = <String>[];
      for (final file in mediaFiles) {
        final url = await _uploadMediaFile(chatId, file);
        attachmentUrls.add(url);
      }

      // Send message with attachments
      return await sendMessage(
        chatId: chatId,
        content: content,
        type: type,
        attachmentUrls: attachmentUrls,
        replyToMessageId: replyToMessageId,
        extraData: {},
      );
    } catch (e) {
      debugPrint('❌ Error sending media message: $e');
      throw Exception('Failed to send media message: ${e.toString()}');
    }
  }

  // Upload media file
  static Future<String> _uploadMediaFile(String chatId, File file) async {
    try {
      final fileName = '${_uuid.v4()}_${file.path.split('/').last}';
      final ref = _storage.ref().child('chat_media/$chatId/$fileName');

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      debugPrint('❌ Error uploading media: $e');
      throw Exception('Failed to upload media: ${e.toString()}');
    }
  }

  // Get chat by ID
  static Future<WorkingChat> getChatById(String chatId) async {
    try {
      final doc = await _firestore.collection(_chatsCollection).doc(chatId).get();
      if (!doc.exists) throw Exception('Chat not found');
      
      return WorkingChat.fromFirestore(doc);
    } catch (e) {
      debugPrint('❌ Error getting chat: $e');
      throw Exception('Failed to get chat: ${e.toString()}');
    }
  }

  // Get user chats stream
  static Stream<List<WorkingChat>> getUserChatsStream({String? userId}) {
    final currentUserId = userId ?? _auth.currentUser?.uid;
    if (currentUserId == null) return Stream.value([]);

    return _firestore
        .collection(_chatsCollection)
        .where('participantIds', arrayContains: currentUserId)
        .where('isActive', isEqualTo: true)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => WorkingChat.fromFirestore(doc))
          .toList();
    });
  }

  // Get chat messages stream
  static Stream<List<WorkingChatMessage>> getChatMessagesStream(String chatId) {
    return _firestore
        .collection(_messagesCollection)
        .where('chatId', isEqualTo: chatId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => WorkingChatMessage.fromFirestore(doc))
          .toList();
    });
  }

  // Mark messages as read
  static Future<void> markMessagesAsRead({
    required String chatId,
    required List<String> messageIds,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final batch = _firestore.batch();
      
      for (final messageId in messageIds) {
        final messageRef = _firestore.collection(_messagesCollection).doc(messageId);
        batch.update(messageRef, {
          'readBy': FieldValue.arrayUnion([currentUser.uid]),
          'status': 'read',
        });
      }

      // Update unread count in chat
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'unreadCounts.${currentUser.uid}': 0,
      });

      await batch.commit();
    } catch (e) {
      debugPrint('❌ Error marking messages as read: $e');
    }
  }

  // Add reaction to a message
  static Future<void> addReaction({
    required String messageId,
    required String emoji,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore.collection(_messagesCollection).doc(messageId).update({
        'reactions.${currentUser.uid}': emoji,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Log analytics
      await AnalyticsService.logEvent('message_reaction_added', {
        'message_id': messageId,
        'emoji': emoji,
      });
    } catch (e) {
      debugPrint('❌ Error adding reaction: $e');
      throw Exception('Failed to add reaction: ${e.toString()}');
    }
  }

  // Helper methods
  static Future<void> _updateChatLastMessage(
    String chatId,
    WorkingChatMessage message,
  ) async {
    await _firestore.collection(_chatsCollection).doc(chatId).update({
      'lastMessage': message.content,
      'lastMessageTime': Timestamp.fromDate(message.timestamp),
      'lastMessageSenderId': message.senderId,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  static Future<void> _updateMessageStatus(
    String messageId,
    String status,
  ) async {
    await _firestore.collection(_messagesCollection).doc(messageId).update({
      'status': status,
    });
  }

  static Future<void> _sendMessageNotifications(
    String chatId,
    WorkingChatMessage message,
  ) async {
    try {
      final chat = await getChatById(chatId);
      
      for (final participantId in chat.participantIds) {
        if (participantId != message.senderId) {
          await NotificationService.showLocalNotification(
            title: '💬 ${message.senderName}',
            body: _getNotificationBody(message),
            payload: chatId,
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error sending notifications: $e');
    }
  }

  static String _getNotificationBody(WorkingChatMessage message) {
    switch (message.type) {
      case 'text':
        return message.content;
      case 'image':
        return '📷 Photo';
      case 'video':
        return '🎥 Video';
      case 'audio':
        return '🎵 Audio';
      case 'file':
        return '📄 Document';
      case 'location':
        return '📍 Location';
      default:
        return 'New message';
    }
  }
}



