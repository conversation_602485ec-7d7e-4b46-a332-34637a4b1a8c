import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../shared/widgets/common_widgets.dart';
import '../../domain/models/product.dart';
import 'category_items_page.dart';

class FilteredCategoriesPage extends StatefulWidget {
  final String filterType;
  final String title;

  const FilteredCategoriesPage({
    super.key,
    required this.filterType,
    required this.title,
  });

  @override
  State<FilteredCategoriesPage> createState() => _FilteredCategoriesPageState();
}

class _FilteredCategoriesPageState extends State<FilteredCategoriesPage> {
  List<Category> _getFilteredCategories() {
    switch (widget.filterType.toLowerCase()) {
      case 'restaurants':
      case 'food':
        return SampleCategories.foodCategories;
      case 'products':
      case 'featured':
        return SampleCategories.productCategories;
      case 'grocery':
        return SampleCategories.productCategories
            .where(
              (cat) =>
                  cat.name.toLowerCase().contains('food') ||
                  cat.name.toLowerCase().contains('grocery') ||
                  cat.name.toLowerCase().contains('beverages'),
            )
            .toList();
      case 'services':
        // Return service-related categories
        return SampleCategories.productCategories
            .where(
              (cat) =>
                  cat.name.toLowerCase().contains('service') ||
                  cat.name.toLowerCase().contains('repair') ||
                  cat.name.toLowerCase().contains('beauty'),
            )
            .toList();
      default:
        return [
          ...SampleCategories.productCategories,
          ...SampleCategories.foodCategories,
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredCategories = _getFilteredCategories();

    return Scaffold(
      appBar: AppBar(title: Text(widget.title), centerTitle: true),
      body: filteredCategories.isEmpty
          ? _buildEmptyState()
          : GridView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.0,
              ),
              itemCount: filteredCategories.length,
              itemBuilder: (context, index) {
                final category = filteredCategories[index];
                return FadeInWidget(
                  delay: Duration(milliseconds: index * 100),
                  child: _buildCategoryCard(category),
                );
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyStateWidget(
      title: 'No categories found',
      message: 'Try browsing all categories instead',
      icon: Icons.category_outlined,
      actionText: 'Browse All',
      onAction: () => Navigator.of(context).pop(),
    );
  }

  Widget _buildCategoryCard(Category category) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final color = Color(int.parse(category.colorHex.replaceFirst('#', '0xFF')));

    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _handleCategoryTap(category),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            children: [
              // Category Image
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: colorScheme.surfaceContainerHighest,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildCategoryImage(category.imageUrl),
                  ),
                ),
              ),

              // Category Info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      Text(
                        category.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${category.itemCount} items',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryImage(String imageUrl) {
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(
        imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildImagePlaceholder(),
      );
    } else {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildImagePlaceholder(),
        errorWidget: (context, url, error) => _buildImagePlaceholder(),
      );
    }
  }

  Widget _buildImagePlaceholder() {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.image,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        size: 32,
      ),
    );
  }

  void _handleCategoryTap(Category category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CategoryItemsPage(
          categoryId: category.id,
          categoryName: category.name,
        ),
      ),
    );
  }
}
