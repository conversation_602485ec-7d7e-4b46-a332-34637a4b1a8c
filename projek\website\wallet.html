<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>💰 Wallet - 👁️ PROJEK Super Vision</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="css/wallet.css">
</head>
<body>
  <!-- Header -->
  <header class="header">
    <nav class="nav-container">
      <div class="logo">
        <img src="assets/image/logo/app_logo.png" alt="PROJEK Logo">
        <span class="logo-icon">👁️</span>
        <span>PROJEK Super Vision</span>
      </div>
      
      <ul class="nav-links">
        <li><a href="index.html">Home</a></li>
        <li><a href="booking.html">Book Now</a></li>
        <li><a href="gaming-system.html">🎮 Gaming</a></li>
        <li><a href="#wallet" class="active">💰 Wallet</a></li>
      </ul>
    </nav>
  </header>

  <!-- Wallet Hero Section -->
  <section class="wallet-hero">
    <div class="container">
      <h1>💰 <span class="wallet-text">PROJEK Wallet</span></h1>
      <p>Manage your ProjekCoin, INR, and earnings in one secure wallet</p>
    </div>
  </section>

  <!-- Wallet Dashboard -->
  <section class="wallet-dashboard">
    <div class="container">
      <!-- Balance Cards -->
      <div class="balance-cards">
        <!-- ProjekCoin Balance -->
        <div class="balance-card pc-card">
          <div class="card-header">
            <h3>💰 ProjekCoin (PC)</h3>
            <button class="visibility-toggle" onclick="toggleBalance('pc')">
              <span class="material-icons" id="pcVisibility">visibility</span>
            </button>
          </div>
          <div class="balance-amount" id="pcBalance">1,250 PC</div>
          <div class="balance-inr">≈ ₹1,250</div>
          <div class="card-actions">
            <button class="action-btn primary" onclick="openDepositModal()">
              <span class="material-icons">add</span>
              Deposit INR
            </button>
            <button class="action-btn secondary" onclick="openWithdrawModal()">
              <span class="material-icons">account_balance</span>
              Withdraw to Bank
            </button>
          </div>
        </div>

        <!-- INR Balance -->
        <div class="balance-card inr-card">
          <div class="card-header">
            <h3>💵 INR Balance</h3>
            <button class="visibility-toggle" onclick="toggleBalance('inr')">
              <span class="material-icons" id="inrVisibility">visibility</span>
            </button>
          </div>
          <div class="balance-amount" id="inrBalance">₹850</div>
          <div class="balance-subtitle">Available for conversion</div>
          <div class="card-actions">
            <button class="action-btn primary" onclick="convertToProjekCoin()">
              <span class="material-icons">swap_horiz</span>
              Convert to PC
            </button>
            <button class="action-btn secondary" onclick="addMoney()">
              <span class="material-icons">payment</span>
              Add Money
            </button>
          </div>
        </div>

        <!-- Gaming Balance -->
        <div class="balance-card gaming-card">
          <div class="card-header">
            <h3>🎮 Gaming Balance</h3>
            <button class="visibility-toggle" onclick="toggleBalance('gaming')">
              <span class="material-icons" id="gamingVisibility">visibility</span>
            </button>
          </div>
          <div class="balance-amount" id="gamingBalance">450 PC</div>
          <div class="balance-inr">≈ ₹450</div>
          <div class="card-actions">
            <button class="action-btn primary" onclick="transferToGaming()">
              <span class="material-icons">sports_esports</span>
              Transfer to Gaming
            </button>
            <button class="action-btn secondary" onclick="window.location.href='gaming-system.html'">
              <span class="material-icons">casino</span>
              Play Games
            </button>
          </div>
        </div>

        <!-- Staking Balance -->
        <div class="balance-card staking-card">
          <div class="card-header">
            <h3>📈 Staking Balance</h3>
            <button class="visibility-toggle" onclick="toggleBalance('staking')">
              <span class="material-icons" id="stakingVisibility">visibility</span>
            </button>
          </div>
          <div class="balance-amount" id="stakingBalance">2,100 PC</div>
          <div class="balance-inr">≈ ₹2,100 | 🔥 12% APY</div>
          <div class="card-actions">
            <button class="action-btn primary" onclick="openStakeModal()">
              <span class="material-icons">trending_up</span>
              Stake More
            </button>
            <button class="action-btn secondary" onclick="openUnstakeModal()">
              <span class="material-icons">trending_down</span>
              Unstake
            </button>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <h3>⚡ Quick Actions</h3>
        <div class="actions-grid">
          <button class="quick-action-btn" onclick="openDepositModal()">
            <span class="material-icons">add_circle</span>
            <span>Deposit INR</span>
          </button>
          <button class="quick-action-btn" onclick="convertToProjekCoin()">
            <span class="material-icons">currency_exchange</span>
            <span>Convert to PC</span>
          </button>
          <button class="quick-action-btn" onclick="openWithdrawModal()">
            <span class="material-icons">account_balance</span>
            <span>Withdraw</span>
          </button>
          <button class="quick-action-btn" onclick="openStakeModal()">
            <span class="material-icons">savings</span>
            <span>Stake & Earn</span>
          </button>
          <button class="quick-action-btn" onclick="window.location.href='gaming-system.html'">
            <span class="material-icons">casino</span>
            <span>Play Games</span>
          </button>
          <button class="quick-action-btn" onclick="sendMoney()">
            <span class="material-icons">send</span>
            <span>Send Money</span>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Conversion Rate -->
  <section class="conversion-section">
    <div class="container">
      <div class="conversion-card">
        <h3>💱 Current Exchange Rate</h3>
        <div class="rate-display">
          <span class="rate-amount">1 INR = 1 PC</span>
          <span class="rate-status">🟢 Live Rate</span>
        </div>
        <p>Convert your INR to ProjekCoin instantly at 1:1 ratio</p>
      </div>
    </div>
  </section>

  <!-- Staking Section -->
  <section class="staking-section">
    <div class="container">
      <h2 class="section-title">📈 Stake & Earn</h2>
      <p class="section-subtitle">Earn passive income by staking your ProjekCoin</p>
      
      <div class="staking-plans">
        <div class="staking-plan">
          <h3>🥉 Bronze Plan</h3>
          <div class="apy-rate">8% APY</div>
          <div class="plan-details">
            <p>Minimum: 100 PC</p>
            <p>Lock Period: 30 days</p>
            <p>Daily Rewards: 0.022% daily</p>
          </div>
          <button class="stake-btn" onclick="openStakeModal('bronze')">Stake Now</button>
        </div>
        
        <div class="staking-plan featured">
          <h3>🥈 Silver Plan</h3>
          <div class="apy-rate">12% APY</div>
          <div class="plan-details">
            <p>Minimum: 500 PC</p>
            <p>Lock Period: 90 days</p>
            <p>Daily Rewards: 0.033% daily</p>
          </div>
          <button class="stake-btn" onclick="openStakeModal('silver')">Stake Now</button>
          <div class="featured-badge">Most Popular</div>
        </div>
        
        <div class="staking-plan">
          <h3>🥇 Gold Plan</h3>
          <div class="apy-rate">18% APY</div>
          <div class="plan-details">
            <p>Minimum: 2000 PC</p>
            <p>Lock Period: 180 days</p>
            <p>Daily Rewards: 0.049% daily</p>
          </div>
          <button class="stake-btn" onclick="openStakeModal('gold')">Stake Now</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Transaction History -->
  <section class="transaction-history">
    <div class="container">
      <h2 class="section-title">📊 Transaction History</h2>
      
      <div class="history-filters">
        <button class="filter-btn active" onclick="filterTransactions('all')">All</button>
        <button class="filter-btn" onclick="filterTransactions('deposit')">Deposits</button>
        <button class="filter-btn" onclick="filterTransactions('withdraw')">Withdrawals</button>
        <button class="filter-btn" onclick="filterTransactions('gaming')">Gaming</button>
        <button class="filter-btn" onclick="filterTransactions('staking')">Staking</button>
      </div>
      
      <div class="transactions-list" id="transactionsList">
        <div class="transaction-item gaming win">
          <div class="transaction-icon">🎮</div>
          <div class="transaction-details">
            <h4>Gaming Win</h4>
            <p>Spin-to-Earn Wheel Game</p>
            <span class="transaction-time">2 minutes ago</span>
          </div>
          <div class="transaction-amount win">+250 PC</div>
        </div>
        
        <div class="transaction-item deposit">
          <div class="transaction-icon">💰</div>
          <div class="transaction-details">
            <h4>INR Deposit</h4>
            <p>UPI Payment</p>
            <span class="transaction-time">1 hour ago</span>
          </div>
          <div class="transaction-amount">+₹500</div>
        </div>
        
        <div class="transaction-item staking">
          <div class="transaction-icon">📈</div>
          <div class="transaction-details">
            <h4>Staking Reward</h4>
            <p>Silver Plan Daily Reward</p>
            <span class="transaction-time">1 day ago</span>
          </div>
          <div class="transaction-amount win">+12 PC</div>
        </div>
        
        <div class="transaction-item gaming loss">
          <div class="transaction-icon">🎮</div>
          <div class="transaction-details">
            <h4>Gaming Entry</h4>
            <p>Max Spin Entry Fee</p>
            <span class="transaction-time">2 days ago</span>
          </div>
          <div class="transaction-amount loss">-50 PC</div>
        </div>
        
        <div class="transaction-item withdraw">
          <div class="transaction-icon">🏦</div>
          <div class="transaction-details">
            <h4>Bank Withdrawal</h4>
            <p>To HDFC Bank ****1234</p>
            <span class="transaction-time">3 days ago</span>
          </div>
          <div class="transaction-amount">-₹1,000</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modals will be added via JavaScript -->
  <div id="modalContainer"></div>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="assets/image/logo/app_logo.png" alt="PROJEK Logo" class="footer-logo-img">
          <span class="logo-icon">👁️</span>
          <span class="logo-text">PROJEK Super Vision</span>
          <p>Your Digital Wallet, Your Financial Freedom</p>
        </div>
        
        <div class="footer-links">
          <h4>Wallet</h4>
          <a href="#deposit">Deposit Money</a>
          <a href="#withdraw">Withdraw Funds</a>
          <a href="#staking">Stake & Earn</a>
        </div>
        
        <div class="footer-links">
          <h4>Support</h4>
          <a href="#help">Help Center</a>
          <a href="#security">Security</a>
          <a href="#terms">Terms & Conditions</a>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2024 👁️ PROJEK Super Vision Wallet. Secure & Trusted.</p>
      </div>
    </div>
  </footer>

  <script src="js/wallet.js"></script>
</body>
</html>
