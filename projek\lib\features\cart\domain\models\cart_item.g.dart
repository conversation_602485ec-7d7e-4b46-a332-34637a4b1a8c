// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_item.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CartItemAdapter extends TypeAdapter<CartItem> {
  @override
  final int typeId = 1;

  @override
  CartItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CartItem(
      id: fields[0] as String,
      productId: fields[1] as String,
      name: fields[2] as String,
      price: fields[3] as double,
      originalPrice: fields[4] as double,
      currency: fields[5] as String,
      imageUrl: fields[6] as String,
      category: fields[7] as String,
      brand: fields[8] as String,
      vendorId: fields[9] as String,
      vendorName: fields[10] as String,
      quantity: fields[11] as int,
      addedAt: fields[12] as DateTime,
      selectedVariants: (fields[13] as Map).cast<String, dynamic>(),
      inStock: fields[14] as bool,
      maxQuantity: fields[15] as int,
    );
  }

  @override
  void write(BinaryWriter writer, CartItem obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.originalPrice)
      ..writeByte(5)
      ..write(obj.currency)
      ..writeByte(6)
      ..write(obj.imageUrl)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.brand)
      ..writeByte(9)
      ..write(obj.vendorId)
      ..writeByte(10)
      ..write(obj.vendorName)
      ..writeByte(11)
      ..write(obj.quantity)
      ..writeByte(12)
      ..write(obj.addedAt)
      ..writeByte(13)
      ..write(obj.selectedVariants)
      ..writeByte(14)
      ..write(obj.inStock)
      ..writeByte(15)
      ..write(obj.maxQuantity);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CartItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CartAdapter extends TypeAdapter<Cart> {
  @override
  final int typeId = 2;

  @override
  Cart read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Cart(
      items: (fields[0] as List).cast<CartItem>(),
      updatedAt: fields[1] as DateTime,
      promoCode: fields[2] as String?,
      promoDiscount: fields[3] as double,
    );
  }

  @override
  void write(BinaryWriter writer, Cart obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.items)
      ..writeByte(1)
      ..write(obj.updatedAt)
      ..writeByte(2)
      ..write(obj.promoCode)
      ..writeByte(3)
      ..write(obj.promoDiscount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CartAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartItem _$CartItemFromJson(Map<String, dynamic> json) => CartItem(
      id: json['id'] as String,
      productId: json['productId'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      imageUrl: json['imageUrl'] as String,
      category: json['category'] as String,
      brand: json['brand'] as String,
      vendorId: json['vendorId'] as String,
      vendorName: json['vendorName'] as String,
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      addedAt: DateTime.parse(json['addedAt'] as String),
      selectedVariants:
          json['selectedVariants'] as Map<String, dynamic>? ?? const {},
      inStock: json['inStock'] as bool? ?? true,
      maxQuantity: (json['maxQuantity'] as num?)?.toInt() ?? 10,
    );

Map<String, dynamic> _$CartItemToJson(CartItem instance) => <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'name': instance.name,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'currency': instance.currency,
      'imageUrl': instance.imageUrl,
      'category': instance.category,
      'brand': instance.brand,
      'vendorId': instance.vendorId,
      'vendorName': instance.vendorName,
      'quantity': instance.quantity,
      'addedAt': instance.addedAt.toIso8601String(),
      'selectedVariants': instance.selectedVariants,
      'inStock': instance.inStock,
      'maxQuantity': instance.maxQuantity,
    };

Cart _$CartFromJson(Map<String, dynamic> json) => Cart(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => CartItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      promoCode: json['promoCode'] as String?,
      promoDiscount: (json['promoDiscount'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$CartToJson(Cart instance) => <String, dynamic>{
      'items': instance.items,
      'updatedAt': instance.updatedAt.toIso8601String(),
      'promoCode': instance.promoCode,
      'promoDiscount': instance.promoDiscount,
    };
