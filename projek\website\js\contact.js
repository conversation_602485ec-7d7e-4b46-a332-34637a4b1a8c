// Contact Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
  
  // Initialize form handling
  initializeContactForm();
  
  // Initialize FAQ functionality
  initializeFAQ();
  
  // Initialize live chat
  initializeLiveChat();
  
  // Initialize method cards interactions
  initializeMethodCards();
  
  function initializeContactForm() {
    const form = document.getElementById('contactForm');
    
    if (form) {
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
          submitForm();
        }
      });
      
      // Real-time validation
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.addEventListener('blur', function() {
          validateField(this);
        });
        
        input.addEventListener('input', function() {
          clearFieldError(this);
        });
      });
    }
  }
  
  function validateForm() {
    const form = document.getElementById('contactForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!validateField(field)) {
        isValid = false;
      }
    });
    
    return isValid;
  }
  
  function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Check if required field is empty
    if (field.hasAttribute('required') && !value) {
      errorMessage = 'This field is required';
      isValid = false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errorMessage = 'Please enter a valid email address';
        isValid = false;
      }
    }
    
    // Phone validation
    if (field.type === 'tel' && value) {
      const phoneRegex = /^[6-9]\d{9}$/;
      if (!phoneRegex.test(value.replace(/\D/g, ''))) {
        errorMessage = 'Please enter a valid 10-digit phone number';
        isValid = false;
      }
    }
    
    // Message length validation
    if (field.name === 'message' && value && value.length < 10) {
      errorMessage = 'Message should be at least 10 characters long';
      isValid = false;
    }
    
    if (!isValid) {
      showFieldError(field, errorMessage);
    } else {
      clearFieldError(field);
    }
    
    return isValid;
  }
  
  function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = '#dc3545';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.cssText = `
      color: #dc3545;
      font-size: 0.8rem;
      margin-top: 0.25rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    `;
    errorDiv.innerHTML = `<span class="material-icons" style="font-size: 1rem;">error</span>${message}`;
    
    field.parentNode.appendChild(errorDiv);
  }
  
  function clearFieldError(field) {
    field.style.borderColor = '';
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
  }
  
  function submitForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<span class="material-icons">hourglass_empty</span><span>Sending...</span>';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
      showSuccessMessage();
      form.reset();
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }, 2000);
  }
  
  function showSuccessMessage() {
    // Create success modal
    const modal = document.createElement('div');
    modal.className = 'success-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
    `;
    
    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 3rem; text-align: center; max-width: 400px; margin: 2rem;">
        <div style="width: 80px; height: 80px; margin: 0 auto 2rem; background: var(--gradient-green); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <span class="material-icons" style="font-size: 3rem; color: white;">check_circle</span>
        </div>
        <h3 style="color: var(--green); margin-bottom: 1rem;">Message Sent Successfully!</h3>
        <p style="color: var(--medium-gray); margin-bottom: 2rem;">Thank you for contacting us. We'll get back to you within 2 hours.</p>
        <button class="btn btn-primary" onclick="this.closest('.success-modal').remove()">
          <span>Close</span>
        </button>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-close after 5 seconds
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 5000);
  }
  
  function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      
      question.addEventListener('click', function() {
        const isActive = item.classList.contains('active');
        
        // Close all other FAQ items
        faqItems.forEach(otherItem => {
          otherItem.classList.remove('active');
        });
        
        // Toggle current item
        if (!isActive) {
          item.classList.add('active');
        }
      });
    });
  }
  
  function initializeLiveChat() {
    const chatButton = document.getElementById('startChat');
    
    if (chatButton) {
      chatButton.addEventListener('click', function() {
        showChatModal();
      });
    }
  }
  
  function showChatModal() {
    const modal = document.createElement('div');
    modal.className = 'chat-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
    `;
    
    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; width: 90%; max-width: 500px; max-height: 80vh; overflow: hidden;">
        <div style="background: var(--gradient-blue); color: white; padding: 1.5rem; display: flex; justify-content: space-between; align-items: center;">
          <h3>Live Chat Support</h3>
          <button onclick="this.closest('.chat-modal').remove()" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">×</button>
        </div>
        <div style="padding: 2rem; text-align: center;">
          <div style="width: 80px; height: 80px; margin: 0 auto 2rem; background: var(--gradient-saffron); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <span class="material-icons" style="font-size: 3rem; color: white;">support_agent</span>
          </div>
          <h4 style="margin-bottom: 1rem;">Chat Support Available</h4>
          <p style="color: var(--medium-gray); margin-bottom: 2rem;">Our support agents are available from 6 AM to 12 AM to help you with any questions.</p>
          <div style="display: flex; gap: 1rem; justify-content: center;">
            <button class="btn btn-primary" onclick="alert('Connecting to chat...'); this.closest('.chat-modal').remove();">
              <span class="material-icons">chat</span>
              <span>Start Chat</span>
            </button>
            <button class="btn btn-secondary" onclick="this.closest('.chat-modal').remove();">
              <span>Maybe Later</span>
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
  }
  
  function initializeMethodCards() {
    const methodCards = document.querySelectorAll('.method-card');
    
    methodCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
        
        const icon = this.querySelector('.method-icon');
        if (icon) {
          icon.style.transform = 'scale(1.1) rotate(5deg)';
        }
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = '';
        
        const icon = this.querySelector('.method-icon');
        if (icon) {
          icon.style.transform = '';
        }
      });
    });
  }
  
  // Auto-format phone number input
  const phoneInput = document.getElementById('phone');
  if (phoneInput) {
    phoneInput.addEventListener('input', function() {
      let value = this.value.replace(/\D/g, '');
      if (value.length > 10) value = value.slice(0, 10);
      this.value = value;
    });
  }
  
  // Subject-based form customization
  const subjectSelect = document.getElementById('subject');
  if (subjectSelect) {
    subjectSelect.addEventListener('change', function() {
      const messageField = document.getElementById('message');
      const placeholders = {
        'general': 'Please describe your general inquiry...',
        'support': 'Please describe the technical issue you\'re experiencing...',
        'billing': 'Please provide details about your billing inquiry...',
        'partnership': 'Please tell us about your partnership proposal...',
        'feedback': 'We\'d love to hear your feedback...',
        'complaint': 'Please describe your complaint in detail...',
        'other': 'Please describe your inquiry...'
      };
      
      if (messageField && placeholders[this.value]) {
        messageField.placeholder = placeholders[this.value];
      }
    });
  }
  
  // Initialize animations
  const animatedElements = document.querySelectorAll('.method-card, .faq-item');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '0';
        entry.target.style.transform = 'translateY(30px)';
        entry.target.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }, 100);
        
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });

  animatedElements.forEach(element => {
    observer.observe(element);
  });
  
  console.log('Contact page initialized successfully!');
});
