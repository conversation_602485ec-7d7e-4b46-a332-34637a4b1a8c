import 'dart:developer' as developer;
import '../config/environment.dart';

/// Logger utility for Projek App
/// Provides different log levels and conditional logging based on environment
class Logger {
  static const String _tag = 'ProjekApp';

  /// Log debug messages (only in debug mode)
  static void debug(String message, [String? tag]) {
    if (EnvironmentConfig.enableDetailedLogging) {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 500, // Debug level
      );
    }
  }

  /// Log info messages
  static void info(String message, [String? tag]) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 800, // Info level
    );
  }

  /// Log warning messages
  static void warning(String message, [String? tag]) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 900, // Warning level
    );
  }

  /// Log error messages
  static void error(String message, [Object? error, StackTrace? stackTrace, String? tag]) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log analytics events
  static void analytics(String eventName, Map<String, dynamic> data) {
    if (EnvironmentConfig.enableAnalytics) {
      debug('Analytics: $eventName - $data', 'Analytics');
    }
  }

  /// Log location events
  static void location(String message) {
    debug(message, 'Location');
  }

  /// Log notification events
  static void notification(String message) {
    debug(message, 'Notification');
  }

  /// Log Firebase events
  static void firebase(String message) {
    debug(message, 'Firebase');
  }
}
