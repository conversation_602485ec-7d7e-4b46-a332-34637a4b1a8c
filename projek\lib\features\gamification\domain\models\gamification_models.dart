import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gamification_models.g.dart';

@HiveType(typeId: 90)
enum AchievementType {
  @HiveField(0)
  firstOrder,
  @HiveField(1)
  orderMilestone,
  @HiveField(2)
  spendingMilestone,
  @HiveField(3)
  referralMilestone,
  @HiveField(4)
  reviewMilestone,
  @HiveField(5)
  loginStreak,
  @HiveField(6)
  gameWins,
  @HiveField(7)
  socialSharing,
  @HiveField(8)
  profileCompletion,
  @HiveField(9)
  loyaltyTier,
  @HiveField(10)
  specialEvent,
}

@HiveType(typeId: 91)
enum LoyaltyTier {
  @HiveField(0)
  bronze,
  @HiveField(1)
  silver,
  @HiveField(2)
  gold,
  @HiveField(3)
  platinum,
  @HiveField(4)
  diamond,
}

@HiveType(typeId: 92)
enum GameType {
  @<PERSON>veField(0)
  spinWheel,
  @<PERSON>veField(1)
  daily<PERSON>heckin,
  @HiveField(2)
  scratchCard,
  @HiveField(3)
  quiz,
  @HiveField(4)
  treasure,
  @HiveField(5)
  roulette,
  @HiveField(6)
  slotMachine,
}

@HiveType(typeId: 93)
@JsonSerializable()
class Achievement extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final AchievementType type;
  
  @HiveField(2)
  final String title;
  
  @HiveField(3)
  final String description;
  
  @HiveField(4)
  final String icon;
  
  @HiveField(5)
  final double rewardAmount;
  
  @HiveField(6)
  final int targetValue;
  
  @HiveField(7)
  final int currentProgress;
  
  @HiveField(8)
  final bool isUnlocked;
  
  @HiveField(9)
  final bool isClaimed;
  
  @HiveField(10)
  final DateTime? unlockedAt;
  
  @HiveField(11)
  final DateTime? claimedAt;
  
  @HiveField(12)
  final Map<String, dynamic> metadata;

  const Achievement({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.rewardAmount,
    required this.targetValue,
    this.currentProgress = 0,
    this.isUnlocked = false,
    this.isClaimed = false,
    this.unlockedAt,
    this.claimedAt,
    this.metadata = const {},
  });

  factory Achievement.fromJson(Map<String, dynamic> json) =>
      _$AchievementFromJson(json);

  Map<String, dynamic> toJson() => _$AchievementToJson(this);

  double get progressPercentage => 
      targetValue > 0 ? (currentProgress / targetValue).clamp(0.0, 1.0) : 0.0;

  bool get isCompleted => currentProgress >= targetValue;

  String get formattedReward => '${rewardAmount.toStringAsFixed(0)} PC';

  Achievement copyWith({
    String? id,
    AchievementType? type,
    String? title,
    String? description,
    String? icon,
    double? rewardAmount,
    int? targetValue,
    int? currentProgress,
    bool? isUnlocked,
    bool? isClaimed,
    DateTime? unlockedAt,
    DateTime? claimedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Achievement(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      rewardAmount: rewardAmount ?? this.rewardAmount,
      targetValue: targetValue ?? this.targetValue,
      currentProgress: currentProgress ?? this.currentProgress,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      isClaimed: isClaimed ?? this.isClaimed,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      claimedAt: claimedAt ?? this.claimedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        description,
        icon,
        rewardAmount,
        targetValue,
        currentProgress,
        isUnlocked,
        isClaimed,
        unlockedAt,
        claimedAt,
        metadata,
      ];
}

@JsonSerializable()
class UserProfile {
  final String userId;
  final String username;
  final String? avatarUrl;
  final LoyaltyTier loyaltyTier;
  final int totalPoints;
  final int level;
  final int experience;
  final int experienceToNextLevel;
  final int totalOrders;
  final double totalSpent;
  final int referralCount;
  final int loginStreak;
  final int maxLoginStreak;
  final DateTime lastLoginDate;
  final List<String> unlockedAchievements;
  final Map<String, dynamic> stats;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.userId,
    required this.username,
    this.avatarUrl,
    this.loyaltyTier = LoyaltyTier.bronze,
    this.totalPoints = 0,
    this.level = 1,
    this.experience = 0,
    this.experienceToNextLevel = 100,
    this.totalOrders = 0,
    this.totalSpent = 0.0,
    this.referralCount = 0,
    this.loginStreak = 0,
    this.maxLoginStreak = 0,
    required this.lastLoginDate,
    this.unlockedAchievements = const [],
    this.stats = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => 
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

@HiveType(typeId: 95)
@JsonSerializable()
class LeaderboardEntry extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final String username;
  
  @HiveField(2)
  final String? avatarUrl;
  
  @HiveField(3)
  final int rank;
  
  @HiveField(4)
  final int score;
  
  @HiveField(5)
  final LoyaltyTier tier;
  
  @HiveField(6)
  final Map<String, dynamic> stats;

  const LeaderboardEntry({
    required this.userId,
    required this.username,
    this.avatarUrl,
    required this.rank,
    required this.score,
    required this.tier,
    this.stats = const {},
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardEntryFromJson(json);

  Map<String, dynamic> toJson() => _$LeaderboardEntryToJson(this);

  @override
  List<Object?> get props => [userId, username, avatarUrl, rank, score, tier, stats];
}

@HiveType(typeId: 96)
@JsonSerializable()
class ReferralProgram extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final String referralCode;
  
  @HiveField(2)
  final int totalReferrals;
  
  @HiveField(3)
  final int successfulReferrals;
  
  @HiveField(4)
  final double totalEarnings;
  
  @HiveField(5)
  final List<ReferralEntry> referrals;
  
  @HiveField(6)
  final DateTime createdAt;
  
  @HiveField(7)
  final DateTime updatedAt;

  const ReferralProgram({
    required this.userId,
    required this.referralCode,
    this.totalReferrals = 0,
    this.successfulReferrals = 0,
    this.totalEarnings = 0.0,
    this.referrals = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReferralProgram.fromJson(Map<String, dynamic> json) =>
      _$ReferralProgramFromJson(json);

  Map<String, dynamic> toJson() => _$ReferralProgramToJson(this);

  double get conversionRate => 
      totalReferrals > 0 ? (successfulReferrals / totalReferrals) : 0.0;

  String get formattedEarnings => '₹${totalEarnings.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
        userId,
        referralCode,
        totalReferrals,
        successfulReferrals,
        totalEarnings,
        referrals,
        createdAt,
        updatedAt,
      ];
}

@HiveType(typeId: 97)
@JsonSerializable()
class ReferralEntry extends Equatable {
  @HiveField(0)
  final String referredUserId;
  
  @HiveField(1)
  final String referredUsername;
  
  @HiveField(2)
  final DateTime referredAt;
  
  @HiveField(3)
  final bool isSuccessful;
  
  @HiveField(4)
  final double rewardEarned;
  
  @HiveField(5)
  final DateTime? rewardClaimedAt;

  const ReferralEntry({
    required this.referredUserId,
    required this.referredUsername,
    required this.referredAt,
    this.isSuccessful = false,
    this.rewardEarned = 0.0,
    this.rewardClaimedAt,
  });

  factory ReferralEntry.fromJson(Map<String, dynamic> json) =>
      _$ReferralEntryFromJson(json);

  Map<String, dynamic> toJson() => _$ReferralEntryToJson(this);

  @override
  List<Object?> get props => [
        referredUserId,
        referredUsername,
        referredAt,
        isSuccessful,
        rewardEarned,
        rewardClaimedAt,
      ];
}

@HiveType(typeId: 98)
@JsonSerializable()
class GameSession extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final GameType gameType;
  
  @HiveField(3)
  final double entryFee;
  
  @HiveField(4)
  final double winAmount;
  
  @HiveField(5)
  final bool isWin;
  
  @HiveField(6)
  final DateTime playedAt;
  
  @HiveField(7)
  final Map<String, dynamic> gameData;

  const GameSession({
    required this.id,
    required this.userId,
    required this.gameType,
    required this.entryFee,
    required this.winAmount,
    required this.isWin,
    required this.playedAt,
    this.gameData = const {},
  });

  factory GameSession.fromJson(Map<String, dynamic> json) =>
      _$GameSessionFromJson(json);

  Map<String, dynamic> toJson() => _$GameSessionToJson(this);

  double get netAmount => winAmount - entryFee;
  String get formattedNetAmount => '${netAmount >= 0 ? '+' : ''}₹${netAmount.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
        id,
        userId,
        gameType,
        entryFee,
        winAmount,
        isWin,
        playedAt,
        gameData,
      ];
}




