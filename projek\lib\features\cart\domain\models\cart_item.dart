import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

import '../../../marketplace/domain/models/product.dart';

part 'cart_item.g.dart';

@HiveType(typeId: 1)
@JsonSerializable()
class CartItem extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String productId;

  @HiveField(2)
  final String name;

  @HiveField(3)
  final double price;

  @HiveField(4)
  final double originalPrice;

  @HiveField(5)
  final String currency;

  @HiveField(6)
  final String imageUrl;

  @HiveField(7)
  final String category;

  @HiveField(8)
  final String brand;

  @HiveField(9)
  final String vendorId;

  @HiveField(10)
  final String vendorName;

  @HiveField(11)
  final int quantity;

  @HiveField(12)
  final DateTime addedAt;

  @HiveField(13)
  final Map<String, dynamic> selectedVariants;

  @HiveField(14)
  final bool inStock;

  @HiveField(15)
  final int maxQuantity;

  const CartItem({
    required this.id,
    required this.productId,
    required this.name,
    required this.price,
    required this.originalPrice,
    this.currency = 'INR',
    required this.imageUrl,
    required this.category,
    required this.brand,
    required this.vendorId,
    required this.vendorName,
    this.quantity = 1,
    required this.addedAt,
    this.selectedVariants = const {},
    this.inStock = true,
    this.maxQuantity = 10,
  });

  factory CartItem.fromProduct(Product product, {int quantity = 1}) {
    return CartItem(
      id: '${product.id}_${DateTime.now().millisecondsSinceEpoch}',
      productId: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      currency: product.currency,
      imageUrl: product.primaryImage,
      category: product.category,
      brand: product.brand,
      vendorId: product.vendorId,
      vendorName: product.vendorName,
      quantity: quantity,
      addedAt: DateTime.now(),
      inStock: product.inStock,
      maxQuantity: product.stockQuantity > 0 ? product.stockQuantity : 10,
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json) =>
      _$CartItemFromJson(json);
  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  // Helper getters
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedOriginalPrice => '₹${originalPrice.toStringAsFixed(2)}';
  String get formattedTotalPrice => '₹${totalPrice.toStringAsFixed(2)}';
  String get formattedOriginalTotalPrice =>
      '₹${originalTotalPrice.toStringAsFixed(2)}';

  double get totalPrice => price * quantity;
  double get originalTotalPrice => originalPrice * quantity;
  double get totalSavings => originalTotalPrice - totalPrice;

  bool get hasDiscount => originalPrice > price;

  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice - price) / originalPrice) * 100;
  }

  bool get canIncreaseQuantity => quantity < maxQuantity && inStock;
  bool get canDecreaseQuantity => quantity > 1;

  CartItem copyWith({
    String? id,
    String? productId,
    String? name,
    double? price,
    double? originalPrice,
    String? currency,
    String? imageUrl,
    String? category,
    String? brand,
    String? vendorId,
    String? vendorName,
    int? quantity,
    DateTime? addedAt,
    Map<String, dynamic>? selectedVariants,
    bool? inStock,
    int? maxQuantity,
  }) {
    return CartItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      currency: currency ?? this.currency,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      quantity: quantity ?? this.quantity,
      addedAt: addedAt ?? this.addedAt,
      selectedVariants: selectedVariants ?? this.selectedVariants,
      inStock: inStock ?? this.inStock,
      maxQuantity: maxQuantity ?? this.maxQuantity,
    );
  }

  @override
  List<Object?> get props => [
    id,
    productId,
    name,
    price,
    originalPrice,
    currency,
    imageUrl,
    category,
    brand,
    vendorId,
    vendorName,
    quantity,
    addedAt,
    selectedVariants,
    inStock,
    maxQuantity,
  ];
}

@HiveType(typeId: 2)
@JsonSerializable()
class Cart extends Equatable {
  @HiveField(0)
  final List<CartItem> items;

  @HiveField(1)
  final DateTime updatedAt;

  @HiveField(2)
  final String? promoCode;

  @HiveField(3)
  final double promoDiscount;

  const Cart({
    this.items = const [],
    required this.updatedAt,
    this.promoCode,
    this.promoDiscount = 0.0,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);
  Map<String, dynamic> toJson() => _$CartToJson(this);

  // Helper getters
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  int get uniqueItemCount => items.length;

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get originalSubtotal =>
      items.fold(0.0, (sum, item) => sum + item.originalTotalPrice);
  double get totalSavings => originalSubtotal - subtotal + promoDiscount;

  double get taxAmount => subtotal * 0.18; // 18% GST
  double get deliveryFee =>
      subtotal > 500 ? 0.0 : 40.0; // Free delivery above ₹500

  double get total => subtotal + taxAmount + deliveryFee - promoDiscount;

  String get formattedSubtotal => '₹${subtotal.toStringAsFixed(2)}';
  String get formattedTaxAmount => '₹${taxAmount.toStringAsFixed(2)}';
  String get formattedDeliveryFee =>
      deliveryFee > 0 ? '₹${deliveryFee.toStringAsFixed(2)}' : 'FREE';
  String get formattedTotal => '₹${total.toStringAsFixed(2)}';
  String get formattedTotalSavings => '₹${totalSavings.toStringAsFixed(2)}';
  String get formattedPromoDiscount => '₹${promoDiscount.toStringAsFixed(2)}';

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  bool get hasPromoCode => promoCode != null && promoCode!.isNotEmpty;

  Cart copyWith({
    List<CartItem>? items,
    DateTime? updatedAt,
    String? promoCode,
    double? promoDiscount,
  }) {
    return Cart(
      items: items ?? this.items,
      updatedAt: updatedAt ?? this.updatedAt,
      promoCode: promoCode ?? this.promoCode,
      promoDiscount: promoDiscount ?? this.promoDiscount,
    );
  }

  @override
  List<Object?> get props => [items, updatedAt, promoCode, promoDiscount];
}
