import 'package:get_it/get_it.dart';
// import 'package:injectable/injectable.dart'; // Temporarily disabled for build
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

// import 'injection.config.dart'; // Temporarily disabled for build

final getIt = GetIt.instance;

// @InjectableInit() // Temporarily disabled for build
Future<void> configureDependencies() async {
  // Manual registration for now
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);

  final dio = Dio();
  dio.options.baseUrl = 'https://api.projek.com/v1/';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.sendTimeout = const Duration(seconds: 30);

  dio.interceptors.add(
    LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
    ),
  );

  getIt.registerSingleton<Dio>(dio);

  await Hive.initFlutter();
}

// @module // Temporarily disabled for build
// abstract class RegisterModule {
//   @preResolve
//   Future<SharedPreferences> get prefs => SharedPreferences.getInstance();
//
//   @lazySingleton
//   Dio get dio {
//     final dio = Dio();
//     dio.options.baseUrl = 'https://api.projek.com/v1/';
//     dio.options.connectTimeout = const Duration(seconds: 30);
//     dio.options.receiveTimeout = const Duration(seconds: 30);
//     dio.options.sendTimeout = const Duration(seconds: 30);
//
//     // Add interceptors for logging, auth, etc.
//     dio.interceptors.add(
//       LogInterceptor(
//         requestBody: true,
//         responseBody: true,
//         requestHeader: true,
//         responseHeader: true,
//       ),
//     );
//
//     return dio;
//   }
//
//   @preResolve
//   Future<void> initHive() async {
//     await Hive.initFlutter();
//     // Register adapters here when needed
//   }
// }
