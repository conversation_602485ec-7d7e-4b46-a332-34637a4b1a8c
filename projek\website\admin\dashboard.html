<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Dashboard - Super Vision Platform</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/animations.css">
  <link rel="stylesheet" href="css/admin-dashboard.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="icon" href="../assets/favicon.ico" type="image/x-icon">
</head>
<body class="admin-body">
  <!-- Sidebar -->
  <aside class="admin-sidebar">
    <div class="sidebar-header">
      <img src="../assets/images/logos/nav-logo.svg" alt="Super Vision Admin">
      <h2>Admin Panel</h2>
    </div>
    
    <nav class="sidebar-nav">
      <ul>
        <li class="nav-item active">
          <a href="#dashboard" data-section="dashboard">
            <span class="material-icons">dashboard</span>
            <span>Dashboard</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#orders" data-section="orders">
            <span class="material-icons">shopping_cart</span>
            <span>Orders</span>
            <span class="badge">1,234</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#users" data-section="users">
            <span class="material-icons">people</span>
            <span>Users</span>
            <span class="badge">45.2K</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#riders" data-section="riders">
            <span class="material-icons">directions_bike</span>
            <span>Riders</span>
            <span class="badge">2,156</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#sellers" data-section="sellers">
            <span class="material-icons">store</span>
            <span>Sellers</span>
            <span class="badge">1,245</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#service-providers" data-section="service-providers">
            <span class="material-icons">engineering</span>
            <span>Service Providers</span>
            <span class="badge">3,456</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#verification" data-section="verification">
            <span class="material-icons">verified</span>
            <span>KYC & Verification</span>
            <span class="badge pending">89</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#analytics" data-section="analytics">
            <span class="material-icons">analytics</span>
            <span>Analytics</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#gaming" data-section="gaming">
            <span class="material-icons">casino</span>
            <span>Gaming Controls</span>
            <span class="badge gaming">2,847</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#wallet" data-section="wallet">
            <span class="material-icons">account_balance_wallet</span>
            <span>Wallet Oversight</span>
            <span class="badge wallet">₹456L</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#payments" data-section="payments">
            <span class="material-icons">payment</span>
            <span>Payments</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#settings" data-section="settings">
            <span class="material-icons">settings</span>
            <span>Settings</span>
          </a>
        </li>
      </ul>
    </nav>
    
    <div class="sidebar-footer">
      <div class="admin-profile">
        <div class="profile-avatar">
          <span class="material-icons">account_circle</span>
        </div>
        <div class="profile-info">
          <h4>Monuj Admin</h4>
          <p>Super Administrator</p>
        </div>
      </div>
      <button class="logout-btn" id="logoutBtn">
        <span class="material-icons">logout</span>
        <span>Logout</span>
      </button>
    </div>
  </aside>

  <!-- Main Content -->
  <main class="admin-main">
    <!-- Header -->
    <header class="admin-header">
      <div class="header-left">
        <button class="sidebar-toggle" id="sidebarToggle">
          <span class="material-icons">menu</span>
        </button>
        <h1 id="pageTitle">Dashboard Overview</h1>
      </div>
      
      <div class="header-right">
        <div class="search-box">
          <span class="material-icons">search</span>
          <input type="text" placeholder="Search anything...">
        </div>
        
        <div class="header-actions">
          <button class="action-btn" id="notificationsBtn">
            <span class="material-icons">notifications</span>
            <span class="notification-badge">5</span>
          </button>
          
          <button class="action-btn" id="messagesBtn">
            <span class="material-icons">message</span>
            <span class="notification-badge">12</span>
          </button>
          
          <div class="admin-avatar">
            <span class="material-icons">account_circle</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Dashboard Content -->
    <div class="admin-content">
      <!-- Dashboard Section -->
      <section id="dashboard-section" class="content-section active">
        <!-- Stats Cards -->
        <div class="stats-grid">
          <div class="stat-card revenue">
            <div class="stat-icon">
              <span class="material-icons">trending_up</span>
            </div>
            <div class="stat-content">
              <h3>₹12,45,678</h3>
              <p>Total Revenue</p>
              <span class="stat-change positive">+12.5%</span>
            </div>
          </div>
          
          <div class="stat-card orders">
            <div class="stat-icon">
              <span class="material-icons">shopping_cart</span>
            </div>
            <div class="stat-content">
              <h3>8,456</h3>
              <p>Total Orders</p>
              <span class="stat-change positive">+8.2%</span>
            </div>
          </div>
          
          <div class="stat-card users">
            <div class="stat-icon">
              <span class="material-icons">people</span>
            </div>
            <div class="stat-content">
              <h3>45,234</h3>
              <p>Active Users</p>
              <span class="stat-change positive">+15.3%</span>
            </div>
          </div>
          
          <div class="stat-card riders">
            <div class="stat-icon">
              <span class="material-icons">directions_bike</span>
            </div>
            <div class="stat-content">
              <h3>2,156</h3>
              <p>Active Riders</p>
              <span class="stat-change negative">-2.1%</span>
            </div>
          </div>
        </div>

        <!-- Charts Row -->
        <div class="charts-row">
          <!-- Small Revenue Analytics -->
          <div class="chart-container small">
            <div class="chart-header">
              <h3>Revenue Analytics</h3>
              <div class="chart-controls">
                <select id="revenueTimeframe">
                  <option value="7d">Last 7 Days</option>
                  <option value="30d" selected>Last 30 Days</option>
                  <option value="90d">Last 90 Days</option>
                </select>
              </div>
            </div>
            <canvas id="revenueChart"></canvas>
          </div>

          <!-- GPS Tracking Map -->
          <div class="chart-container large">
            <div class="chart-header">
              <h3>🗺️ Live Rider GPS Tracking</h3>
              <div class="chart-controls">
                <button class="btn-small" id="refreshMap">
                  <span class="material-icons">refresh</span>
                  Refresh
                </button>
                <span class="live-indicator"></span>
                <span class="live-text">Live</span>
              </div>
            </div>
            <div class="gps-map-container">
              <div id="riderMap" class="rider-map">
                <!-- Map will be rendered here -->
                <div class="map-overlay">
                  <div class="rider-marker active" style="top: 20%; left: 30%;" data-rider="R001">
                    <span class="material-icons">delivery_dining</span>
                    <div class="rider-tooltip">
                      <strong>Rider #R001</strong><br>
                      Status: Delivering<br>
                      ETA: 12 min
                    </div>
                  </div>
                  <div class="rider-marker active" style="top: 45%; left: 60%;" data-rider="R002">
                    <span class="material-icons">delivery_dining</span>
                    <div class="rider-tooltip">
                      <strong>Rider #R002</strong><br>
                      Status: Available<br>
                      Location: MG Road
                    </div>
                  </div>
                  <div class="rider-marker busy" style="top: 70%; left: 25%;" data-rider="R003">
                    <span class="material-icons">delivery_dining</span>
                    <div class="rider-tooltip">
                      <strong>Rider #R003</strong><br>
                      Status: Picking Up<br>
                      Restaurant: Pizza Palace
                    </div>
                  </div>
                  <div class="rider-marker active" style="top: 35%; left: 75%;" data-rider="R004">
                    <span class="material-icons">delivery_dining</span>
                    <div class="rider-tooltip">
                      <strong>Rider #R004</strong><br>
                      Status: Available<br>
                      Location: Brigade Road
                    </div>
                  </div>
                </div>
                <div class="map-legend">
                  <div class="legend-item">
                    <span class="legend-dot active"></span>
                    Available (856)
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot busy"></span>
                    Busy (234)
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot offline"></span>
                    Offline (45)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Booking Reports Section -->
        <div class="booking-reports-section">
          <h3>📊 Product & Service Booking Reports</h3>
          <div class="booking-stats-grid">
            <div class="booking-stat-card confirmed">
              <div class="booking-icon">
                <span class="material-icons">check_circle</span>
              </div>
              <div class="booking-content">
                <h4>5,678</h4>
                <p>Confirmed Bookings</p>
                <span class="booking-change positive">+18.5%</span>
              </div>
            </div>

            <div class="booking-stat-card pending">
              <div class="booking-icon">
                <span class="material-icons">schedule</span>
              </div>
              <div class="booking-content">
                <h4>1,234</h4>
                <p>Pending Bookings</p>
                <span class="booking-change neutral">****%</span>
              </div>
            </div>

            <div class="booking-stat-card cancelled">
              <div class="booking-icon">
                <span class="material-icons">cancel</span>
              </div>
              <div class="booking-content">
                <h4>456</h4>
                <p>Cancelled Bookings</p>
                <span class="booking-change negative">-5.2%</span>
              </div>
            </div>

            <div class="booking-stat-card total">
              <div class="booking-icon">
                <span class="material-icons">assessment</span>
              </div>
              <div class="booking-content">
                <h4>7,368</h4>
                <p>Total Bookings</p>
                <span class="booking-change positive">+12.8%</span>
              </div>
            </div>
          </div>

          <!-- Detailed Booking Table -->
          <div class="booking-table-container">
            <div class="table-header">
              <h4>Recent Bookings</h4>
              <div class="table-filters">
                <select id="bookingFilter">
                  <option value="all">All Status</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="pending">Pending</option>
                  <option value="cancelled">Cancelled</option>
                </select>
                <button class="btn-export">
                  <span class="material-icons">download</span>
                  Export
                </button>
              </div>
            </div>
            <div class="booking-table">
              <table>
                <thead>
                  <tr>
                    <th>Booking ID</th>
                    <th>Customer</th>
                    <th>Service/Product</th>
                    <th>Date & Time</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="bookingTableBody">
                  <tr>
                    <td>#BK001</td>
                    <td>Rajesh Kumar</td>
                    <td>Home Cleaning Service</td>
                    <td>2024-01-15 10:30 AM</td>
                    <td>₹1,200</td>
                    <td><span class="status-badge confirmed">Confirmed</span></td>
                    <td>
                      <button class="action-btn-small view">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small edit">
                        <span class="material-icons">edit</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#BK002</td>
                    <td>Priya Sharma</td>
                    <td>Grocery Delivery</td>
                    <td>2024-01-15 02:15 PM</td>
                    <td>₹850</td>
                    <td><span class="status-badge pending">Pending</span></td>
                    <td>
                      <button class="action-btn-small view">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small edit">
                        <span class="material-icons">edit</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#BK003</td>
                    <td>Amit Patel</td>
                    <td>AC Repair Service</td>
                    <td>2024-01-15 04:45 PM</td>
                    <td>₹2,500</td>
                    <td><span class="status-badge confirmed">Confirmed</span></td>
                    <td>
                      <button class="action-btn-small view">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small edit">
                        <span class="material-icons">edit</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#BK004</td>
                    <td>Sneha Reddy</td>
                    <td>Food Delivery</td>
                    <td>2024-01-15 07:20 PM</td>
                    <td>₹450</td>
                    <td><span class="status-badge cancelled">Cancelled</span></td>
                    <td>
                      <button class="action-btn-small view">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small edit">
                        <span class="material-icons">edit</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#BK005</td>
                    <td>Vikram Singh</td>
                    <td>Plumbing Service</td>
                    <td>2024-01-15 09:10 PM</td>
                    <td>₹1,800</td>
                    <td><span class="status-badge pending">Pending</span></td>
                    <td>
                      <button class="action-btn-small view">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small edit">
                        <span class="material-icons">edit</span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Real-time Metrics -->
        <div class="realtime-section">
          <h3>Real-time Metrics</h3>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-header">
                <h4>Active Orders</h4>
                <span class="live-indicator"></span>
              </div>
              <div class="metric-value" id="activeOrders">1,234</div>
              <div class="metric-chart">
                <canvas id="activeOrdersChart"></canvas>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <h4>Online Riders</h4>
                <span class="live-indicator"></span>
              </div>
              <div class="metric-value" id="onlineRiders">856</div>
              <div class="metric-chart">
                <canvas id="onlineRidersChart"></canvas>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <h4>Avg Delivery Time</h4>
                <span class="live-indicator"></span>
              </div>
              <div class="metric-value" id="avgDeliveryTime">28 min</div>
              <div class="metric-chart">
                <canvas id="deliveryTimeChart"></canvas>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <h4>Customer Satisfaction</h4>
                <span class="live-indicator"></span>
              </div>
              <div class="metric-value" id="customerSatisfaction">4.8★</div>
              <div class="metric-chart">
                <canvas id="satisfactionChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Geographic View -->
        <div class="geographic-section">
          <h3>Geographic Distribution</h3>
          <div class="geo-container">
            <div class="geo-map">
              <div class="map-placeholder">
                <div class="map-content">
                  <span class="material-icons">map</span>
                  <h4>India Coverage Map</h4>
                  <p>Interactive map showing service coverage across India</p>
                  <div class="coverage-stats">
                    <div class="coverage-item">
                      <span class="coverage-number">100+</span>
                      <span class="coverage-label">Cities</span>
                    </div>
                    <div class="coverage-item">
                      <span class="coverage-number">28</span>
                      <span class="coverage-label">States</span>
                    </div>
                    <div class="coverage-item">
                      <span class="coverage-number">95%</span>
                      <span class="coverage-label">Coverage</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="geo-stats">
              <h4>Top Performing Cities</h4>
              <div class="city-list">
                <div class="city-item">
                  <div class="city-info">
                    <span class="city-name">Bangalore</span>
                    <span class="city-orders">12,456 orders</span>
                  </div>
                  <div class="city-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <span class="progress-value">85%</span>
                  </div>
                </div>
                
                <div class="city-item">
                  <div class="city-info">
                    <span class="city-name">Mumbai</span>
                    <span class="city-orders">10,234 orders</span>
                  </div>
                  <div class="city-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 72%"></div>
                    </div>
                    <span class="progress-value">72%</span>
                  </div>
                </div>
                
                <div class="city-item">
                  <div class="city-info">
                    <span class="city-name">Delhi</span>
                    <span class="city-orders">9,876 orders</span>
                  </div>
                  <div class="city-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 68%"></div>
                    </div>
                    <span class="progress-value">68%</span>
                  </div>
                </div>
                
                <div class="city-item">
                  <div class="city-info">
                    <span class="city-name">Chennai</span>
                    <span class="city-orders">7,543 orders</span>
                  </div>
                  <div class="city-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 55%"></div>
                    </div>
                    <span class="progress-value">55%</span>
                  </div>
                </div>
                
                <div class="city-item">
                  <div class="city-info">
                    <span class="city-name">Hyderabad</span>
                    <span class="city-orders">6,789 orders</span>
                  </div>
                  <div class="city-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 48%"></div>
                    </div>
                    <span class="progress-value">48%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>Quick Actions</h3>
          <div class="actions-grid">
            <button class="action-card">
              <span class="material-icons">add_business</span>
              <span>Add Restaurant</span>
            </button>
            
            <button class="action-card">
              <span class="material-icons">person_add</span>
              <span>Add Rider</span>
            </button>
            
            <button class="action-card">
              <span class="material-icons">campaign</span>
              <span>Send Notification</span>
            </button>
            
            <button class="action-card">
              <span class="material-icons">analytics</span>
              <span>Generate Report</span>
            </button>
            
            <button class="action-card">
              <span class="material-icons">support_agent</span>
              <span>Support Tickets</span>
            </button>
            
            <button class="action-card">
              <span class="material-icons">settings</span>
              <span>System Settings</span>
            </button>
          </div>
        </div>
      </section>

      <!-- Orders Management Section -->
      <section id="orders-section" class="content-section">
        <div class="section-header">
          <h2>🛒 Orders Management</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">add</span>
              New Order
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Orders
            </button>
          </div>
        </div>

        <!-- Orders Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card active">
            <div class="stat-icon">
              <span class="material-icons">check_circle</span>
            </div>
            <div class="stat-content">
              <h3>5,678</h3>
              <p>Completed Orders</p>
              <span class="stat-change positive">+18.5%</span>
            </div>
          </div>
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">schedule</span>
            </div>
            <div class="stat-content">
              <h3>1,234</h3>
              <p>Pending Orders</p>
              <span class="stat-change neutral">****%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">cancel</span>
            </div>
            <div class="stat-content">
              <h3>456</h3>
              <p>Cancelled Orders</p>
              <span class="stat-change negative">-5.2%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">shopping_cart</span>
            </div>
            <div class="stat-content">
              <h3>7,368</h3>
              <p>Total Orders</p>
              <span class="stat-change positive">+12.8%</span>
            </div>
          </div>
        </div>

        <!-- Orders Table -->
        <div class="profile-table-container">
          <div class="table-header">
            <h4>Recent Orders</h4>
            <div class="table-filters">
              <select id="orderStatusFilter">
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <input type="text" placeholder="Search orders..." class="search-input">
            </div>
          </div>
          <div class="profile-table">
            <table>
              <thead>
                <tr>
                  <th>Order ID</th>
                  <th>Customer</th>
                  <th>Items</th>
                  <th>Amount</th>
                  <th>Status</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#ORD001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/user1.jpg" alt="Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Rajesh Kumar</strong>
                        <small>+91 98765 43210</small>
                      </div>
                    </div>
                  </td>
                  <td>Pizza, Burger, Coke</td>
                  <td>₹850</td>
                  <td><span class="status-badge active">Completed</span></td>
                  <td>2024-01-15 10:30 AM</td>
                  <td>
                    <button class="action-btn-small view">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#ORD002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/user2.jpg" alt="Priya Sharma" class="profile-avatar">
                      <div>
                        <strong>Priya Sharma</strong>
                        <small>+91 87654 32109</small>
                      </div>
                    </div>
                  </td>
                  <td>Groceries, Vegetables</td>
                  <td>₹1,200</td>
                  <td><span class="status-badge pending">Pending</span></td>
                  <td>2024-01-15 02:15 PM</td>
                  <td>
                    <button class="action-btn-small view">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Cancellation History Section -->
        <div class="cancellation-history-section">
          <div class="section-header">
            <h3>📊 Order Cancellation History & Analytics</h3>
            <div class="section-actions">
              <button class="btn-secondary" onclick="exportCancellationData()">
                <span class="material-icons">download</span>
                Export Cancel Data
              </button>
            </div>
          </div>

          <!-- Cancellation Stats -->
          <div class="cancellation-stats-grid">
            <div class="cancel-stat-card total">
              <div class="cancel-icon">
                <span class="material-icons">cancel</span>
              </div>
              <div class="cancel-content">
                <h4>456</h4>
                <p>Total Cancelled Orders</p>
                <span class="cancel-change negative">-5.2% from last month</span>
              </div>
            </div>

            <div class="cancel-stat-card customer">
              <div class="cancel-icon">
                <span class="material-icons">person_off</span>
              </div>
              <div class="cancel-content">
                <h4>278</h4>
                <p>Customer Cancelled</p>
                <span class="cancel-percentage">60.9%</span>
              </div>
            </div>

            <div class="cancel-stat-card restaurant">
              <div class="cancel-icon">
                <span class="material-icons">store_mall_directory</span>
              </div>
              <div class="cancel-content">
                <h4>123</h4>
                <p>Restaurant Cancelled</p>
                <span class="cancel-percentage">27.0%</span>
              </div>
            </div>

            <div class="cancel-stat-card system">
              <div class="cancel-icon">
                <span class="material-icons">error</span>
              </div>
              <div class="cancel-content">
                <h4>55</h4>
                <p>System/Technical Issues</p>
                <span class="cancel-percentage">12.1%</span>
              </div>
            </div>
          </div>

          <!-- Cancellation Reasons Chart -->
          <div class="cancellation-chart-container">
            <div class="chart-header">
              <h4>📈 Cancellation Reasons Breakdown</h4>
              <div class="chart-filters">
                <select id="cancelTimeFilter">
                  <option value="7d">Last 7 Days</option>
                  <option value="30d" selected>Last 30 Days</option>
                  <option value="90d">Last 90 Days</option>
                </select>
              </div>
            </div>
            <div class="chart-content">
              <canvas id="cancellationReasonsChart" width="600" height="300"></canvas>
            </div>
          </div>

          <!-- Detailed Cancellation Table -->
          <div class="cancellation-table-container">
            <div class="table-header">
              <h4>🗂️ Detailed Cancellation Records</h4>
              <div class="table-filters">
                <select id="cancelReasonFilter">
                  <option value="all">All Reasons</option>
                  <option value="customer">Customer Cancelled</option>
                  <option value="restaurant">Restaurant Unavailable</option>
                  <option value="rider">Rider Issues</option>
                  <option value="payment">Payment Failed</option>
                  <option value="technical">Technical Issues</option>
                </select>
                <input type="text" placeholder="Search cancelled orders..." class="search-input">
              </div>
            </div>
            <div class="cancellation-table">
              <table>
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Restaurant/Store</th>
                    <th>Order Value</th>
                    <th>Cancellation Reason</th>
                    <th>Cancelled By</th>
                    <th>Cancel Time</th>
                    <th>Refund Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>#ORD1001</td>
                    <td>
                      <div class="profile-info">
                        <img src="../assets/images/avatars/user1.jpg" alt="Rajesh Kumar" class="profile-avatar">
                        <div>
                          <strong>Rajesh Kumar</strong>
                          <small>+91 98765 43210</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>Pizza Palace</strong>
                        <small>Italian Cuisine</small>
                      </div>
                    </td>
                    <td>₹850</td>
                    <td>
                      <span class="cancel-reason customer">Customer changed mind</span>
                    </td>
                    <td>Customer</td>
                    <td>
                      <div>2024-01-15 10:45 AM</div>
                      <small>5 min after order</small>
                    </td>
                    <td><span class="refund-badge completed">Refunded</span></td>
                    <td>
                      <button class="action-btn-small view" onclick="viewCancelDetails('ORD1001')">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small contact">
                        <span class="material-icons">phone</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#ORD1002</td>
                    <td>
                      <div class="profile-info">
                        <img src="../assets/images/avatars/user2.jpg" alt="Priya Sharma" class="profile-avatar">
                        <div>
                          <strong>Priya Sharma</strong>
                          <small>+91 87654 32109</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>Fresh Mart</strong>
                        <small>Grocery Store</small>
                      </div>
                    </td>
                    <td>₹1,200</td>
                    <td>
                      <span class="cancel-reason restaurant">Items out of stock</span>
                    </td>
                    <td>Restaurant</td>
                    <td>
                      <div>2024-01-15 02:30 PM</div>
                      <small>15 min after order</small>
                    </td>
                    <td><span class="refund-badge completed">Refunded</span></td>
                    <td>
                      <button class="action-btn-small view" onclick="viewCancelDetails('ORD1002')">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small contact">
                        <span class="material-icons">phone</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#ORD1003</td>
                    <td>
                      <div class="profile-info">
                        <img src="../assets/images/avatars/user3.jpg" alt="Amit Patel" class="profile-avatar">
                        <div>
                          <strong>Amit Patel</strong>
                          <small>+91 76543 21098</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>Burger King</strong>
                        <small>Fast Food</small>
                      </div>
                    </td>
                    <td>₹650</td>
                    <td>
                      <span class="cancel-reason payment">Payment gateway failed</span>
                    </td>
                    <td>System</td>
                    <td>
                      <div>2024-01-15 04:15 PM</div>
                      <small>2 min after order</small>
                    </td>
                    <td><span class="refund-badge processing">Processing</span></td>
                    <td>
                      <button class="action-btn-small view" onclick="viewCancelDetails('ORD1003')">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small contact">
                        <span class="material-icons">phone</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#ORD1004</td>
                    <td>
                      <div class="profile-info">
                        <img src="../assets/images/avatars/user4.jpg" alt="Sneha Reddy" class="profile-avatar">
                        <div>
                          <strong>Sneha Reddy</strong>
                          <small>+91 65432 10987</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>Domino's Pizza</strong>
                        <small>Pizza Chain</small>
                      </div>
                    </td>
                    <td>₹950</td>
                    <td>
                      <span class="cancel-reason rider">No rider available</span>
                    </td>
                    <td>System</td>
                    <td>
                      <div>2024-01-15 07:45 PM</div>
                      <small>25 min after order</small>
                    </td>
                    <td><span class="refund-badge completed">Refunded</span></td>
                    <td>
                      <button class="action-btn-small view" onclick="viewCancelDetails('ORD1004')">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small contact">
                        <span class="material-icons">phone</span>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>#ORD1005</td>
                    <td>
                      <div class="profile-info">
                        <img src="../assets/images/avatars/user5.jpg" alt="Vikram Singh" class="profile-avatar">
                        <div>
                          <strong>Vikram Singh</strong>
                          <small>+91 54321 09876</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>KFC</strong>
                        <small>Fried Chicken</small>
                      </div>
                    </td>
                    <td>₹750</td>
                    <td>
                      <span class="cancel-reason customer">Long delivery time</span>
                    </td>
                    <td>Customer</td>
                    <td>
                      <div>2024-01-15 09:20 PM</div>
                      <small>35 min after order</small>
                    </td>
                    <td><span class="refund-badge completed">Refunded</span></td>
                    <td>
                      <button class="action-btn-small view" onclick="viewCancelDetails('ORD1005')">
                        <span class="material-icons">visibility</span>
                      </button>
                      <button class="action-btn-small contact">
                        <span class="material-icons">phone</span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Cancellation Insights -->
          <div class="cancellation-insights">
            <h4>💡 Cancellation Insights & Recommendations</h4>
            <div class="insights-grid">
              <div class="insight-card high-priority">
                <div class="insight-icon">
                  <span class="material-icons">warning</span>
                </div>
                <div class="insight-content">
                  <h5>High Customer Cancellations</h5>
                  <p>60.9% of cancellations are customer-initiated. Consider improving order confirmation flow and estimated delivery times.</p>
                  <button class="insight-action">View Details</button>
                </div>
              </div>

              <div class="insight-card medium-priority">
                <div class="insight-icon">
                  <span class="material-icons">store</span>
                </div>
                <div class="insight-content">
                  <h5>Restaurant Stock Issues</h5>
                  <p>27% cancellations due to unavailable items. Implement real-time inventory management.</p>
                  <button class="insight-action">View Details</button>
                </div>
              </div>

              <div class="insight-card low-priority">
                <div class="insight-icon">
                  <span class="material-icons">payment</span>
                </div>
                <div class="insight-content">
                  <h5>Payment Gateway Issues</h5>
                  <p>12.1% technical cancellations. Consider backup payment options.</p>
                  <button class="insight-action">View Details</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Users Management Section -->
      <section id="users-section" class="content-section">
        <div class="section-header">
          <h2>👥 Users Management</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">person_add</span>
              Add User
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Users
            </button>
          </div>
        </div>

        <!-- Users Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card active">
            <div class="stat-icon">
              <span class="material-icons">people</span>
            </div>
            <div class="stat-content">
              <h3>45,234</h3>
              <p>Active Users</p>
              <span class="stat-change positive">+15.3%</span>
            </div>
          </div>
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">person_add</span>
            </div>
            <div class="stat-content">
              <h3>2,456</h3>
              <p>New This Month</p>
              <span class="stat-change positive">+25.7%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">block</span>
            </div>
            <div class="stat-content">
              <h3>234</h3>
              <p>Blocked Users</p>
              <span class="stat-change negative">+1.2%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">groups</span>
            </div>
            <div class="stat-content">
              <h3>47,924</h3>
              <p>Total Users</p>
              <span class="stat-change positive">+18.9%</span>
            </div>
          </div>
        </div>

        <!-- Users Table -->
        <div class="profile-table-container">
          <div class="table-header">
            <h4>Users List</h4>
            <div class="table-filters">
              <select id="userStatusFilter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="blocked">Blocked</option>
              </select>
              <input type="text" placeholder="Search users..." class="search-input">
            </div>
          </div>
          <div class="profile-table">
            <table>
              <thead>
                <tr>
                  <th>User ID</th>
                  <th>Name & Photo</th>
                  <th>Contact</th>
                  <th>Join Date</th>
                  <th>Orders</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#U001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/user1.jpg" alt="Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Rajesh Kumar</strong>
                        <small>Customer</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 98765 43210</div>
                    <small><EMAIL></small>
                  </td>
                  <td>Jan 2024</td>
                  <td>45 orders</td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#U002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/user2.jpg" alt="Priya Sharma" class="profile-avatar">
                      <div>
                        <strong>Priya Sharma</strong>
                        <small>Premium Customer</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 87654 32109</div>
                    <small><EMAIL></small>
                  </td>
                  <td>Dec 2023</td>
                  <td>78 orders</td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Riders Management Section -->
      <section id="riders-section" class="content-section">
        <div class="section-header">
          <h2>🚴‍♂️ Active Riders Management</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">add</span>
              Add New Rider
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Data
            </button>
          </div>
        </div>

        <!-- Riders Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card active">
            <div class="stat-icon">
              <span class="material-icons">check_circle</span>
            </div>
            <div class="stat-content">
              <h3>1,856</h3>
              <p>Active Riders</p>
              <span class="stat-change positive">+12.5%</span>
            </div>
          </div>
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">pending</span>
            </div>
            <div class="stat-content">
              <h3>234</h3>
              <p>Pending Verification</p>
              <span class="stat-change neutral">+5.2%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">cancel</span>
            </div>
            <div class="stat-content">
              <h3>66</h3>
              <p>Rejected/Suspended</p>
              <span class="stat-change negative">-2.1%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">directions_bike</span>
            </div>
            <div class="stat-content">
              <h3>2,156</h3>
              <p>Total Riders</p>
              <span class="stat-change positive">+8.7%</span>
            </div>
          </div>
        </div>

        <!-- Riders Table -->
        <div class="profile-table-container">
          <div class="table-header">
            <h4>Riders List</h4>
            <div class="table-filters">
              <select id="riderStatusFilter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
              </select>
              <input type="text" placeholder="Search riders..." class="search-input">
            </div>
          </div>
          <div class="profile-table">
            <table>
              <thead>
                <tr>
                  <th>Rider ID</th>
                  <th>Name & Photo</th>
                  <th>Contact</th>
                  <th>Vehicle Info</th>
                  <th>KYC Status</th>
                  <th>Rating</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#R001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/rider1.jpg" alt="Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Rajesh Kumar</strong>
                        <small>Joined: Jan 2024</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 98765 43210</div>
                    <small><EMAIL></small>
                  </td>
                  <td>
                    <div><strong>KA 01 AB 1234</strong></div>
                    <small>Honda Activa 6G</small>
                  </td>
                  <td><span class="kyc-badge verified">✓ Verified</span></td>
                  <td>
                    <div class="rating">
                      <span>4.8</span>
                      <div class="stars">★★★★★</div>
                    </div>
                  </td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewRiderProfile('R001')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#R002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/rider2.jpg" alt="Amit Singh" class="profile-avatar">
                      <div>
                        <strong>Amit Singh</strong>
                        <small>Joined: Dec 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 87654 32109</div>
                    <small><EMAIL></small>
                  </td>
                  <td>
                    <div><strong>KA 02 CD 5678</strong></div>
                    <small>TVS Jupiter</small>
                  </td>
                  <td><span class="kyc-badge pending">⏳ Pending</span></td>
                  <td>
                    <div class="rating">
                      <span>4.6</span>
                      <div class="stars">★★★★☆</div>
                    </div>
                  </td>
                  <td><span class="status-badge pending">Pending</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewRiderProfile('R002')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Sellers Management Section -->
      <section id="sellers-section" class="content-section">
        <div class="section-header">
          <h2>🏪 Active Sellers Management</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">add</span>
              Add New Seller
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Data
            </button>
          </div>
        </div>

        <!-- Sellers Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card active">
            <div class="stat-icon">
              <span class="material-icons">store</span>
            </div>
            <div class="stat-content">
              <h3>1,089</h3>
              <p>Active Sellers</p>
              <span class="stat-change positive">+15.3%</span>
            </div>
          </div>
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">pending</span>
            </div>
            <div class="stat-content">
              <h3>123</h3>
              <p>Pending Verification</p>
              <span class="stat-change neutral">+8.2%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">block</span>
            </div>
            <div class="stat-content">
              <h3>33</h3>
              <p>Suspended</p>
              <span class="stat-change negative">-1.5%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">business</span>
            </div>
            <div class="stat-content">
              <h3>1,245</h3>
              <p>Total Sellers</p>
              <span class="stat-change positive">+12.7%</span>
            </div>
          </div>
        </div>

        <!-- Sellers Table -->
        <div class="profile-table-container">
          <div class="table-header">
            <h4>Sellers List</h4>
            <div class="table-filters">
              <select id="sellerStatusFilter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
              </select>
              <select id="sellerCategoryFilter">
                <option value="all">All Categories</option>
                <option value="restaurant">Restaurant</option>
                <option value="grocery">Grocery</option>
                <option value="pharmacy">Pharmacy</option>
                <option value="electronics">Electronics</option>
              </select>
              <input type="text" placeholder="Search sellers..." class="search-input">
            </div>
          </div>
          <div class="profile-table">
            <table>
              <thead>
                <tr>
                  <th>Seller ID</th>
                  <th>Business Info</th>
                  <th>Owner Details</th>
                  <th>Category</th>
                  <th>Documents</th>
                  <th>Rating</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#S001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/store1.jpg" alt="Pizza Palace" class="profile-avatar">
                      <div>
                        <strong>Pizza Palace</strong>
                        <small>Joined: Nov 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div><strong>Ramesh Gupta</strong></div>
                    <div>+91 98765 11111</div>
                    <small><EMAIL></small>
                  </td>
                  <td><span class="category-badge restaurant">Restaurant</span></td>
                  <td>
                    <div class="document-status">
                      <span class="doc-item verified">FSSAI ✓</span>
                      <span class="doc-item verified">GST ✓</span>
                      <span class="doc-item verified">Trade License ✓</span>
                    </div>
                  </td>
                  <td>
                    <div class="rating">
                      <span>4.7</span>
                      <div class="stars">★★★★★</div>
                    </div>
                  </td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewSellerProfile('S001')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#S002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/store2.jpg" alt="Fresh Mart" class="profile-avatar">
                      <div>
                        <strong>Fresh Mart Grocery</strong>
                        <small>Joined: Dec 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div><strong>Suresh Kumar</strong></div>
                    <div>+91 87654 22222</div>
                    <small><EMAIL></small>
                  </td>
                  <td><span class="category-badge grocery">Grocery</span></td>
                  <td>
                    <div class="document-status">
                      <span class="doc-item verified">FSSAI ✓</span>
                      <span class="doc-item pending">GST ⏳</span>
                      <span class="doc-item verified">Trade License ✓</span>
                    </div>
                  </td>
                  <td>
                    <div class="rating">
                      <span>4.5</span>
                      <div class="stars">★★★★☆</div>
                    </div>
                  </td>
                  <td><span class="status-badge pending">Pending</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewSellerProfile('S002')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Service Providers Management Section -->
      <section id="service-providers-section" class="content-section">
        <div class="section-header">
          <h2>🔧 Service Providers Management</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">add</span>
              Add New Provider
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Data
            </button>
          </div>
        </div>

        <!-- Service Providers Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card active">
            <div class="stat-icon">
              <span class="material-icons">engineering</span>
            </div>
            <div class="stat-content">
              <h3>2,987</h3>
              <p>Active Providers</p>
              <span class="stat-change positive">+18.7%</span>
            </div>
          </div>
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">pending</span>
            </div>
            <div class="stat-content">
              <h3>345</h3>
              <p>Pending Verification</p>
              <span class="stat-change neutral">+12.3%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">block</span>
            </div>
            <div class="stat-content">
              <h3>124</h3>
              <p>Suspended</p>
              <span class="stat-change negative">-3.2%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">groups</span>
            </div>
            <div class="stat-content">
              <h3>3,456</h3>
              <p>Total Providers</p>
              <span class="stat-change positive">+15.8%</span>
            </div>
          </div>
        </div>

        <!-- Service Providers Table -->
        <div class="profile-table-container">
          <div class="table-header">
            <h4>Service Providers List</h4>
            <div class="table-filters">
              <select id="providerStatusFilter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
              </select>
              <select id="providerCategoryFilter">
                <option value="all">All Services</option>
                <option value="healthcare">Healthcare</option>
                <option value="education">Education</option>
                <option value="home-services">Home Services</option>
                <option value="beauty">Beauty & Wellness</option>
                <option value="automotive">Automotive</option>
                <option value="legal">Legal</option>
              </select>
              <input type="text" placeholder="Search providers..." class="search-input">
            </div>
          </div>
          <div class="profile-table">
            <table>
              <thead>
                <tr>
                  <th>Provider ID</th>
                  <th>Name & Photo</th>
                  <th>Contact</th>
                  <th>Service Category</th>
                  <th>Certifications</th>
                  <th>Rating</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#SP001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/doctor1.jpg" alt="Dr. Priya Sharma" class="profile-avatar">
                      <div>
                        <strong>Dr. Priya Sharma</strong>
                        <small>Joined: Oct 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 98765 55555</div>
                    <small><EMAIL></small>
                  </td>
                  <td><span class="category-badge healthcare">👩‍⚕️ Doctor</span></td>
                  <td>
                    <div class="document-status">
                      <span class="doc-item verified">MBBS ✓</span>
                      <span class="doc-item verified">Medical License ✓</span>
                      <span class="doc-item verified">Experience Certificate ✓</span>
                    </div>
                  </td>
                  <td>
                    <div class="rating">
                      <span>4.9</span>
                      <div class="stars">★★★★★</div>
                    </div>
                  </td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewProviderProfile('SP001')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#SP002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/teacher1.jpg" alt="Prof. Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Prof. Rajesh Kumar</strong>
                        <small>Joined: Sep 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 87654 66666</div>
                    <small><EMAIL></small>
                  </td>
                  <td><span class="category-badge education">👨‍🏫 Teacher</span></td>
                  <td>
                    <div class="document-status">
                      <span class="doc-item verified">M.Tech ✓</span>
                      <span class="doc-item verified">Teaching License ✓</span>
                      <span class="doc-item pending">Background Check ⏳</span>
                    </div>
                  </td>
                  <td>
                    <div class="rating">
                      <span>4.8</span>
                      <div class="stars">★★★★★</div>
                    </div>
                  </td>
                  <td><span class="status-badge pending">Pending</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewProviderProfile('SP002')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>#SP003</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/plumber1.jpg" alt="Suresh Patel" class="profile-avatar">
                      <div>
                        <strong>Suresh Patel</strong>
                        <small>Joined: Nov 2023</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>+91 76543 77777</div>
                    <small><EMAIL></small>
                  </td>
                  <td><span class="category-badge home-services">🔧 Plumber</span></td>
                  <td>
                    <div class="document-status">
                      <span class="doc-item verified">Trade Certificate ✓</span>
                      <span class="doc-item verified">Experience Letter ✓</span>
                      <span class="doc-item verified">ID Proof ✓</span>
                    </div>
                  </td>
                  <td>
                    <div class="rating">
                      <span>4.6</span>
                      <div class="stars">★★★★☆</div>
                    </div>
                  </td>
                  <td><span class="status-badge active">Active</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="viewProviderProfile('SP003')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small edit">
                      <span class="material-icons">edit</span>
                    </button>
                    <button class="action-btn-small verify">
                      <span class="material-icons">verified</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- KYC & Verification Section -->
      <section id="verification-section" class="content-section">
        <div class="section-header">
          <h2>🛡️ KYC & Document Verification</h2>
          <div class="section-actions">
            <button class="btn-primary">
              <span class="material-icons">verified_user</span>
              Bulk Approve
            </button>
            <button class="btn-secondary">
              <span class="material-icons">download</span>
              Export Report
            </button>
          </div>
        </div>

        <!-- Verification Stats -->
        <div class="profile-stats-grid">
          <div class="profile-stat-card pending">
            <div class="stat-icon">
              <span class="material-icons">pending_actions</span>
            </div>
            <div class="stat-content">
              <h3>89</h3>
              <p>Pending Verification</p>
              <span class="stat-change urgent">Urgent</span>
            </div>
          </div>
          <div class="profile-stat-card verified">
            <div class="stat-icon">
              <span class="material-icons">verified</span>
            </div>
            <div class="stat-content">
              <h3>5,234</h3>
              <p>Verified Profiles</p>
              <span class="stat-change positive">+23.5%</span>
            </div>
          </div>
          <div class="profile-stat-card rejected">
            <div class="stat-icon">
              <span class="material-icons">cancel</span>
            </div>
            <div class="stat-content">
              <h3>156</h3>
              <p>Rejected Documents</p>
              <span class="stat-change negative">+5.2%</span>
            </div>
          </div>
          <div class="profile-stat-card total">
            <div class="stat-icon">
              <span class="material-icons">assignment</span>
            </div>
            <div class="stat-content">
              <h3>5,479</h3>
              <p>Total Submissions</p>
              <span class="stat-change positive">+18.7%</span>
            </div>
          </div>
        </div>

        <!-- Verification Queue -->
        <div class="verification-queue-container">
          <div class="table-header">
            <h4>Document Verification Queue</h4>
            <div class="table-filters">
              <select id="verificationTypeFilter">
                <option value="all">All Types</option>
                <option value="kyc">KYC Documents</option>
                <option value="license">Professional License</option>
                <option value="certificate">Certificates</option>
                <option value="identity">Identity Proof</option>
              </select>
              <select id="verificationStatusFilter">
                <option value="pending">Pending Review</option>
                <option value="all">All Status</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
          <div class="verification-table">
            <table>
              <thead>
                <tr>
                  <th>Submission ID</th>
                  <th>User Details</th>
                  <th>Document Type</th>
                  <th>Submitted Date</th>
                  <th>Priority</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr class="priority-high">
                  <td>#VER001</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/doctor1.jpg" alt="Dr. Priya Sharma" class="profile-avatar">
                      <div>
                        <strong>Dr. Priya Sharma</strong>
                        <small>Service Provider - Doctor</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="document-type">
                      <span class="doc-icon">📋</span>
                      <div>
                        <strong>Medical License</strong>
                        <small>Professional Certificate</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>2024-01-15</div>
                    <small>2 days ago</small>
                  </td>
                  <td><span class="priority-badge high">High</span></td>
                  <td><span class="status-badge pending">Pending Review</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="reviewDocument('VER001')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small approve">
                      <span class="material-icons">check</span>
                    </button>
                    <button class="action-btn-small reject">
                      <span class="material-icons">close</span>
                    </button>
                  </td>
                </tr>
                <tr class="priority-medium">
                  <td>#VER002</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/teacher1.jpg" alt="Prof. Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Prof. Rajesh Kumar</strong>
                        <small>Service Provider - Teacher</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="document-type">
                      <span class="doc-icon">🎓</span>
                      <div>
                        <strong>Teaching Certificate</strong>
                        <small>Educational Qualification</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>2024-01-14</div>
                    <small>3 days ago</small>
                  </td>
                  <td><span class="priority-badge medium">Medium</span></td>
                  <td><span class="status-badge pending">Pending Review</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="reviewDocument('VER002')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small approve">
                      <span class="material-icons">check</span>
                    </button>
                    <button class="action-btn-small reject">
                      <span class="material-icons">close</span>
                    </button>
                  </td>
                </tr>
                <tr class="priority-low">
                  <td>#VER003</td>
                  <td>
                    <div class="profile-info">
                      <img src="../assets/images/avatars/rider1.jpg" alt="Rajesh Kumar" class="profile-avatar">
                      <div>
                        <strong>Rajesh Kumar</strong>
                        <small>Rider</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="document-type">
                      <span class="doc-icon">🆔</span>
                      <div>
                        <strong>Driving License</strong>
                        <small>Identity & License</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>2024-01-13</div>
                    <small>4 days ago</small>
                  </td>
                  <td><span class="priority-badge low">Low</span></td>
                  <td><span class="status-badge pending">Pending Review</span></td>
                  <td>
                    <button class="action-btn-small view" onclick="reviewDocument('VER003')">
                      <span class="material-icons">visibility</span>
                    </button>
                    <button class="action-btn-small approve">
                      <span class="material-icons">check</span>
                    </button>
                    <button class="action-btn-small reject">
                      <span class="material-icons">close</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Gaming Controls Section -->
      <section id="gaming-section" class="content-section">
        <div class="section-header">
          <h2>🎮 Gaming Controls & Monitoring</h2>
          <p>Monitor and control all gaming activities on the platform</p>
        </div>

        <!-- Gaming Metrics Cards -->
        <div class="metrics-grid">
          <div class="metric-card gaming">
            <div class="metric-icon">
              <span class="material-icons">sports_esports</span>
            </div>
            <div class="metric-content">
              <h3 id="activePlayersCount">2,847</h3>
              <p>Active Players</p>
              <span class="metric-change positive">+12%</span>
            </div>
          </div>

          <div class="metric-card gaming">
            <div class="metric-icon">
              <span class="material-icons">casino</span>
            </div>
            <div class="metric-content">
              <h3 id="gamesPlayedToday">15,623</h3>
              <p>Games Played Today</p>
              <span class="metric-change positive">+8%</span>
            </div>
          </div>

          <div class="metric-card gaming">
            <div class="metric-icon">
              <span class="material-icons">monetization_on</span>
            </div>
            <div class="metric-content">
              <h3 id="totalWinningsAmount">₹24,56,789</h3>
              <p>Total Winnings</p>
              <span class="metric-change positive">+15%</span>
            </div>
          </div>

          <div class="metric-card gaming">
            <div class="metric-icon">
              <span class="material-icons">trending_up</span>
            </div>
            <div class="metric-content">
              <h3 id="houseEdgePercent">12.5%</h3>
              <p>House Edge</p>
              <span class="metric-change neutral">Stable</span>
            </div>
          </div>
        </div>

        <!-- Gaming Controls Panel -->
        <div class="control-panel">
          <h3>🎮 Gaming Control Panel</h3>
          <div class="control-buttons">
            <button class="control-btn pause" id="pauseGaming">
              <span class="material-icons">pause</span>
              Pause All Games
            </button>
            <button class="control-btn resume" id="resumeGaming">
              <span class="material-icons">play_arrow</span>
              Resume Games
            </button>
            <button class="control-btn adjust" id="adjustHouseEdge">
              <span class="material-icons">tune</span>
              Adjust House Edge
            </button>
            <button class="control-btn reports" id="viewGamingReports">
              <span class="material-icons">assessment</span>
              Gaming Reports
            </button>
          </div>
        </div>

        <!-- Gaming Alerts -->
        <div class="alerts-section">
          <h3>⚠️ Gaming Alerts</h3>
          <div id="gamingAlerts" class="alerts-container">
            <!-- Dynamic alerts will be added here -->
          </div>
        </div>

        <!-- Gaming Leaderboard -->
        <div class="leaderboard-section">
          <h3>🏆 Top Players</h3>
          <div id="gamingLeaderboard" class="leaderboard-container">
            <!-- Dynamic leaderboard will be added here -->
          </div>
        </div>

        <!-- DEMO ONLY: Manual Game Controls -->
        <div class="demo-warning-section">
          <div class="demo-warning">
            ⚠️ <strong>DEMO ONLY</strong> - These controls are for demonstration purposes only and should NEVER be used in production. Manual game outcome control violates gaming regulations and fair play standards.
          </div>
        </div>

        <div class="manual-controls-section">
          <h3>🎯 Manual Game Controls (DEMO ONLY)</h3>

          <!-- Active Games Monitor -->
          <div class="active-games-panel">
            <h4>🎮 Active Games Monitor</h4>
            <div id="activeGamesList" class="active-games-list">
              <!-- Dynamic active games will be added here -->
            </div>
          </div>

          <!-- Manual Control Panel -->
          <div class="manual-control-panel">
            <h4>🎛️ Manual Outcome Controls (DEMO)</h4>
            <div class="control-grid">
              <button class="manual-btn force-win" id="forceWinBtn">
                <span class="material-icons">emoji_events</span>
                Force Player Win
              </button>
              <button class="manual-btn force-lose" id="forceLoseBtn">
                <span class="material-icons">cancel</span>
                Force Player Loss
              </button>
              <button class="manual-btn override-result" id="overrideResultBtn">
                <span class="material-icons">edit</span>
                Override Game Result
              </button>
              <button class="manual-btn custom-outcome" id="customOutcomeBtn">
                <span class="material-icons">tune</span>
                Custom Outcome
              </button>
            </div>
          </div>

          <!-- Player Selection Panel -->
          <div class="player-selection-panel">
            <h4>👥 Player Selection (DEMO)</h4>
            <div class="player-controls">
              <select id="targetPlayerSelect" class="player-select">
                <option value="">Select Player...</option>
                <option value="P001">Rajesh Kumar (Game #G123)</option>
                <option value="P002">Priya Sharma (Game #G124)</option>
                <option value="P003">Amit Singh (Game #G125)</option>
                <option value="P004">Vikram Patel (Game #G126)</option>
              </select>
              <select id="gameOutcomeSelect" class="outcome-select">
                <option value="">Select Outcome...</option>
                <option value="win">Force Win</option>
                <option value="lose">Force Loss</option>
                <option value="draw">Force Draw</option>
                <option value="custom">Custom Amount</option>
              </select>
              <input type="number" id="customAmountInput" placeholder="Custom Amount (₹)" class="custom-amount-input" style="display: none;">
              <button class="apply-btn" id="applyOutcomeBtn">Apply Outcome</button>
            </div>
          </div>

          <!-- Audit Trail -->
          <div class="audit-trail-section">
            <h4>📋 Manual Interventions Log (DEMO)</h4>
            <div id="auditTrail" class="audit-trail">
              <!-- Audit entries will be added here -->
            </div>
          </div>
        </div>
      </section>

      <!-- Wallet Oversight Section -->
      <section id="wallet-section" class="content-section">
        <div class="section-header">
          <h2>💰 Wallet Oversight & Transaction Monitoring</h2>
          <p>Monitor and control all wallet operations and transactions</p>
        </div>

        <!-- Wallet Metrics Cards -->
        <div class="metrics-grid">
          <div class="metric-card wallet">
            <div class="metric-icon">
              <span class="material-icons">account_balance_wallet</span>
            </div>
            <div class="metric-content">
              <h3 id="totalWalletBalance">₹456.8L</h3>
              <p>Total Wallet Balance</p>
              <span class="metric-change positive">+5.2%</span>
            </div>
          </div>

          <div class="metric-card wallet">
            <div class="metric-icon">
              <span class="material-icons">swap_horiz</span>
            </div>
            <div class="metric-content">
              <h3 id="totalTransactionsCount">1,56,789</h3>
              <p>Total Transactions</p>
              <span class="metric-change positive">+18%</span>
            </div>
          </div>

          <div class="metric-card wallet">
            <div class="metric-icon">
              <span class="material-icons">hourglass_empty</span>
            </div>
            <div class="metric-content">
              <h3 id="pendingWithdrawalsCount">234</h3>
              <p>Pending Withdrawals</p>
              <span class="metric-change neutral">₹12.3L</span>
            </div>
          </div>

          <div class="metric-card wallet">
            <div class="metric-icon">
              <span class="material-icons">toll</span>
            </div>
            <div class="metric-content">
              <h3 id="projekCoinSupply">12,50,000 PC</h3>
              <p>ProjekCoin Supply</p>
              <span class="metric-change positive">****%</span>
            </div>
          </div>
        </div>

        <!-- Wallet Controls Panel -->
        <div class="control-panel">
          <h3>💰 Wallet Control Panel</h3>
          <div class="control-buttons">
            <button class="control-btn freeze" id="freezeWallet">
              <span class="material-icons">block</span>
              Freeze Wallet
            </button>
            <button class="control-btn approve" id="approveWithdrawals">
              <span class="material-icons">check_circle</span>
              Approve Withdrawals
            </button>
            <button class="control-btn adjust" id="adjustExchangeRate">
              <span class="material-icons">currency_exchange</span>
              Adjust Exchange Rate
            </button>
            <button class="control-btn emergency" id="emergencyStop">
              <span class="material-icons">emergency</span>
              Emergency Stop
            </button>
            <button class="control-btn reports" id="viewWalletReports">
              <span class="material-icons">assessment</span>
              Wallet Reports
            </button>
          </div>
        </div>

        <!-- Transaction Alerts -->
        <div class="alerts-section">
          <h3>🚨 Transaction Alerts</h3>
          <div id="transactionAlerts" class="alerts-container">
            <!-- Dynamic alerts will be added here -->
          </div>
        </div>

        <!-- Exchange Rate Display -->
        <div class="exchange-rate-section">
          <h3>💱 Current Exchange Rate</h3>
          <div class="exchange-rate-display">
            <span class="rate-amount">1 INR = 1 PC</span>
            <span class="rate-status live">🟢 Live Rate</span>
          </div>
        </div>
      </section>

      <section id="analytics-section" class="content-section">
        <h2>📊 Advanced Analytics</h2>
        <p>Advanced analytics interface will be here...</p>
      </section>

      <section id="payments-section" class="content-section">
        <h2>💳 Payments Management</h2>
        <p>Payments management interface will be here...</p>
      </section>

      <section id="settings-section" class="content-section">
        <h2>⚙️ System Settings</h2>
        <p>System settings interface will be here...</p>
      </section>
    </div>
  </main>

  <script src="js/admin-dashboard.js"></script>

  <!-- Navigation Helper Buttons -->
  <div id="navigationHelper" style="position: fixed; top: 80px; right: 20px; z-index: 3000; background: white; padding: 1rem; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.2); font-family: 'Poppins', sans-serif;">
    <h4 style="margin: 0 0 1rem 0; color: #2c2c54; font-size: 0.9rem;">🎯 Profile Management</h4>
    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
      <button onclick="navigateToSection('riders')" style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
        🚴‍♂️ Riders (2,156)
      </button>
      <button onclick="navigateToSection('sellers')" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
        🏪 Sellers (1,245)
      </button>
      <button onclick="navigateToSection('service-providers')" style="background: #ffc107; color: #2c2c54; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
        🔧 Service Providers (3,456)
      </button>
      <button onclick="navigateToSection('verification')" style="background: #dc3545; color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
        🛡️ KYC & Verification (89)
      </button>
      <button onclick="document.getElementById('navigationHelper').remove()" style="background: #6c757d; color: white; border: none; padding: 0.3rem 0.8rem; border-radius: 6px; cursor: pointer; font-size: 0.7rem; margin-top: 0.5rem;">
        ✕ Close
      </button>
    </div>
  </div>

  <script>
    // Make navigation function globally available
    window.navigateToSection = function(sectionName) {
      console.log('Global navigation called for:', sectionName);

      // Define titles
      const titles = {
        'dashboard': 'Dashboard Overview',
        'orders': 'Orders Management',
        'users': 'Users Management',
        'riders': 'Riders Management',
        'sellers': 'Sellers Management',
        'service-providers': 'Service Providers Management',
        'verification': 'KYC & Verification',
        'restaurants': 'Restaurants Management',
        'analytics': 'Advanced Analytics',
        'payments': 'Payments Management',
        'settings': 'System Settings'
      };

      // Update active nav item
      const navItems = document.querySelectorAll('.nav-item');
      navItems.forEach(item => item.classList.remove('active'));

      const targetNavItem = document.querySelector(`a[data-section="${sectionName}"]`);
      if (targetNavItem) {
        targetNavItem.parentElement.classList.add('active');
      }

      // Hide all sections
      const allSections = document.querySelectorAll('.content-section');
      allSections.forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
      });

      // Show target section
      const targetSection = document.getElementById(sectionName + '-section');
      if (targetSection) {
        targetSection.classList.add('active');
        targetSection.style.display = 'block';

        // Update page title
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
          pageTitle.textContent = titles[sectionName] || 'Dashboard';
        }

        // Show success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
          position: fixed; top: 20px; right: 20px; z-index: 3001;
          background: #28a745; color: white; padding: 0.8rem 1.2rem;
          border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          font-family: 'Poppins', sans-serif; font-weight: 600; font-size: 0.9rem;
        `;
        notification.innerHTML = `✅ Navigated to ${titles[sectionName]}`;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 3000);

        console.log('Successfully navigated to:', sectionName);
      } else {
        console.error('Section not found:', sectionName + '-section');
      }
    };

    // Auto-test navigation after page loads
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(() => {
        console.log('Setting up sidebar navigation...');

        // Get all navigation items
        const navItems = document.querySelectorAll('.nav-item a');
        console.log('Found navigation items:', navItems.length);

        navItems.forEach((item, index) => {
          const section = item.getAttribute('data-section');
          console.log(`Setting up nav item ${index}: ${section}`);

          // Remove any existing event listeners
          item.removeEventListener('click', handleNavClick);

          // Add click event listener
          item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const targetSection = this.getAttribute('data-section');
            console.log('Sidebar navigation clicked:', targetSection);

            // Call the global navigation function
            window.navigateToSection(targetSection);
          });

          // Add visual feedback
          item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            this.style.transform = 'translateX(5px)';
          });

          item.addEventListener('mouseleave', function() {
            if (!this.parentElement.classList.contains('active')) {
              this.style.backgroundColor = '';
              this.style.transform = '';
            }
          });
        });

        console.log('Sidebar navigation setup complete!');
      }, 500);
    });

    // Helper function for navigation clicks
    function handleNavClick(e) {
      e.preventDefault();
      e.stopPropagation();

      const targetSection = this.getAttribute('data-section');
      console.log('Navigation clicked:', targetSection);

      window.navigateToSection(targetSection);
    }
  </script>
</body>
</html>
