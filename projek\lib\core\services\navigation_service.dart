import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Service to handle navigation between different parts of the super app
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  /// Navigate to rider app functionality
  static void navigateToRiderApp(BuildContext context) {
    // For now, show a snackbar indicating the feature
    // In the future, this could launch the rider app or navigate to ride booking
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚗 Opening Rider App...'),
        backgroundColor: Colors.green,
      ),
    );
    
    // TODO: Implement actual rider app navigation
    // This could be:
    // 1. Navigate to a ride booking page within the same app
    // 2. Launch a separate rider app
    // 3. Navigate to a unified ride booking interface
  }

  /// Navigate to seller app functionality
  static void navigateToSellerApp(BuildContext context, {String? productId}) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🛍️ Opening Seller App...'),
        backgroundColor: Colors.orange,
      ),
    );
    
    // TODO: Implement actual seller app navigation
    // This could navigate to seller dashboard or specific product
  }

  /// Navigate to food ordering
  static void navigateToFoodOrdering(BuildContext context) {
    // Navigate to categories filtered for food
    context.push('/categories/filtered?type=food&title=Food%20Delivery');
  }

  /// Navigate to games section
  static void navigateToGames(BuildContext context) {
    context.push('/games/spin-wheel');
  }

  /// Navigate to chat system
  static void navigateToChat(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💬 Opening Chat...'),
        backgroundColor: Colors.blue,
      ),
    );
    
    // TODO: Navigate to chat list or create new chat
    // context.push('/chat');
  }

  /// Navigate to specific service booking
  static void navigateToService(BuildContext context, String serviceName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🔧 Opening $serviceName Service...'),
        backgroundColor: Colors.purple,
      ),
    );
    
    // TODO: Navigate to specific service booking page
    // context.push('/services/${serviceName.toLowerCase()}');
  }

  /// Navigate to marketplace category
  static void navigateToCategory(BuildContext context, String category) {
    context.push('/categories/filtered?type=${category.toLowerCase()}&title=$category');
  }

  /// Navigate to spin wheel game
  static void navigateToSpinGame(BuildContext context) {
    context.push('/games/spin-wheel');
  }

  /// Navigate to rewards system
  static void navigateToRewards(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🎁 Opening Rewards...'),
        backgroundColor: Colors.amber,
      ),
    );
    
    // TODO: Navigate to rewards page
    // context.push('/rewards');
  }

  /// Navigate to wallet
  static void navigateToWallet(BuildContext context) {
    context.push('/wallet');
  }

  /// Navigate to add money page
  static void navigateToAddMoney(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💰 Opening Add Money...'),
        backgroundColor: Colors.green,
      ),
    );
    
    // TODO: Navigate to add money page
    // context.push('/wallet/add-money');
  }

  /// Navigate to send money page
  static void navigateToSendMoney(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📤 Opening Send Money...'),
        backgroundColor: Colors.blue,
      ),
    );
    
    // TODO: Navigate to send money page
    // context.push('/wallet/send-money');
  }

  /// Show feature coming soon message
  static void showComingSoon(BuildContext context, String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$featureName is coming soon!'),
        backgroundColor: Colors.grey[600],
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {},
        ),
      ),
    );
  }

  /// Navigate to marketplace home
  static void navigateToMarketplace(BuildContext context) {
    context.push('/categories');
  }

  /// Navigate to profile
  static void navigateToProfile(BuildContext context) {
    context.push('/profile');
  }

  /// Navigate to notifications
  static void navigateToNotifications(BuildContext context) {
    context.push('/notifications');
  }

  /// Navigate to help center
  static void navigateToHelpCenter(BuildContext context) {
    context.push('/enhanced-help-center');
  }
}
