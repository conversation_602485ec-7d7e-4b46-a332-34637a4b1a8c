plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    // Google Services plugin for Firebase
    id "com.google.gms.google-services"
}

// Load keystore properties
def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.projek.user"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    flavorDimensions "app", "environment"

    productFlavors {
        // App flavors
        user {
            dimension "app"
            applicationId = "com.projek.user"
            versionNameSuffix "-user"
            manifestPlaceholders = [appName: "Projek User"]
        }

        rider {
            dimension "app"
            applicationId = "com.projek.rider"
            versionNameSuffix "-rider"
            manifestPlaceholders = [appName: "Projek Rider"]
        }

        seller {
            dimension "app"
            applicationId = "com.projek.seller"
            versionNameSuffix "-seller"
            manifestPlaceholders = [appName: "Projek Seller"]
        }

        // Environment flavors
        dev {
            dimension "environment"
            versionNameSuffix "-dev"
            manifestPlaceholders = [usesCleartextTraffic: "true"]
        }

        prod {
            dimension "environment"
            manifestPlaceholders = [usesCleartextTraffic: "false"]
        }
    }

    signingConfigs {
        if (keystorePropertiesFile.exists()) {
            release {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    defaultConfig {
        // Application ID is defined in product flavors
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = 34  // Android 14 - Compatible with Vivo V23 5G
        compileSdk = 35  // Required by plugins
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // Enable multidex to handle large number of methods
        multiDexEnabled = true

        // Ensure compatibility with modern Android devices
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }

    buildFeatures {
        buildConfig = true
    }

    // Create universal APKs for better device compatibility
    splits {
        abi {
            enable false  // Disabled for universal APK compatibility
            reset()
            include 'arm64-v8a', 'armeabi-v7a'
            universalApk true  // Create universal APK for all devices
        }
    }

    buildTypes {
        debug {
            debuggable = true
            minifyEnabled = false
            shrinkResources = false
            manifestPlaceholders = [usesCleartextTraffic: "true"]
        }

        release {
            // Enable size optimization for smaller APK
            minifyEnabled = true
            shrinkResources = true
            manifestPlaceholders = [usesCleartextTraffic: "false"]
            if (keystorePropertiesFile.exists()) {
                signingConfig = signingConfigs.release
            }
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // MultiDex support for large apps
    implementation 'androidx.multidex:multidex:2.0.1'

    // Core library desugaring for Java 8+ features
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
}
