import 'package:geolocator/geolocator.dart';
import '../database/hive_service.dart';
import '../utils/logger.dart';

class LocationService {
  static Position? _lastKnownPosition;
  static Stream<Position>? _positionStream;

  static Future<void> initialize() async {
    await _requestLocationPermission();
    await _loadLastKnownPosition();
  }

  static Future<bool> _requestLocationPermission() async {
    // Check if location services are enabled
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    // Check location permission
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    // Save permission status
    await HiveService.saveLocationPermission(true);
    return true;
  }

  static Future<Position?> getCurrentLocation() async {
    try {
      final hasPermission = await _requestLocationPermission();
      if (!hasPermission) return null;

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      _lastKnownPosition = position;
      await _saveLastKnownPosition(position);

      return position;
    } catch (e) {
      Logger.error('Error getting current location', e);
      return _lastKnownPosition;
    }
  }

  static Stream<Position> getLocationStream() {
    _positionStream ??=
        Geolocator.getPositionStream(
          locationSettings: const LocationSettings(
            accuracy: LocationAccuracy.high,
            distanceFilter: 10, // Update every 10 meters
            timeLimit: Duration(seconds: 10),
          ),
        ).handleError((error) {
          Logger.error('Location stream error', error);
        });

    return _positionStream!;
  }

  static Future<double> getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) async {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  static Future<double> getBearingBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) async {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  static Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  static Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  static Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  static Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  static Position? get lastKnownPosition => _lastKnownPosition;

  static Future<void> _saveLastKnownPosition(Position position) async {
    await HiveService.saveToCache('last_known_position', {
      'latitude': position.latitude,
      'longitude': position.longitude,
      'accuracy': position.accuracy,
      'timestamp': position.timestamp.millisecondsSinceEpoch,
    });
  }

  static Future<void> _loadLastKnownPosition() async {
    final cached = HiveService.getFromCache<Map<String, dynamic>>(
      'last_known_position',
      maxAge: const Duration(hours: 24),
    );

    if (cached != null) {
      _lastKnownPosition = Position(
        latitude: cached['latitude'],
        longitude: cached['longitude'],
        timestamp: DateTime.fromMillisecondsSinceEpoch(cached['timestamp']),
        accuracy: cached['accuracy'],
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
    }
  }

  // Utility methods for common location operations
  static String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()} m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)} km';
    }
  }

  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  static bool isWithinRadius(
    double centerLat,
    double centerLng,
    double pointLat,
    double pointLng,
    double radiusInMeters,
  ) {
    final distance = Geolocator.distanceBetween(
      centerLat,
      centerLng,
      pointLat,
      pointLng,
    );
    return distance <= radiusInMeters;
  }

  // Background location tracking (for rider app)
  static Future<void> startBackgroundLocationTracking() async {
    // This would require additional setup for background location
    // For now, we'll just start the location stream
    getLocationStream().listen((position) {
      _lastKnownPosition = position;
      _saveLastKnownPosition(position);
    });
  }

  static void stopBackgroundLocationTracking() {
    _positionStream = null;
  }

  // Mock location detection (for development)
  static Future<bool> isMockLocationEnabled() async {
    try {
      final position = await getCurrentLocation();
      return position?.isMocked ?? false;
    } catch (e) {
      return false;
    }
  }
}
