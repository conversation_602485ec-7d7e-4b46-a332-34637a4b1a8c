// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentRequestAdapter extends TypeAdapter<PaymentRequest> {
  @override
  final int typeId = 80;

  @override
  PaymentRequest read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentRequest(
      orderId: fields[0] as String,
      amount: fields[1] as double,
      currency: fields[2] as String,
      description: fields[3] as String,
      method: fields[4] as PaymentMethod,
      customerId: fields[5] as String,
      customerName: fields[6] as String,
      customerEmail: fields[7] as String,
      customerPhone: fields[8] as String,
      metadata: (fields[9] as Map).cast<String, dynamic>(),
      timestamp: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentRequest obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.orderId)
      ..writeByte(1)
      ..write(obj.amount)
      ..writeByte(2)
      ..write(obj.currency)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.method)
      ..writeByte(5)
      ..write(obj.customerId)
      ..writeByte(6)
      ..write(obj.customerName)
      ..writeByte(7)
      ..write(obj.customerEmail)
      ..writeByte(8)
      ..write(obj.customerPhone)
      ..writeByte(9)
      ..write(obj.metadata)
      ..writeByte(10)
      ..write(obj.timestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentRequestAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentResultAdapter extends TypeAdapter<PaymentResult> {
  @override
  final int typeId = 81;

  @override
  PaymentResult read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentResult(
      success: fields[0] as bool,
      transactionId: fields[1] as String?,
      orderId: fields[2] as String,
      amount: fields[3] as double,
      method: fields[4] as PaymentMethod,
      timestamp: fields[5] as DateTime,
      error: fields[6] as String?,
      metadata: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentResult obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.success)
      ..writeByte(1)
      ..write(obj.transactionId)
      ..writeByte(2)
      ..write(obj.orderId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.method)
      ..writeByte(5)
      ..write(obj.timestamp)
      ..writeByte(6)
      ..write(obj.error)
      ..writeByte(7)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentResultAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RefundResultAdapter extends TypeAdapter<RefundResult> {
  @override
  final int typeId = 82;

  @override
  RefundResult read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RefundResult(
      success: fields[0] as bool,
      refundId: fields[1] as String?,
      amount: fields[2] as double,
      status: fields[3] as String,
      timestamp: fields[4] as DateTime,
      error: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, RefundResult obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.success)
      ..writeByte(1)
      ..write(obj.refundId)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.error);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RefundResultAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UpiAppAdapter extends TypeAdapter<UpiApp> {
  @override
  final int typeId = 83;

  @override
  UpiApp read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UpiApp(
      id: fields[0] as String,
      name: fields[1] as String,
      displayName: fields[2] as String,
      packageName: fields[3] as String,
      icon: fields[4] as String,
      isInstalled: fields[5] as bool,
      isSupported: fields[6] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, UpiApp obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.packageName)
      ..writeByte(4)
      ..write(obj.icon)
      ..writeByte(5)
      ..write(obj.isInstalled)
      ..writeByte(6)
      ..write(obj.isSupported);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UpiAppAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BankAccountAdapter extends TypeAdapter<BankAccount> {
  @override
  final int typeId = 84;

  @override
  BankAccount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BankAccount(
      id: fields[0] as String,
      accountNumber: fields[1] as String,
      ifscCode: fields[2] as String,
      bankName: fields[3] as String,
      accountHolderName: fields[4] as String,
      accountType: fields[5] as String,
      isVerified: fields[6] as bool,
      isPrimary: fields[7] as bool,
      createdAt: fields[8] as DateTime,
      verifiedAt: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, BankAccount obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.accountNumber)
      ..writeByte(2)
      ..write(obj.ifscCode)
      ..writeByte(3)
      ..write(obj.bankName)
      ..writeByte(4)
      ..write(obj.accountHolderName)
      ..writeByte(5)
      ..write(obj.accountType)
      ..writeByte(6)
      ..write(obj.isVerified)
      ..writeByte(7)
      ..write(obj.isPrimary)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.verifiedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BankAccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SavedCardAdapter extends TypeAdapter<SavedCard> {
  @override
  final int typeId = 85;

  @override
  SavedCard read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SavedCard(
      id: fields[0] as String,
      last4Digits: fields[1] as String,
      cardType: fields[2] as String,
      bankName: fields[3] as String,
      expiryMonth: fields[4] as String,
      expiryYear: fields[5] as String,
      cardHolderName: fields[6] as String,
      isDefault: fields[7] as bool,
      createdAt: fields[8] as DateTime,
      lastUsed: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, SavedCard obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.last4Digits)
      ..writeByte(2)
      ..write(obj.cardType)
      ..writeByte(3)
      ..write(obj.bankName)
      ..writeByte(4)
      ..write(obj.expiryMonth)
      ..writeByte(5)
      ..write(obj.expiryYear)
      ..writeByte(6)
      ..write(obj.cardHolderName)
      ..writeByte(7)
      ..write(obj.isDefault)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.lastUsed);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SavedCardAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentSummaryAdapter extends TypeAdapter<PaymentSummary> {
  @override
  final int typeId = 86;

  @override
  PaymentSummary read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentSummary(
      subtotal: fields[0] as double,
      tax: fields[1] as double,
      deliveryFee: fields[2] as double,
      discount: fields[3] as double,
      cashback: fields[4] as double,
      total: fields[5] as double,
      currency: fields[6] as String,
      breakdown: (fields[7] as Map).cast<String, double>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentSummary obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.subtotal)
      ..writeByte(1)
      ..write(obj.tax)
      ..writeByte(2)
      ..write(obj.deliveryFee)
      ..writeByte(3)
      ..write(obj.discount)
      ..writeByte(4)
      ..write(obj.cashback)
      ..writeByte(5)
      ..write(obj.total)
      ..writeByte(6)
      ..write(obj.currency)
      ..writeByte(7)
      ..write(obj.breakdown);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentSummaryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) =>
    PaymentRequest(
      orderId: json['orderId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      description: json['description'] as String,
      method: PaymentMethod.fromJson(json['method'] as Map<String, dynamic>),
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      customerEmail: json['customerEmail'] as String,
      customerPhone: json['customerPhone'] as String,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PaymentRequestToJson(PaymentRequest instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'amount': instance.amount,
      'currency': instance.currency,
      'description': instance.description,
      'method': instance.method,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'customerEmail': instance.customerEmail,
      'customerPhone': instance.customerPhone,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp.toIso8601String(),
    };

PaymentResult _$PaymentResultFromJson(Map<String, dynamic> json) =>
    PaymentResult(
      success: json['success'] as bool,
      transactionId: json['transactionId'] as String?,
      orderId: json['orderId'] as String,
      amount: (json['amount'] as num).toDouble(),
      method: PaymentMethod.fromJson(json['method'] as Map<String, dynamic>),
      timestamp: DateTime.parse(json['timestamp'] as String),
      error: json['error'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$PaymentResultToJson(PaymentResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'transactionId': instance.transactionId,
      'orderId': instance.orderId,
      'amount': instance.amount,
      'method': instance.method,
      'timestamp': instance.timestamp.toIso8601String(),
      'error': instance.error,
      'metadata': instance.metadata,
    };

RefundResult _$RefundResultFromJson(Map<String, dynamic> json) => RefundResult(
      success: json['success'] as bool,
      refundId: json['refundId'] as String?,
      amount: (json['amount'] as num).toDouble(),
      status: json['status'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$RefundResultToJson(RefundResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'refundId': instance.refundId,
      'amount': instance.amount,
      'status': instance.status,
      'timestamp': instance.timestamp.toIso8601String(),
      'error': instance.error,
    };

UpiApp _$UpiAppFromJson(Map<String, dynamic> json) => UpiApp(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      packageName: json['packageName'] as String,
      icon: json['icon'] as String,
      isInstalled: json['isInstalled'] as bool? ?? false,
      isSupported: json['isSupported'] as bool? ?? true,
    );

Map<String, dynamic> _$UpiAppToJson(UpiApp instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'packageName': instance.packageName,
      'icon': instance.icon,
      'isInstalled': instance.isInstalled,
      'isSupported': instance.isSupported,
    };

BankAccount _$BankAccountFromJson(Map<String, dynamic> json) => BankAccount(
      id: json['id'] as String,
      accountNumber: json['accountNumber'] as String,
      ifscCode: json['ifscCode'] as String,
      bankName: json['bankName'] as String,
      accountHolderName: json['accountHolderName'] as String,
      accountType: json['accountType'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
      isPrimary: json['isPrimary'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
    );

Map<String, dynamic> _$BankAccountToJson(BankAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountNumber': instance.accountNumber,
      'ifscCode': instance.ifscCode,
      'bankName': instance.bankName,
      'accountHolderName': instance.accountHolderName,
      'accountType': instance.accountType,
      'isVerified': instance.isVerified,
      'isPrimary': instance.isPrimary,
      'createdAt': instance.createdAt.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
    };

SavedCard _$SavedCardFromJson(Map<String, dynamic> json) => SavedCard(
      id: json['id'] as String,
      last4Digits: json['last4Digits'] as String,
      cardType: json['cardType'] as String,
      bankName: json['bankName'] as String,
      expiryMonth: json['expiryMonth'] as String,
      expiryYear: json['expiryYear'] as String,
      cardHolderName: json['cardHolderName'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsed: json['lastUsed'] == null
          ? null
          : DateTime.parse(json['lastUsed'] as String),
    );

Map<String, dynamic> _$SavedCardToJson(SavedCard instance) => <String, dynamic>{
      'id': instance.id,
      'last4Digits': instance.last4Digits,
      'cardType': instance.cardType,
      'bankName': instance.bankName,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'cardHolderName': instance.cardHolderName,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastUsed': instance.lastUsed?.toIso8601String(),
    };

PaymentSummary _$PaymentSummaryFromJson(Map<String, dynamic> json) =>
    PaymentSummary(
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
      deliveryFee: (json['deliveryFee'] as num?)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      cashback: (json['cashback'] as num?)?.toDouble() ?? 0.0,
      total: (json['total'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      breakdown: (json['breakdown'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toDouble()),
          ) ??
          const {},
    );

Map<String, dynamic> _$PaymentSummaryToJson(PaymentSummary instance) =>
    <String, dynamic>{
      'subtotal': instance.subtotal,
      'tax': instance.tax,
      'deliveryFee': instance.deliveryFee,
      'discount': instance.discount,
      'cashback': instance.cashback,
      'total': instance.total,
      'currency': instance.currency,
      'breakdown': instance.breakdown,
    };
