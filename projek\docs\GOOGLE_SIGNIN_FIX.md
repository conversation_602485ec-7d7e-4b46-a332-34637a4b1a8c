# Google Sign-In Web Configuration Fix

## 🎯 **Problem**
Google Sign-In not configured for web platform, causing authentication errors.

## 🔧 **Solution Steps**

### **Step 1: Get Web Client ID from Firebase Console**

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: **"projek-7a8f8 (projek)"**
3. Navigate to **Authentication** → **Sign-in method**
4. Click on **Google** provider
5. Copy the **Web client ID** (looks like: `123456789-abc...xyz.apps.googleusercontent.com`)

### **Step 2: Update web/index.html**

Replace `YOUR_WEB_CLIENT_ID` in line 36 with your actual Web Client ID:

```html
<meta name="google-signin-client-id" content="YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com">
```

### **Step 3: Update lib/main.dart**

Replace `YOUR_WEB_CLIENT_ID` in line 83 with your actual Web Client ID:

```dart
serverClientId: 'YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com',
```

### **Step 4: Restart Your App**

```bash
# Stop the current app (Ctrl+C in terminal)
# Then restart
flutter run -d chrome
```

## 🚀 **Quick Alternative - Enable Google Sign-In in Firebase**

If you haven't enabled Google Sign-In yet:

1. **Firebase Console** → **Authentication** → **Sign-in method**
2. **Click Google** → **Enable**
3. **Add your domain**: `localhost` for testing
4. **Save**

## ✅ **After Fix**

Your Google Sign-In button will work properly and users can authenticate with their Google accounts.

## 🔍 **Find Your Web Client ID**

Your Web Client ID is in:
- Firebase Console → Project Settings → General → Your apps → Web app
- Or Authentication → Sign-in method → Google → Web client ID

## 📱 **Test on Mobile**

For mobile testing, you'll also need to:
1. Add SHA-1 fingerprint to Firebase
2. Download updated `google-services.json`
3. Rebuild the app

The current fix is specifically for web testing.
