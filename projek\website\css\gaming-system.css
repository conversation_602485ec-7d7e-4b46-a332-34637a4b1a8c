/* Gaming System Styles */

:root {
  --primary-color: #ff9933;
  --secondary-color: #138808;
  --accent-color: #146eb4;
  --dark-gray: #2c3e50;
  --medium-gray: #666;
  --light-gray: #f8f9fa;
  --white: #ffffff;
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.05);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--dark-gray);
  background: var(--light-gray);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header */
.header {
  background: var(--white);
  box-shadow: 0 2px 10px var(--shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--dark-gray);
}

.logo img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
  color: var(--primary-color);
}

/* Gaming Hero */
.gaming-hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  margin-top: 70px;
}

.gaming-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 800;
}

.gaming-text {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gaming-hero p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.gaming-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
}

/* Spin Game Section */
.spin-game-section {
  padding: 5rem 0;
  background: var(--white);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.section-subtitle {
  text-align: center;
  color: var(--medium-gray);
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

.game-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Spin Wheel */
.wheel-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wheel {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  position: relative;
  border: 8px solid var(--primary-color);
  overflow: hidden;
  transition: transform 3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.wheel-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  color: var(--white);
}

.wheel-segment:nth-child(1) { background: #e74c3c; transform: rotate(0deg) skewY(-45deg); }
.wheel-segment:nth-child(2) { background: #f39c12; transform: rotate(45deg) skewY(-45deg); }
.wheel-segment:nth-child(3) { background: #27ae60; transform: rotate(90deg) skewY(-45deg); }
.wheel-segment:nth-child(4) { background: #3498db; transform: rotate(135deg) skewY(-45deg); }
.wheel-segment:nth-child(5) { background: #9b59b6; transform: rotate(180deg) skewY(-45deg); }
.wheel-segment:nth-child(6) { background: #e67e22; transform: rotate(225deg) skewY(-45deg); }
.wheel-segment:nth-child(7) { background: #95a5a6; transform: rotate(270deg) skewY(-45deg); }
.wheel-segment:nth-child(8) { background: #2c3e50; transform: rotate(315deg) skewY(-45deg); }

.wheel-pointer {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid var(--danger);
  z-index: 10;
}

/* Game Controls */
.game-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.spin-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.spin-option {
  background: var(--light-gray);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.spin-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-5px);
}

.spin-option h3 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.spin-option p {
  margin-bottom: 0.5rem;
  color: var(--medium-gray);
}

.multiplier {
  color: var(--success);
  font-weight: 600;
}

.spin-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.spin-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

/* Gaming Balance */
.gaming-balance {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow);
}

.gaming-balance h4 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.balance-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--success);
  margin-bottom: 1rem;
}

.add-money-btn {
  background: var(--accent-color);
  color: var(--white);
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-money-btn:hover {
  background: #0d5aa7;
  transform: translateY(-2px);
}

/* Gaming Features */
.gaming-features {
  padding: 5rem 0;
  background: var(--light-gray);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.feature-card p {
  color: var(--medium-gray);
  line-height: 1.6;
}

/* Game History */
.game-history {
  padding: 5rem 0;
  background: var(--white);
}

.history-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.stats-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.summary-card {
  background: var(--light-gray);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
}

.summary-card h4 {
  margin-bottom: 1rem;
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.summary-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-gray);
}

.recent-games {
  background: var(--light-gray);
  padding: 2rem;
  border-radius: 15px;
}

.recent-games h3 {
  margin-bottom: 1.5rem;
  color: var(--dark-gray);
}

.game-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.game-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--white);
  border-radius: 10px;
  border-left: 4px solid var(--medium-gray);
}

.game-item.win {
  border-left-color: var(--success);
}

.game-item.loss {
  border-left-color: var(--danger);
}

.game-time {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.game-type {
  font-weight: 600;
  color: var(--dark-gray);
}

.game-result {
  font-weight: 700;
}

.game-item.win .game-result {
  color: var(--success);
}

.game-item.loss .game-result {
  color: var(--danger);
}

/* Wallet Integration */
.wallet-integration {
  padding: 5rem 0;
  background: var(--light-gray);
}

.wallet-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.wallet-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow);
}

.wallet-card h3 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.wallet-card p {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
}

.balance-display {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--success);
}

.transfer-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.transfer-btn {
  background: var(--accent-color);
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.transfer-btn:hover {
  background: #0d5aa7;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: var(--dark-gray);
  color: var(--white);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo-img {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.footer-links h4 {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.footer-links a {
  display: block;
  color: var(--white);
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--medium-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
  .gaming-hero h1 {
    font-size: 2rem;
  }

  .game-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .wheel {
    width: 250px;
    height: 250px;
  }

  .spin-options {
    grid-template-columns: 1fr;
  }

  .gaming-stats {
    grid-template-columns: 1fr;
  }

  .history-container {
    grid-template-columns: 1fr;
  }

  .stats-summary {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .transfer-options {
    flex-direction: column;
    align-items: center;
  }
}
