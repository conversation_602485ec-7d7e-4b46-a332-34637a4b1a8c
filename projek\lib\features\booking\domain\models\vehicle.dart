import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// part 'vehicle.g.dart'; // Temporarily commented for compilation

@HiveType(typeId: 20)
enum VehicleType {
  @HiveField(0)
  bike,
  @HiveField(1)
  car,
  @HiveField(2)
  scooter,
  @HiveField(3)
  bicycle,
}

@HiveType(typeId: 21)
enum VehicleStatus {
  @HiveField(0)
  available,
  @HiveField(1)
  booked,
  @HiveField(2)
  maintenance,
  @HiveField(3)
  outOfService,
}

@HiveType(typeId: 22)
enum FuelType {
  @HiveField(0)
  petrol,
  @HiveField(1)
  electric,
  @HiveField(2)
  diesel,
  @HiveField(3)
  hybrid,
}

@HiveType(typeId: 23)
@JsonSerializable()
class Vehicle extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String model;

  @HiveField(3)
  final String brand;

  @HiveField(4)
  final VehicleType type;

  @HiveField(5)
  final FuelType fuelType;

  @HiveField(6)
  final double pricePerHour;

  @HiveField(7)
  final double pricePerDay;

  @HiveField(8)
  final double latitude;

  @HiveField(9)
  final double longitude;

  @HiveField(10)
  final VehicleStatus status;

  @HiveField(11)
  final String imageUrl;

  @HiveField(12)
  final double rating;

  @HiveField(13)
  final int reviewCount;

  @HiveField(14)
  final String licensePlate;

  @HiveField(15)
  final int year;

  @HiveField(16)
  final String color;

  @HiveField(17)
  final int seatingCapacity;

  @HiveField(18)
  final double fuelLevel; // 0.0 to 1.0

  @HiveField(19)
  final String ownerId;

  @HiveField(20)
  final String ownerName;

  @HiveField(21)
  final String ownerPhone;

  @HiveField(22)
  final List<String> features;

  @HiveField(23)
  final DateTime createdAt;

  @HiveField(24)
  final DateTime updatedAt;

  @HiveField(25)
  final double? distanceFromUser; // in kilometers

  @HiveField(26)
  final bool isVerified;

  @HiveField(27)
  final double securityDeposit;

  const Vehicle({
    required this.id,
    required this.name,
    required this.model,
    required this.brand,
    required this.type,
    required this.fuelType,
    required this.pricePerHour,
    required this.pricePerDay,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.licensePlate,
    required this.year,
    required this.color,
    required this.seatingCapacity,
    required this.fuelLevel,
    required this.ownerId,
    required this.ownerName,
    required this.ownerPhone,
    required this.features,
    required this.createdAt,
    required this.updatedAt,
    this.distanceFromUser,
    this.isVerified = false,
    this.securityDeposit = 0.0,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'] as String,
      name: json['name'] as String,
      model: json['model'] as String,
      brand: json['brand'] as String,
      type: VehicleType.values[json['type'] as int],
      fuelType: FuelType.values[json['fuelType'] as int],
      pricePerHour: (json['pricePerHour'] as num).toDouble(),
      pricePerDay: (json['pricePerDay'] as num).toDouble(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      status: VehicleStatus.values[json['status'] as int],
      imageUrl: json['imageUrl'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      licensePlate: json['licensePlate'] as String,
      year: json['year'] as int,
      color: json['color'] as String,
      seatingCapacity: json['seatingCapacity'] as int,
      fuelLevel: (json['fuelLevel'] as num).toDouble(),
      ownerId: json['ownerId'] as String,
      ownerName: json['ownerName'] as String,
      ownerPhone: json['ownerPhone'] as String,
      features: List<String>.from(json['features'] as List),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      distanceFromUser: json['distanceFromUser'] != null
          ? (json['distanceFromUser'] as num).toDouble()
          : null,
      isVerified: json['isVerified'] as bool? ?? false,
      securityDeposit: (json['securityDeposit'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'model': model,
      'brand': brand,
      'type': type.index,
      'fuelType': fuelType.index,
      'pricePerHour': pricePerHour,
      'pricePerDay': pricePerDay,
      'latitude': latitude,
      'longitude': longitude,
      'status': status.index,
      'imageUrl': imageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'licensePlate': licensePlate,
      'year': year,
      'color': color,
      'seatingCapacity': seatingCapacity,
      'fuelLevel': fuelLevel,
      'ownerId': ownerId,
      'ownerName': ownerName,
      'ownerPhone': ownerPhone,
      'features': features,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'distanceFromUser': distanceFromUser,
      'isVerified': isVerified,
      'securityDeposit': securityDeposit,
    };
  }

  LatLng get location => LatLng(latitude, longitude);

  String get formattedPricePerHour => '₹${pricePerHour.toStringAsFixed(0)}/hr';
  String get formattedPricePerDay => '₹${pricePerDay.toStringAsFixed(0)}/day';
  String get formattedSecurityDeposit =>
      '₹${securityDeposit.toStringAsFixed(0)}';

  String get vehicleTypeDisplayName {
    switch (type) {
      case VehicleType.bike:
        return 'Bike';
      case VehicleType.car:
        return 'Car';
      case VehicleType.scooter:
        return 'Scooter';
      case VehicleType.bicycle:
        return 'Bicycle';
    }
  }

  String get fuelTypeDisplayName {
    switch (fuelType) {
      case FuelType.petrol:
        return 'Petrol';
      case FuelType.electric:
        return 'Electric';
      case FuelType.diesel:
        return 'Diesel';
      case FuelType.hybrid:
        return 'Hybrid';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case VehicleStatus.available:
        return 'Available';
      case VehicleStatus.booked:
        return 'Booked';
      case VehicleStatus.maintenance:
        return 'Under Maintenance';
      case VehicleStatus.outOfService:
        return 'Out of Service';
    }
  }

  String get fuelLevelPercentage => '${(fuelLevel * 100).toInt()}%';

  String get distanceText {
    if (distanceFromUser == null) return '';
    if (distanceFromUser! < 1) {
      return '${(distanceFromUser! * 1000).toInt()}m away';
    }
    return '${distanceFromUser!.toStringAsFixed(1)}km away';
  }

  bool get isAvailable => status == VehicleStatus.available;

  Vehicle copyWith({
    String? id,
    String? name,
    String? model,
    String? brand,
    VehicleType? type,
    FuelType? fuelType,
    double? pricePerHour,
    double? pricePerDay,
    double? latitude,
    double? longitude,
    VehicleStatus? status,
    String? imageUrl,
    double? rating,
    int? reviewCount,
    String? licensePlate,
    int? year,
    String? color,
    int? seatingCapacity,
    double? fuelLevel,
    String? ownerId,
    String? ownerName,
    String? ownerPhone,
    List<String>? features,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? distanceFromUser,
    bool? isVerified,
    double? securityDeposit,
  }) {
    return Vehicle(
      id: id ?? this.id,
      name: name ?? this.name,
      model: model ?? this.model,
      brand: brand ?? this.brand,
      type: type ?? this.type,
      fuelType: fuelType ?? this.fuelType,
      pricePerHour: pricePerHour ?? this.pricePerHour,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      status: status ?? this.status,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      licensePlate: licensePlate ?? this.licensePlate,
      year: year ?? this.year,
      color: color ?? this.color,
      seatingCapacity: seatingCapacity ?? this.seatingCapacity,
      fuelLevel: fuelLevel ?? this.fuelLevel,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      ownerPhone: ownerPhone ?? this.ownerPhone,
      features: features ?? this.features,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      distanceFromUser: distanceFromUser ?? this.distanceFromUser,
      isVerified: isVerified ?? this.isVerified,
      securityDeposit: securityDeposit ?? this.securityDeposit,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    model,
    brand,
    type,
    fuelType,
    pricePerHour,
    pricePerDay,
    latitude,
    longitude,
    status,
    imageUrl,
    rating,
    reviewCount,
    licensePlate,
    year,
    color,
    seatingCapacity,
    fuelLevel,
    ownerId,
    ownerName,
    ownerPhone,
    features,
    createdAt,
    updatedAt,
    distanceFromUser,
    isVerified,
    securityDeposit,
  ];
}

// Sample vehicles for testing
class SampleVehicles {
  static List<Vehicle> get vehicles => [
    // Bikes
    Vehicle(
      id: 'bike_1',
      name: 'Royal Enfield Classic 350',
      model: 'Classic 350',
      brand: 'Royal Enfield',
      type: VehicleType.bike,
      fuelType: FuelType.petrol,
      pricePerHour: 80.0,
      pricePerDay: 800.0,
      latitude: 28.6139,
      longitude: 77.2090,
      status: VehicleStatus.available,
      imageUrl: 'assets/images/vehicles/royal_enfield_classic.jpg',
      rating: 4.5,
      reviewCount: 45,
      licensePlate: 'DL 01 AB 1234',
      year: 2022,
      color: 'Black',
      seatingCapacity: 2,
      fuelLevel: 0.8,
      ownerId: 'owner_1',
      ownerName: 'Rajesh Kumar',
      ownerPhone: '+91 **********',
      features: ['Bluetooth', 'USB Charging', 'LED Lights'],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      distanceFromUser: 0.5,
      isVerified: true,
      securityDeposit: 2000.0,
    ),

    Vehicle(
      id: 'bike_2',
      name: 'Honda Activa 6G',
      model: 'Activa 6G',
      brand: 'Honda',
      type: VehicleType.scooter,
      fuelType: FuelType.petrol,
      pricePerHour: 50.0,
      pricePerDay: 500.0,
      latitude: 28.6129,
      longitude: 77.2295,
      status: VehicleStatus.available,
      imageUrl: 'assets/images/vehicles/honda_activa.jpg',
      rating: 4.2,
      reviewCount: 32,
      licensePlate: 'DL 02 CD 5678',
      year: 2023,
      color: 'Red',
      seatingCapacity: 2,
      fuelLevel: 0.9,
      ownerId: 'owner_2',
      ownerName: 'Priya Sharma',
      ownerPhone: '+91 **********',
      features: ['Under Seat Storage', 'Mobile Charging'],
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now(),
      distanceFromUser: 1.2,
      isVerified: true,
      securityDeposit: 1500.0,
    ),

    // Cars
    Vehicle(
      id: 'car_1',
      name: 'Maruti Swift',
      model: 'Swift VXI',
      brand: 'Maruti Suzuki',
      type: VehicleType.car,
      fuelType: FuelType.petrol,
      pricePerHour: 150.0,
      pricePerDay: 1500.0,
      latitude: 28.6169,
      longitude: 77.2090,
      status: VehicleStatus.available,
      imageUrl: 'assets/images/vehicles/maruti_swift.jpg',
      rating: 4.3,
      reviewCount: 67,
      licensePlate: 'DL 03 EF 9012',
      year: 2021,
      color: 'White',
      seatingCapacity: 5,
      fuelLevel: 0.7,
      ownerId: 'owner_3',
      ownerName: 'Amit Singh',
      ownerPhone: '+91 **********',
      features: ['AC', 'Music System', 'Power Steering'],
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
      distanceFromUser: 0.8,
      isVerified: true,
      securityDeposit: 5000.0,
    ),

    Vehicle(
      id: 'car_2',
      name: 'Hyundai i20',
      model: 'i20 Sportz',
      brand: 'Hyundai',
      type: VehicleType.car,
      fuelType: FuelType.petrol,
      pricePerHour: 180.0,
      pricePerDay: 1800.0,
      latitude: 28.6189,
      longitude: 77.2190,
      status: VehicleStatus.available,
      imageUrl: 'assets/images/vehicles/hyundai_i20.jpg',
      rating: 4.6,
      reviewCount: 89,
      licensePlate: 'DL 04 GH 3456',
      year: 2022,
      color: 'Blue',
      seatingCapacity: 5,
      fuelLevel: 0.85,
      ownerId: 'owner_4',
      ownerName: 'Neha Gupta',
      ownerPhone: '+91 **********',
      features: ['Touchscreen', 'Reverse Camera', 'Alloy Wheels'],
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
      distanceFromUser: 1.5,
      isVerified: true,
      securityDeposit: 6000.0,
    ),

    // Electric Vehicles
    Vehicle(
      id: 'bike_3',
      name: 'Ather 450X',
      model: '450X',
      brand: 'Ather',
      type: VehicleType.scooter,
      fuelType: FuelType.electric,
      pricePerHour: 60.0,
      pricePerDay: 600.0,
      latitude: 28.6149,
      longitude: 77.2390,
      status: VehicleStatus.available,
      imageUrl: 'assets/images/vehicles/ather_450x.jpg',
      rating: 4.7,
      reviewCount: 23,
      licensePlate: 'DL 05 IJ 7890',
      year: 2023,
      color: 'Green',
      seatingCapacity: 2,
      fuelLevel: 0.95, // Battery level
      ownerId: 'owner_5',
      ownerName: 'Rohit Verma',
      ownerPhone: '+91 **********',
      features: ['Digital Dashboard', 'Navigation', 'Fast Charging'],
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now(),
      distanceFromUser: 2.1,
      isVerified: true,
      securityDeposit: 3000.0,
    ),
  ];

  static List<Vehicle> getBikesByType(VehicleType type) {
    return vehicles.where((vehicle) => vehicle.type == type).toList();
  }

  static List<Vehicle> getAvailableVehicles() {
    return vehicles.where((vehicle) => vehicle.isAvailable).toList();
  }

  static List<Vehicle> getNearbyVehicles(double maxDistance) {
    return vehicles
        .where(
          (vehicle) =>
              vehicle.distanceFromUser != null &&
              vehicle.distanceFromUser! <= maxDistance,
        )
        .toList();
  }
}
