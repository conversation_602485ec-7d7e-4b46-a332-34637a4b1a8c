@echo off
echo ========================================
echo FIXING ALL IMPORT ERRORS - COMPREHENSIVE
echo ========================================

echo.
echo Step 1: Running Flutter clean...
flutter clean

echo Step 2: Getting dependencies...
flutter pub get

echo Step 3: Running dart fix to auto-fix imports...
dart fix --apply

echo Step 4: Analyzing code for remaining issues...
flutter analyze

echo Step 5: Testing build with main.dart...
flutter build apk --debug -t lib/main.dart

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SUCCESS! All import errors fixed and app builds correctly.
    echo Your main.dart is now working properly.
) else (
    echo.
    echo ❌ There are still some issues. Let's try alternative approaches...
    echo.
    echo Testing with main_user.dart...
    flutter build apk --debug --flavor user -t lib/main_user.dart
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ main_user.dart builds successfully!
        echo Consider using main_user.dart as your primary entry point.
    ) else (
        echo ❌ Both main files have issues. Check the output above.
    )
)

echo.
echo ========================================
echo IMPORT FIX COMPLETED
echo ========================================

pause
