import 'package:flutter/material.dart';
import '../../../../core/models/chat_models.dart';

class NewChatPage extends StatelessWidget {
  final ChatType? initialChatType;
  const NewChatPage({super.key, this.initialChatType});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Start New Chat')),
      body: Center(
        child: Text(
          'New chat page (initial type:  ${initialChatType?.toString() ?? 'none'})',
        ),
      ),
    );
  }
}
