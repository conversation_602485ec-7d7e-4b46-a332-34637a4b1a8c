@echo off
echo ========================================
echo Google Sign-In SHA-1 Certificate Fix
echo ========================================
echo.

echo Step 1: Getting debug SHA-1 certificate...
echo.

cd android
echo Running gradlew signingReport...
call gradlew signingReport

echo.
echo ========================================
echo IMPORTANT INSTRUCTIONS:
echo ========================================
echo.
echo 1. Copy the SHA-1 certificate from the output above
echo 2. Go to Firebase Console: https://console.firebase.google.com/
echo 3. Select your project: projek-7a8f8
echo 4. Go to Project Settings (gear icon)
echo 5. Select "Your apps" tab
echo 6. Find your Android app and click the settings icon
echo 7. Add the SHA-1 certificate fingerprint
echo 8. Download the updated google-services.json
echo 9. Replace the existing google-services.json in android/app/
echo.
echo Current Google Services Configuration:
echo Project ID: projek-7a8f8
echo Package Name: com.projek.app.projek
echo.
echo After updating Firebase configuration, run:
echo flutter clean
echo flutter pub get
echo flutter run
echo.
pause
