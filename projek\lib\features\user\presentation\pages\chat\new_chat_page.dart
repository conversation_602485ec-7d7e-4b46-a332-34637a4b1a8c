import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/chat_service.dart';
import '../../../../../core/models/chat_models.dart';
import '../../../../../core/services/analytics_service.dart';
import 'chat_detail_page.dart';

class NewChatPage extends ConsumerStatefulWidget {
  final ChatType? initialChatType;

  const NewChatPage({super.key, this.initialChatType});

  @override
  ConsumerState<NewChatPage> createState() => _NewChatPageState();
}

class _NewChatPageState extends ConsumerState<NewChatPage> {
  ChatType selectedChatType = ChatType.support;
  final TextEditingController _orderIdController = TextEditingController();
  final TextEditingController _productIdController = TextEditingController();
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialChatType != null) {
      selectedChatType = widget.initialChatType!;
    }
    AnalyticsService.logEvent('new_chat_page_opened', {
      'initial_chat_type': widget.initialChatType?.toString(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('💬 Start New Chat'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Icon(
                      Icons.chat_bubble_outline,
                      size: 48,
                      color: AppColors.userPrimary,
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Start a New Conversation',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Choose who you want to chat with and get started',
                      style: TextStyle(color: Colors.grey[600]),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Chat Type Selection
            _buildSectionTitle('Who do you want to chat with?'),
            const SizedBox(height: 12),
            _buildChatTypeOptions(),
            const SizedBox(height: 24),

            // Additional Information
            if (_needsAdditionalInfo()) ...[
              _buildSectionTitle('Additional Information'),
              const SizedBox(height: 12),
              _buildAdditionalInfoFields(),
              const SizedBox(height: 24),
            ],

            // Quick Start Options
            _buildQuickStartOptions(),
            const SizedBox(height: 24),

            // Create Chat Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCreating ? null : _createChat,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.userPrimary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isCreating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('Creating Chat...'),
                        ],
                      )
                    : Text(
                        'Start Chat with ${_getChatTypeDisplayName(selectedChatType)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildChatTypeOptions() {
    final chatTypes = [
      ChatTypeOption(
        type: ChatType.support,
        title: '🆘 Support Team',
        subtitle: 'Get help with your account, orders, or technical issues',
        color: AppColors.error,
      ),
      ChatTypeOption(
        type: ChatType.userSeller,
        title: '🏪 Seller',
        subtitle: 'Ask about products, pricing, or store policies',
        color: AppColors.accentGreen,
      ),
      ChatTypeOption(
        type: ChatType.userRider,
        title: '🚗 Delivery Rider',
        subtitle: 'Coordinate delivery details and special instructions',
        color: AppColors.info,
      ),
    ];

    return Column(
      children: chatTypes.map((option) => _buildChatTypeCard(option)).toList(),
    );
  }

  Widget _buildChatTypeCard(ChatTypeOption option) {
    final isSelected = selectedChatType == option.type;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () {
          setState(() {
            selectedChatType = option.type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: option.color, width: 2)
                : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: option.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getChatTypeIcon(option.type),
                  color: option.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? option.color : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      option.subtitle,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
              if (isSelected) Icon(Icons.check_circle, color: option.color),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoFields() {
    switch (selectedChatType) {
      case ChatType.userSeller:
        return Column(
          children: [
            TextField(
              controller: _productIdController,
              decoration: const InputDecoration(
                labelText: 'Product ID (Optional)',
                hintText: 'Enter product ID if asking about a specific product',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.inventory),
              ),
            ),
          ],
        );
      case ChatType.userRider:
        return Column(
          children: [
            TextField(
              controller: _orderIdController,
              decoration: const InputDecoration(
                labelText: 'Order ID (Optional)',
                hintText: 'Enter order ID for delivery coordination',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.receipt),
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildQuickStartOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Quick Start'),
        const SizedBox(height: 12),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.help_outline, color: AppColors.error),
                title: const Text('I need help with my order'),
                subtitle: const Text('Get support for order-related issues'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () => _quickStartChat(
                  ChatType.support,
                  'I need help with my order',
                ),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.store, color: AppColors.accentGreen),
                title: const Text('Ask about a product'),
                subtitle: const Text('Get product details from sellers'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () => _quickStartChat(
                  ChatType.userSeller,
                  'I have a question about your product',
                ),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(
                  Icons.delivery_dining,
                  color: AppColors.info,
                ),
                title: const Text('Contact my rider'),
                subtitle: const Text('Coordinate delivery details'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () => _quickStartChat(
                  ChatType.userRider,
                  'Hi, I wanted to discuss my delivery',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _needsAdditionalInfo() {
    return selectedChatType == ChatType.userSeller ||
        selectedChatType == ChatType.userRider;
  }

  String _getChatTypeDisplayName(ChatType type) {
    switch (type) {
      case ChatType.support:
        return 'Support';
      case ChatType.userSeller:
        return 'Seller';
      case ChatType.userRider:
        return 'Rider';
      default:
        return 'Chat';
    }
  }

  IconData _getChatTypeIcon(ChatType type) {
    switch (type) {
      case ChatType.support:
        return Icons.support_agent;
      case ChatType.userSeller:
        return Icons.store;
      case ChatType.userRider:
        return Icons.delivery_dining;
      default:
        return Icons.chat;
    }
  }

  void _createChat() async {
    setState(() {
      _isCreating = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      String otherUserId;
      switch (selectedChatType) {
        case ChatType.support:
          otherUserId = 'support_agent_1'; // Default support agent
          break;
        case ChatType.userSeller:
          otherUserId = 'seller_demo_1'; // Demo seller
          break;
        case ChatType.userRider:
          otherUserId = 'rider_demo_1'; // Demo rider
          break;
        default:
          throw Exception('Unsupported chat type');
      }

      final chatId = await ChatService.getOrCreateChat(
        otherUserId: otherUserId,
        chatType: selectedChatType,
        orderId: _orderIdController.text.trim().isEmpty
            ? null
            : _orderIdController.text.trim(),
        productId: _productIdController.text.trim().isEmpty
            ? null
            : _productIdController.text.trim(),
      );

      final chat = Chat(
        id: chatId,
        participants: [currentUser.uid, otherUserId],
        chatType: selectedChatType,
        orderId: _orderIdController.text.trim().isEmpty
            ? null
            : _orderIdController.text.trim(),
        productId: _productIdController.text.trim().isEmpty
            ? null
            : _productIdController.text.trim(),
        metadata: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastMessage: '',
        lastMessageSender: '',
        isActive: true,
        unreadCounts: {},
      );

      // Navigate to chat
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => ChatDetailPage(chat: chat)),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to create chat: $e')));
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }

  void _quickStartChat(ChatType chatType, String initialMessage) async {
    setState(() {
      selectedChatType = chatType;
      _isCreating = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      String otherUserId;
      switch (chatType) {
        case ChatType.support:
          otherUserId = 'support_agent_1';
          break;
        case ChatType.userSeller:
          otherUserId = 'seller_demo_1';
          break;
        case ChatType.userRider:
          otherUserId = 'rider_demo_1';
          break;
        default:
          throw Exception('Unsupported chat type');
      }

      final chatId = await ChatService.getOrCreateChat(
        otherUserId: otherUserId,
        chatType: chatType,
      );

      // Send initial message
      await ChatService.sendMessage(
        chatId: chatId,
        message: initialMessage,
        messageType: MessageType.text,
      );

      final chat = Chat(
        id: chatId,
        participants: [currentUser.uid, otherUserId],
        chatType: chatType,
        metadata: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastMessage: initialMessage,
        lastMessageSender: currentUser.uid,
        isActive: true,
        unreadCounts: {},
      );

      // Navigate to chat
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => ChatDetailPage(chat: chat)),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to start chat: $e')));
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }
}

class ChatTypeOption {
  final ChatType type;
  final String title;
  final String subtitle;
  final Color color;

  ChatTypeOption({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.color,
  });
}
