import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/utils/app_router.dart';
import '../../features/cart/presentation/providers/cart_provider.dart';
import '../../features/wishlist/presentation/providers/wishlist_provider.dart';
import '../../features/wallet/presentation/providers/wallet_provider.dart';

class MainWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const MainWrapper({super.key, required this.child});

  @override
  ConsumerState<MainWrapper> createState() => _MainWrapperState();
}

class _MainWrapperState extends ConsumerState<MainWrapper> {
  int _selectedIndex = 0;

  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      route: AppRoutes.home,
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Home',
    ),
    NavigationItem(
      route: AppRoutes.categories,
      icon: Icons.grid_view_outlined,
      selectedIcon: Icons.grid_view,
      label: 'Categories',
    ),
    NavigationItem(
      route: AppRoutes.cart,
      icon: Icons.shopping_cart_outlined,
      selectedIcon: Icons.shopping_cart,
      label: 'Cart',
    ),
    NavigationItem(
      route: AppRoutes.wishlist,
      icon: Icons.favorite_border,
      selectedIcon: Icons.favorite,
      label: 'Wishlist',
    ),
    NavigationItem(
      route: AppRoutes.wallet,
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet,
      label: 'Wallet',
    ),
    NavigationItem(
      route: AppRoutes.profile,
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final cartItemCount = ref.watch(cartItemCountProvider);
    final wishlistItemCount = ref.watch(wishlistItemCountProvider);
    final walletBalance = ref.watch(walletBalanceProvider);

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: NavigationBar(
        selectedIndex: _selectedIndex,
        onDestinationSelected: _onDestinationSelected,
        destinations: _navigationItems.asMap().entries.map((entry) {
          final item = entry.value;

          Widget icon = Icon(item.icon);
          Widget selectedIcon = Icon(item.selectedIcon);

          // Add badge for cart
          if (item.route == AppRoutes.cart && cartItemCount > 0) {
            icon = Badge(
              label: Text(cartItemCount.toString()),
              child: Icon(item.icon),
            );
            selectedIcon = Badge(
              label: Text(cartItemCount.toString()),
              child: Icon(item.selectedIcon),
            );
          }

          // Add badge for wishlist
          if (item.route == AppRoutes.wishlist && wishlistItemCount > 0) {
            icon = Badge(
              label: Text(wishlistItemCount.toString()),
              child: Icon(item.icon),
            );
            selectedIcon = Badge(
              label: Text(wishlistItemCount.toString()),
              child: Icon(item.selectedIcon),
            );
          }

          // Add balance badge for wallet
          if (item.route == AppRoutes.wallet && walletBalance > 0) {
            final balanceText = walletBalance >= 1000
                ? '${(walletBalance / 1000).toStringAsFixed(1)}K'
                : walletBalance.toStringAsFixed(0);
            icon = Badge(
              label: Text(balanceText),
              backgroundColor: Colors.green,
              child: Icon(item.icon),
            );
            selectedIcon = Badge(
              label: Text(balanceText),
              backgroundColor: Colors.green,
              child: Icon(item.selectedIcon),
            );
          }

          return NavigationDestination(
            icon: icon,
            selectedIcon: selectedIcon,
            label: item.label,
          );
        }).toList(),
      ),
    );
  }

  void _onDestinationSelected(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final String location = GoRouterState.of(context).uri.path;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        if (_selectedIndex != i) {
          setState(() {
            _selectedIndex = i;
          });
        }
        break;
      }
    }
  }
}

class NavigationItem {
  final String route;
  final IconData icon;
  final IconData selectedIcon;
  final String label;

  const NavigationItem({
    required this.route,
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}
