// Rider Registration JavaScript
document.addEventListener('DOMContentLoaded', function() {
  
  // Form step management
  let currentStep = 1;
  const totalSteps = 4;
  
  const formSteps = document.querySelectorAll('.form-step');
  const progressSteps = document.querySelectorAll('.progress-step');
  const nextBtn = document.getElementById('nextStep');
  const prevBtn = document.getElementById('prevStep');
  const startRegistrationBtn = document.getElementById('startRegistration');
  
  // Start registration button
  if (startRegistrationBtn) {
    startRegistrationBtn.addEventListener('click', function() {
      document.getElementById('registrationForm').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    });
  }
  
  // Form navigation
  if (nextBtn) {
    nextBtn.addEventListener('click', function() {
      if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
          currentStep++;
          updateFormStep();
        } else {
          submitForm();
        }
      }
    });
  }
  
  if (prevBtn) {
    prevBtn.addEventListener('click', function() {
      if (currentStep > 1) {
        currentStep--;
        updateFormStep();
      }
    });
  }
  
  function updateFormStep() {
    // Update form steps visibility
    formSteps.forEach((step, index) => {
      step.classList.toggle('active', index + 1 === currentStep);
    });
    
    // Update progress steps
    progressSteps.forEach((step, index) => {
      step.classList.toggle('active', index + 1 <= currentStep);
    });
    
    // Update navigation buttons
    prevBtn.style.display = currentStep === 1 ? 'none' : 'flex';
    
    if (currentStep === totalSteps) {
      nextBtn.innerHTML = '<span>Submit Application</span><span class="material-icons">send</span>';
    } else {
      nextBtn.innerHTML = '<span>Next</span><span class="material-icons">arrow_forward</span>';
    }
    
    // Add step completion animation
    const currentProgressStep = document.querySelector(`[data-step="${currentStep}"]`);
    if (currentProgressStep) {
      currentProgressStep.style.transform = 'scale(1.1)';
      setTimeout(() => {
        currentProgressStep.style.transform = '';
      }, 300);
    }
  }
  
  function validateCurrentStep() {
    const currentFormStep = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    if (!currentFormStep) return true;
    
    const requiredFields = currentFormStep.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        showFieldError(field, 'This field is required');
        isValid = false;
      } else {
        clearFieldError(field);
        
        // Additional validation based on field type
        if (field.type === 'email' && !isValidEmail(field.value)) {
          showFieldError(field, 'Please enter a valid email address');
          isValid = false;
        }
        
        if (field.type === 'tel' && !isValidPhone(field.value)) {
          showFieldError(field, 'Please enter a valid phone number');
          isValid = false;
        }
        
        if (field.name === 'dateOfBirth' && !isValidAge(field.value)) {
          showFieldError(field, 'You must be between 18-60 years old');
          isValid = false;
        }
        
        if (field.name === 'pincode' && !isValidPincode(field.value)) {
          showFieldError(field, 'Please enter a valid 6-digit pincode');
          isValid = false;
        }
        
        if (field.name === 'vehicleNumber' && !isValidVehicleNumber(field.value)) {
          showFieldError(field, 'Please enter a valid vehicle number (e.g., MH12AB1234)');
          isValid = false;
        }
      }
    });
    
    return isValid;
  }
  
  function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = '#dc3545';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '0.8rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
  }
  
  function clearFieldError(field) {
    field.style.borderColor = '';
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
  }
  
  // Validation helper functions
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  function isValidPhone(phone) {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  }
  
  function isValidAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age >= 18 && age <= 60;
  }
  
  function isValidPincode(pincode) {
    const pincodeRegex = /^[1-9][0-9]{5}$/;
    return pincodeRegex.test(pincode);
  }
  
  function isValidVehicleNumber(vehicleNumber) {
    const vehicleRegex = /^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{1,4}$/;
    return vehicleRegex.test(vehicleNumber.toUpperCase().replace(/\s/g, ''));
  }
  
  // Auto-format inputs
  const phoneInputs = document.querySelectorAll('input[type="tel"]');
  phoneInputs.forEach(input => {
    input.addEventListener('input', function() {
      let value = this.value.replace(/\D/g, '');
      if (value.length > 10) value = value.slice(0, 10);
      this.value = value;
    });
  });
  
  const vehicleNumberInput = document.getElementById('vehicleNumber');
  if (vehicleNumberInput) {
    vehicleNumberInput.addEventListener('input', function() {
      this.value = this.value.toUpperCase();
    });
  }
  
  const pincodeInput = document.getElementById('pincode');
  if (pincodeInput) {
    pincodeInput.addEventListener('input', function() {
      let value = this.value.replace(/\D/g, '');
      if (value.length > 6) value = value.slice(0, 6);
      this.value = value;
    });
  }
  
  // Form submission
  function submitForm() {
    const formData = new FormData(document.getElementById('riderRegistrationForm'));
    const data = Object.fromEntries(formData);
    
    // Show loading state
    nextBtn.innerHTML = '<span class="material-icons">hourglass_empty</span><span>Submitting...</span>';
    nextBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
      showSuccessMessage();
    }, 2000);
    
    console.log('Form Data:', data);
  }
  
  function showSuccessMessage() {
    const formContainer = document.querySelector('.form-container');
    formContainer.innerHTML = `
      <div style="text-align: center; padding: 4rem 2rem;">
        <div style="width: 100px; height: 100px; margin: 0 auto 2rem; background: var(--gradient-green); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <span class="material-icons" style="font-size: 3rem; color: white;">check_circle</span>
        </div>
        <h2 style="color: var(--green); margin-bottom: 1rem;">Application Submitted Successfully!</h2>
        <p style="color: var(--medium-gray); margin-bottom: 2rem; line-height: 1.6;">
          Thank you for applying to become a Super Vision rider. We have received your application and will review it within 24-48 hours.
        </p>
        <div style="background: var(--light-gray); border-radius: 12px; padding: 2rem; margin-bottom: 2rem;">
          <h3 style="color: var(--dark-gray); margin-bottom: 1rem;">What's Next?</h3>
          <ul style="text-align: left; color: var(--medium-gray); line-height: 1.8;">
            <li>📧 You'll receive a confirmation email shortly</li>
            <li>📞 Our team will call you within 24 hours</li>
            <li>📋 Document verification will be scheduled</li>
            <li>🎓 Training session will be arranged</li>
            <li>🚀 Start earning as a Super Vision rider!</li>
          </ul>
        </div>
        <div style="display: flex; gap: 1rem; justify-content: center;">
          <a href="public/index.html" class="btn btn-secondary">
            <span class="material-icons">home</span>
            <span>Back to Home</span>
          </a>
          <a href="booking.html" class="btn btn-primary">
            <span class="material-icons">shopping_cart</span>
            <span>Explore Services</span>
          </a>
        </div>
      </div>
    `;
  }
  
  // Add smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
  
  // Initialize fade-in animations
  const fadeElements = document.querySelectorAll('.fade-in');
  const fadeObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        fadeObserver.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });

  fadeElements.forEach(element => {
    fadeObserver.observe(element);
  });
  
  // File upload handling
  const uploadBoxes = document.querySelectorAll('.upload-box');
  uploadBoxes.forEach(box => {
    const input = box.querySelector('input[type="file"]');

    box.addEventListener('click', function() {
      input.click();
    });

    input.addEventListener('change', function() {
      if (this.files && this.files[0]) {
        const fileName = this.files[0].name;
        const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2); // MB

        // Update upload box appearance
        box.classList.add('uploaded');
        box.querySelector('.material-icons').textContent = 'check_circle';
        box.querySelector('span:last-child').textContent = `${fileName} (${fileSize} MB)`;

        // Add success animation
        box.style.transform = 'scale(1.05)';
        setTimeout(() => {
          box.style.transform = '';
        }, 200);
      }
    });
  });

  // Add floating animation to rider icon
  const riderIcon = document.querySelector('.rider-icon-large');
  if (riderIcon) {
    riderIcon.style.animation = 'float 4s ease-in-out infinite';
  }

  console.log('Rider registration page initialized successfully!');
});
