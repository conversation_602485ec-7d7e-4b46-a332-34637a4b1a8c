class Booking {
  final String id;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final String customerEmail;
  final String serviceId;
  final String serviceName;
  final String serviceType; // 'product', 'service', 'food', 'ride'
  final String providerId;
  final String providerName;
  final double totalAmount;
  final BookingStatus status;
  final DateTime bookingDate;
  final DateTime serviceDate;
  final String serviceTime;
  final String address;
  final String city;
  final String pincode;
  final double latitude;
  final double longitude;
  final String? specialInstructions;
  final String? cancellationReason;
  final List<BookingItem> items;
  final PaymentDetails? paymentDetails;
  final List<String> images;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final List<BookingStatusUpdate> statusUpdates;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Booking({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.customerEmail,
    required this.serviceId,
    required this.serviceName,
    required this.serviceType,
    required this.providerId,
    required this.providerName,
    required this.totalAmount,
    required this.status,
    required this.bookingDate,
    required this.serviceDate,
    required this.serviceTime,
    required this.address,
    required this.city,
    required this.pincode,
    required this.latitude,
    required this.longitude,
    this.specialInstructions,
    this.cancellationReason,
    this.items = const [],
    this.paymentDetails,
    this.images = const [],
    this.completedAt,
    this.cancelledAt,
    this.statusUpdates = const [],
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  Booking copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? serviceId,
    String? serviceName,
    String? serviceType,
    String? providerId,
    String? providerName,
    double? totalAmount,
    BookingStatus? status,
    DateTime? bookingDate,
    DateTime? serviceDate,
    String? serviceTime,
    String? address,
    String? city,
    String? pincode,
    double? latitude,
    double? longitude,
    String? specialInstructions,
    String? cancellationReason,
    List<BookingItem>? items,
    PaymentDetails? paymentDetails,
    List<String>? images,
    DateTime? completedAt,
    DateTime? cancelledAt,
    List<BookingStatusUpdate>? statusUpdates,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Booking(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerEmail: customerEmail ?? this.customerEmail,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceType: serviceType ?? this.serviceType,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      bookingDate: bookingDate ?? this.bookingDate,
      serviceDate: serviceDate ?? this.serviceDate,
      serviceTime: serviceTime ?? this.serviceTime,
      address: address ?? this.address,
      city: city ?? this.city,
      pincode: pincode ?? this.pincode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      items: items ?? this.items,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      images: images ?? this.images,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      statusUpdates: statusUpdates ?? this.statusUpdates,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      customerPhone: json['customerPhone'] as String,
      customerEmail: json['customerEmail'] as String,
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      serviceType: json['serviceType'] as String,
      providerId: json['providerId'] as String,
      providerName: json['providerName'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      status: BookingStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      serviceDate: DateTime.parse(json['serviceDate'] as String),
      serviceTime: json['serviceTime'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      pincode: json['pincode'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      specialInstructions: json['specialInstructions'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      items:
          (json['items'] as List<dynamic>?)
              ?.map((e) => BookingItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      paymentDetails: json['paymentDetails'] != null
          ? PaymentDetails.fromJson(
              json['paymentDetails'] as Map<String, dynamic>,
            )
          : null,
      images: (json['images'] as List<dynamic>?)?.cast<String>() ?? [],
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      cancelledAt: json['cancelledAt'] != null
          ? DateTime.parse(json['cancelledAt'] as String)
          : null,
      statusUpdates:
          (json['statusUpdates'] as List<dynamic>?)
              ?.map(
                (e) => BookingStatusUpdate.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'serviceId': serviceId,
      'serviceName': serviceName,
      'serviceType': serviceType,
      'providerId': providerId,
      'providerName': providerName,
      'totalAmount': totalAmount,
      'status': status.toString().split('.').last,
      'bookingDate': bookingDate.toIso8601String(),
      'serviceDate': serviceDate.toIso8601String(),
      'serviceTime': serviceTime,
      'address': address,
      'city': city,
      'pincode': pincode,
      'latitude': latitude,
      'longitude': longitude,
      'specialInstructions': specialInstructions,
      'cancellationReason': cancellationReason,
      'items': items.map((e) => e.toJson()).toList(),
      'paymentDetails': paymentDetails?.toJson(),
      'images': images,
      'completedAt': completedAt?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'statusUpdates': statusUpdates.map((e) => e.toJson()).toList(),
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class BookingItem {
  final String id;
  final String name;
  final String description;
  final double price;
  final int quantity;
  final String? image;
  final String? variant;
  final Map<String, dynamic> specifications;

  const BookingItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.quantity,
    this.image,
    this.variant,
    this.specifications = const {},
  });

  BookingItem copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    int? quantity,
    String? image,
    String? variant,
    Map<String, dynamic>? specifications,
  }) {
    return BookingItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      image: image ?? this.image,
      variant: variant ?? this.variant,
      specifications: specifications ?? this.specifications,
    );
  }

  factory BookingItem.fromJson(Map<String, dynamic> json) {
    return BookingItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: json['quantity'] as int,
      image: json['image'] as String?,
      variant: json['variant'] as String?,
      specifications: (json['specifications'] as Map<String, dynamic>?) ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'quantity': quantity,
      'image': image,
      'variant': variant,
      'specifications': specifications,
    };
  }
}

class PaymentDetails {
  final String paymentId;
  final String paymentMethod; // 'upi', 'card', 'cash', 'wallet'
  final PaymentStatus status;
  final double amount;
  final String? transactionId;
  final String? gatewayResponse;
  final DateTime? paidAt;
  final String? failureReason;

  const PaymentDetails({
    required this.paymentId,
    required this.paymentMethod,
    required this.status,
    required this.amount,
    this.transactionId,
    this.gatewayResponse,
    this.paidAt,
    this.failureReason,
  });

  PaymentDetails copyWith({
    String? paymentId,
    String? paymentMethod,
    PaymentStatus? status,
    double? amount,
    String? transactionId,
    String? gatewayResponse,
    DateTime? paidAt,
    String? failureReason,
  }) {
    return PaymentDetails(
      paymentId: paymentId ?? this.paymentId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      transactionId: transactionId ?? this.transactionId,
      gatewayResponse: gatewayResponse ?? this.gatewayResponse,
      paidAt: paidAt ?? this.paidAt,
      failureReason: failureReason ?? this.failureReason,
    );
  }

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      paymentId: json['paymentId'] as String,
      paymentMethod: json['paymentMethod'] as String,
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      amount: (json['amount'] as num).toDouble(),
      transactionId: json['transactionId'] as String?,
      gatewayResponse: json['gatewayResponse'] as String?,
      paidAt: json['paidAt'] != null
          ? DateTime.parse(json['paidAt'] as String)
          : null,
      failureReason: json['failureReason'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paymentId': paymentId,
      'paymentMethod': paymentMethod,
      'status': status.toString().split('.').last,
      'amount': amount,
      'transactionId': transactionId,
      'gatewayResponse': gatewayResponse,
      'paidAt': paidAt?.toIso8601String(),
      'failureReason': failureReason,
    };
  }
}

class BookingStatusUpdate {
  final BookingStatus status;
  final String message;
  final DateTime timestamp;
  final String? updatedBy;
  final String? location;
  final List<String>? images;

  const BookingStatusUpdate({
    required this.status,
    required this.message,
    required this.timestamp,
    this.updatedBy,
    this.location,
    this.images,
  });

  BookingStatusUpdate copyWith({
    BookingStatus? status,
    String? message,
    DateTime? timestamp,
    String? updatedBy,
    String? location,
    List<String>? images,
  }) {
    return BookingStatusUpdate(
      status: status ?? this.status,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      updatedBy: updatedBy ?? this.updatedBy,
      location: location ?? this.location,
      images: images ?? this.images,
    );
  }

  factory BookingStatusUpdate.fromJson(Map<String, dynamic> json) {
    return BookingStatusUpdate(
      status: BookingStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      updatedBy: json['updatedBy'] as String?,
      location: json['location'] as String?,
      images: (json['images'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.toString().split('.').last,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'updatedBy': updatedBy,
      'location': location,
      'images': images,
    };
  }
}

enum BookingStatus {
  pending,
  confirmed,
  assigned,
  inProgress,
  onTheWay,
  arrived,
  completed,
  cancelled,
  refunded,
}

enum PaymentStatus { pending, processing, completed, failed, refunded }

extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending Confirmation';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.assigned:
        return 'Service Provider Assigned';
      case BookingStatus.inProgress:
        return 'Service in Progress';
      case BookingStatus.onTheWay:
        return 'On the Way';
      case BookingStatus.arrived:
        return 'Service Provider Arrived';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.refunded:
        return 'Refunded';
    }
  }

  String get description {
    switch (this) {
      case BookingStatus.pending:
        return 'Waiting for confirmation from service provider';
      case BookingStatus.confirmed:
        return 'Your booking has been confirmed';
      case BookingStatus.assigned:
        return 'Service provider has been assigned to your booking';
      case BookingStatus.inProgress:
        return 'Service is currently being provided';
      case BookingStatus.onTheWay:
        return 'Service provider is on the way to your location';
      case BookingStatus.arrived:
        return 'Service provider has arrived at your location';
      case BookingStatus.completed:
        return 'Service has been completed successfully';
      case BookingStatus.cancelled:
        return 'Booking has been cancelled';
      case BookingStatus.refunded:
        return 'Payment has been refunded';
    }
  }

  bool get canCancel {
    return this == BookingStatus.pending ||
        this == BookingStatus.confirmed ||
        this == BookingStatus.assigned;
  }

  bool get canReschedule {
    return this == BookingStatus.pending || this == BookingStatus.confirmed;
  }

  bool get isActive {
    return this != BookingStatus.completed &&
        this != BookingStatus.cancelled &&
        this != BookingStatus.refunded;
  }
}

// Sample booking data for development
class SampleBookings {
  static List<Booking> get bookings => [
    Booking(
      id: 'booking_001',
      customerId: 'user_001',
      customerName: 'John Doe',
      customerPhone: '+91 **********',
      customerEmail: '<EMAIL>',
      serviceId: 'service_001',
      serviceName: 'Home Cleaning Service',
      serviceType: 'service',
      providerId: 'provider_001',
      providerName: 'CleanPro Services',
      totalAmount: 1500.0,
      status: BookingStatus.confirmed,
      bookingDate: DateTime.now().subtract(const Duration(hours: 2)),
      serviceDate: DateTime.now().add(const Duration(days: 1)),
      serviceTime: '10:00 AM - 12:00 PM',
      address: '123 Main Street, Apartment 4B',
      city: 'Mumbai',
      pincode: '400001',
      latitude: 19.0760,
      longitude: 72.8777,
      specialInstructions: 'Please bring eco-friendly cleaning products',
      items: [
        BookingItem(
          id: 'item_001',
          name: 'Deep Cleaning',
          description: 'Complete house deep cleaning service',
          price: 1200.0,
          quantity: 1,
          image: 'assets/images/services/cleaning.jpg',
        ),
        BookingItem(
          id: 'item_002',
          name: 'Kitchen Cleaning',
          description: 'Specialized kitchen cleaning',
          price: 300.0,
          quantity: 1,
          image: 'assets/images/services/kitchen_cleaning.jpg',
        ),
      ],
      paymentDetails: PaymentDetails(
        paymentId: 'pay_001',
        paymentMethod: 'upi',
        status: PaymentStatus.completed,
        amount: 1500.0,
        transactionId: 'TXN123456789',
        paidAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      images: [
        'assets/images/bookings/booking_001_1.jpg',
        'assets/images/bookings/booking_001_2.jpg',
      ],
      statusUpdates: [
        BookingStatusUpdate(
          status: BookingStatus.pending,
          message: 'Booking request submitted',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        BookingStatusUpdate(
          status: BookingStatus.confirmed,
          message: 'Booking confirmed by CleanPro Services',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          updatedBy: 'CleanPro Services',
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
    ),

    Booking(
      id: 'booking_002',
      customerId: 'user_001',
      customerName: 'John Doe',
      customerPhone: '+91 **********',
      customerEmail: '<EMAIL>',
      serviceId: 'service_002',
      serviceName: 'Plumbing Repair',
      serviceType: 'service',
      providerId: 'provider_002',
      providerName: 'FixIt Plumbers',
      totalAmount: 800.0,
      status: BookingStatus.inProgress,
      bookingDate: DateTime.now().subtract(const Duration(hours: 4)),
      serviceDate: DateTime.now(),
      serviceTime: '2:00 PM - 4:00 PM',
      address: '123 Main Street, Apartment 4B',
      city: 'Mumbai',
      pincode: '400001',
      latitude: 19.0760,
      longitude: 72.8777,
      specialInstructions: 'Kitchen sink is completely blocked',
      items: [
        BookingItem(
          id: 'item_003',
          name: 'Sink Repair',
          description: 'Kitchen sink blockage repair',
          price: 500.0,
          quantity: 1,
          image: 'assets/images/services/plumbing.jpg',
        ),
        BookingItem(
          id: 'item_004',
          name: 'Pipe Replacement',
          description: 'Replace damaged pipe section',
          price: 300.0,
          quantity: 1,
          image: 'assets/images/services/pipe_repair.jpg',
        ),
      ],
      paymentDetails: PaymentDetails(
        paymentId: 'pay_002',
        paymentMethod: 'cash',
        status: PaymentStatus.pending,
        amount: 800.0,
      ),
      images: [],
      statusUpdates: [
        BookingStatusUpdate(
          status: BookingStatus.pending,
          message: 'Booking request submitted',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
        BookingStatusUpdate(
          status: BookingStatus.confirmed,
          message: 'Booking confirmed by FixIt Plumbers',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          updatedBy: 'FixIt Plumbers',
        ),
        BookingStatusUpdate(
          status: BookingStatus.assigned,
          message: 'Technician Raj Kumar assigned to your booking',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
          updatedBy: 'FixIt Plumbers',
        ),
        BookingStatusUpdate(
          status: BookingStatus.onTheWay,
          message: 'Technician is on the way to your location',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
          updatedBy: 'Raj Kumar',
          location: 'Near Main Street Junction',
        ),
        BookingStatusUpdate(
          status: BookingStatus.arrived,
          message: 'Technician has arrived at your location',
          timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
          updatedBy: 'Raj Kumar',
        ),
        BookingStatusUpdate(
          status: BookingStatus.inProgress,
          message: 'Work has started on your plumbing repair',
          timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
          updatedBy: 'Raj Kumar',
        ),
      ],
      createdAt: DateTime.now().subtract(const Duration(hours: 4)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
    ),
  ];
}
