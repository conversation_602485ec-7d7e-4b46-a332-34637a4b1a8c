import 'dart:math';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

// part 'game_transaction.g.dart'; // Temporarily commented for compilation

@HiveType(typeId: 30)
enum GameType {
  @HiveField(0)
  spinWheel,
  @HiveField(1)
  staking,
  @HiveField(2)
  lottery,
  @HiveField(3)
  scratch,
}

@HiveType(typeId: 31)
enum SpinType {
  @HiveField(0)
  regular,
  @HiveField(1)
  max,
}

@HiveType(typeId: 32)
enum GameTransactionStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  processing,
  @HiveField(2)
  completed,
  @HiveField(3)
  failed,
  @HiveField(4)
  cancelled,
}

@HiveType(typeId: 33)
@JsonSerializable()
class GameTransaction extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final GameType gameType;

  @HiveField(3)
  final SpinType? spinType;

  @HiveField(4)
  final double entryAmount;

  @HiveField(5)
  final double winAmount;

  @HiveField(6)
  final double netAmount; // winAmount - entryAmount

  @HiveField(7)
  final GameTransactionStatus status;

  @HiveField(8)
  final String result; // e.g., "Won ₹250", "Better luck next time"

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final DateTime? completedAt;

  @HiveField(11)
  final Map<String, dynamic> metadata;

  @HiveField(12)
  final String? errorMessage;

  @HiveField(13)
  final int segmentIndex; // Which segment was hit on the wheel

  @HiveField(14)
  final double balanceBeforeGame;

  @HiveField(15)
  final double balanceAfterGame;

  const GameTransaction({
    required this.id,
    required this.userId,
    required this.gameType,
    this.spinType,
    required this.entryAmount,
    required this.winAmount,
    required this.netAmount,
    required this.status,
    required this.result,
    required this.createdAt,
    this.completedAt,
    this.metadata = const {},
    this.errorMessage,
    required this.segmentIndex,
    required this.balanceBeforeGame,
    required this.balanceAfterGame,
  });

  factory GameTransaction.create({
    required String userId,
    required GameType gameType,
    SpinType? spinType,
    required double entryAmount,
    required double winAmount,
    required String result,
    required int segmentIndex,
    required double balanceBeforeGame,
    Map<String, dynamic>? metadata,
  }) {
    final netAmount = winAmount - entryAmount;
    final balanceAfterGame = balanceBeforeGame + netAmount;

    return GameTransaction(
      id: const Uuid().v4(),
      userId: userId,
      gameType: gameType,
      spinType: spinType,
      entryAmount: entryAmount,
      winAmount: winAmount,
      netAmount: netAmount,
      status: GameTransactionStatus.pending,
      result: result,
      createdAt: DateTime.now(),
      metadata: metadata ?? {},
      segmentIndex: segmentIndex,
      balanceBeforeGame: balanceBeforeGame,
      balanceAfterGame: balanceAfterGame,
    );
  }

  factory GameTransaction.fromJson(Map<String, dynamic> json) {
    return GameTransaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      gameType: GameType.values[json['gameType'] as int],
      spinType: json['spinType'] != null
          ? SpinType.values[json['spinType'] as int]
          : null,
      entryAmount: (json['entryAmount'] as num).toDouble(),
      winAmount: (json['winAmount'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      status: GameTransactionStatus.values[json['status'] as int],
      result: json['result'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      errorMessage: json['errorMessage'] as String?,
      segmentIndex: json['segmentIndex'] as int,
      balanceBeforeGame: (json['balanceBeforeGame'] as num).toDouble(),
      balanceAfterGame: (json['balanceAfterGame'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'gameType': gameType.index,
      'spinType': spinType?.index,
      'entryAmount': entryAmount,
      'winAmount': winAmount,
      'netAmount': netAmount,
      'status': status.index,
      'result': result,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'metadata': metadata,
      'errorMessage': errorMessage,
      'segmentIndex': segmentIndex,
      'balanceBeforeGame': balanceBeforeGame,
      'balanceAfterGame': balanceAfterGame,
    };
  }

  bool get isWin => netAmount > 0;
  bool get isLoss => netAmount < 0;
  bool get isBreakEven => netAmount == 0;

  String get formattedEntryAmount => '₹${entryAmount.toStringAsFixed(0)}';
  String get formattedWinAmount => '₹${winAmount.toStringAsFixed(0)}';
  String get formattedNetAmount {
    final prefix = netAmount >= 0 ? '+' : '';
    return '$prefix₹${netAmount.toStringAsFixed(0)}';
  }

  String get gameTypeDisplayName {
    switch (gameType) {
      case GameType.spinWheel:
        return 'Spin Wheel';
      case GameType.staking:
        return 'Staking';
      case GameType.lottery:
        return 'Lottery';
      case GameType.scratch:
        return 'Scratch Card';
    }
  }

  String get spinTypeDisplayName {
    if (spinType == null) return '';
    switch (spinType!) {
      case SpinType.regular:
        return 'Regular Spin';
      case SpinType.max:
        return 'Max Spin';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case GameTransactionStatus.pending:
        return 'Pending';
      case GameTransactionStatus.processing:
        return 'Processing';
      case GameTransactionStatus.completed:
        return 'Completed';
      case GameTransactionStatus.failed:
        return 'Failed';
      case GameTransactionStatus.cancelled:
        return 'Cancelled';
    }
  }

  GameTransaction copyWith({
    String? id,
    String? userId,
    GameType? gameType,
    SpinType? spinType,
    double? entryAmount,
    double? winAmount,
    double? netAmount,
    GameTransactionStatus? status,
    String? result,
    DateTime? createdAt,
    DateTime? completedAt,
    Map<String, dynamic>? metadata,
    String? errorMessage,
    int? segmentIndex,
    double? balanceBeforeGame,
    double? balanceAfterGame,
  }) {
    return GameTransaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      gameType: gameType ?? this.gameType,
      spinType: spinType ?? this.spinType,
      entryAmount: entryAmount ?? this.entryAmount,
      winAmount: winAmount ?? this.winAmount,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      result: result ?? this.result,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      metadata: metadata ?? this.metadata,
      errorMessage: errorMessage ?? this.errorMessage,
      segmentIndex: segmentIndex ?? this.segmentIndex,
      balanceBeforeGame: balanceBeforeGame ?? this.balanceBeforeGame,
      balanceAfterGame: balanceAfterGame ?? this.balanceAfterGame,
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    gameType,
    spinType,
    entryAmount,
    winAmount,
    netAmount,
    status,
    result,
    createdAt,
    completedAt,
    metadata,
    errorMessage,
    segmentIndex,
    balanceBeforeGame,
    balanceAfterGame,
  ];
}

@HiveType(typeId: 34)
@JsonSerializable()
class SpinWheelSegment extends Equatable {
  @HiveField(0)
  final int amount;

  @HiveField(1)
  final double probability;

  @HiveField(2)
  final String color;

  @HiveField(3)
  final int index;

  const SpinWheelSegment({
    required this.amount,
    required this.probability,
    required this.color,
    required this.index,
  });

  factory SpinWheelSegment.fromJson(Map<String, dynamic> json) {
    return SpinWheelSegment(
      amount: json['amount'] as int,
      probability: (json['probability'] as num).toDouble(),
      color: json['color'] as String,
      index: json['index'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'probability': probability,
      'color': color,
      'index': index,
    };
  }

  String get formattedAmount => '₹$amount';

  @override
  List<Object?> get props => [amount, probability, color, index];
}

// Predefined wheel segments with realistic probabilities
class SpinWheelConfig {
  static const List<SpinWheelSegment> segments = [
    SpinWheelSegment(amount: 10, probability: 0.35, color: '#FF5722', index: 0),
    SpinWheelSegment(amount: 25, probability: 0.25, color: '#FF9800', index: 1),
    SpinWheelSegment(amount: 50, probability: 0.20, color: '#FFC107', index: 2),
    SpinWheelSegment(
      amount: 100,
      probability: 0.12,
      color: '#4CAF50',
      index: 3,
    ),
    SpinWheelSegment(
      amount: 250,
      probability: 0.06,
      color: '#2196F3',
      index: 4,
    ),
    SpinWheelSegment(
      amount: 500,
      probability: 0.02,
      color: '#9C27B0',
      index: 5,
    ),
  ];

  static const double regularSpinCost = 10.0;
  static const double maxSpinCost = 50.0;
  static const int maxSpinMultiplier = 3;

  static double getEntryAmount(SpinType spinType) {
    switch (spinType) {
      case SpinType.regular:
        return regularSpinCost;
      case SpinType.max:
        return maxSpinCost;
    }
  }

  static int getMultiplier(SpinType spinType) {
    switch (spinType) {
      case SpinType.regular:
        return 1;
      case SpinType.max:
        return maxSpinMultiplier;
    }
  }

  static bool canAffordSpin(double walletBalance, SpinType spinType) {
    return walletBalance >= getEntryAmount(spinType);
  }

  static SpinWheelSegment getSegmentByIndex(int index) {
    return segments.firstWhere(
      (segment) => segment.index == index,
      orElse: () => segments.first,
    );
  }

  static int determineWinningSegment() {
    final random = Random();
    final randomValue = random.nextDouble();

    double cumulativeProbability = 0.0;
    for (final segment in segments) {
      cumulativeProbability += segment.probability;
      if (randomValue <= cumulativeProbability) {
        return segment.index;
      }
    }

    // Fallback to first segment
    return segments.first.index;
  }
}
