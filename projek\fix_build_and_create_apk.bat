@echo off
echo ========================================
echo Flutter APK Build Fix and Creation Script
echo ========================================
echo.

echo [1/8] Stopping any running Gradle daemons...
call gradle --stop 2>nul
call .\android\gradlew --stop 2>nul

echo [2/8] Cleaning Flutter build cache...
call flutter clean

echo [3/8] Cleaning Android build directory...
if exist "build" (
    echo Removing build directory...
    rmdir /s /q "build" 2>nul
)

echo [4/8] Cleaning Android Gradle cache...
if exist "android\.gradle" (
    echo Removing Android .gradle directory...
    rmdir /s /q "android\.gradle" 2>nul
)

echo [5/8] Cleaning pub cache for problematic plugins...
echo Removing flutter_plugin_android_lifecycle from pub cache...
call flutter pub cache clean

echo [6/8] Getting fresh dependencies...
call flutter pub get

echo [7/8] Building APK (Debug first for testing)...
echo Building debug APK...
call flutter build apk --debug --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Debug APK built successfully!
    echo.
    echo [8/8] Building Release APK...
    call flutter build apk --release --no-shrink --verbose
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo ✅ SUCCESS: APK BUILD COMPLETED!
        echo ========================================
        echo.
        echo APK files created:
        if exist "build\app\outputs\flutter-apk\app-debug.apk" (
            echo ✅ Debug APK: build\app\outputs\flutter-apk\app-debug.apk
        )
        if exist "build\app\outputs\flutter-apk\app-release.apk" (
            echo ✅ Release APK: build\app\outputs\flutter-apk\app-release.apk
        )
        echo.
        echo You can now install the APK on your device!
    ) else (
        echo.
        echo ❌ Release APK build failed. Check the error messages above.
        echo Debug APK is available for testing.
    )
) else (
    echo.
    echo ❌ Debug APK build failed. 
    echo Trying alternative approach...
    echo.
    echo Attempting build without cache...
    call flutter build apk --debug --no-pub --verbose
)

echo.
echo Build process completed.
pause
