import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class AdminSupportInterface extends StatefulWidget {
  const AdminSupportInterface({super.key});

  @override
  State<AdminSupportInterface> createState() => _AdminSupportInterfaceState();
}

class _AdminSupportInterfaceState extends State<AdminSupportInterface> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _replyController = TextEditingController();
  String? _selectedChatId;
  bool _isSendingReply = false;

  @override
  void dispose() {
    _replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Support Admin Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: Row(
        children: [
          // Support chats list
          Expanded(flex: 1, child: _buildSupportChatsList()),
          // Chat messages view
          if (_selectedChatId != null)
            Expanded(flex: 2, child: _buildChatMessagesView()),
        ],
      ),
    );
  }

  Widget _buildSupportChatsList() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: const Row(
              children: [
                Icon(Icons.support_agent),
                SizedBox(width: 8),
                Text(
                  'Active Support Chats',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: _firestore
                  .collection('support_chats')
                  .where('status', isEqualTo: 'active')
                  .orderBy('lastMessageAt', descending: true)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final chats = snapshot.data?.docs ?? [];

                if (chats.isEmpty) {
                  return const Center(child: Text('No active support chats'));
                }

                return ListView.builder(
                  itemCount: chats.length,
                  itemBuilder: (context, index) {
                    final chat = chats[index];
                    final chatData = chat.data() as Map<String, dynamic>;
                    final isSelected = _selectedChatId == chat.id;

                    return ListTile(
                      selected: isSelected,
                      leading: CircleAvatar(
                        backgroundColor: chatData['isReadBySupport'] == false
                            ? Colors.red
                            : Colors.green,
                        child: Text(
                          (chatData['userName'] as String? ?? 'U')[0]
                              .toUpperCase(),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(chatData['userName'] ?? 'Unknown User'),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(chatData['userEmail'] ?? ''),
                          Text(
                            'Topic: ${chatData['topic'] ?? 'General'}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (chatData['isReadBySupport'] == false)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Text(
                            chatData['lastMessageAt'] != null
                                ? DateFormat('HH:mm').format(
                                    (chatData['lastMessageAt'] as Timestamp)
                                        .toDate(),
                                  )
                                : '',
                            style: const TextStyle(fontSize: 10),
                          ),
                        ],
                      ),
                      onTap: () {
                        setState(() {
                          _selectedChatId = chat.id;
                        });
                        _markChatAsRead(chat.id);
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessagesView() {
    return Column(
      children: [
        // Chat header
        Container(
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: Row(
            children: [
              const Icon(Icons.chat),
              const SizedBox(width: 8),
              const Text(
                'Support Chat',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _resolveSupportChat(_selectedChatId!),
                icon: const Icon(Icons.check),
                label: const Text('Resolve'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // Messages
        Expanded(
          child: StreamBuilder<QuerySnapshot>(
            stream: _firestore
                .collection('support_chats')
                .doc(_selectedChatId)
                .collection('messages')
                .orderBy('timestamp', descending: true)
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              }

              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final messages = snapshot.data?.docs ?? [];

              return ListView.builder(
                reverse: true,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  final message =
                      messages[index].data() as Map<String, dynamic>;
                  final isUser = message['senderType'] == 'user';
                  final timestamp = message['timestamp'] as Timestamp?;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      mainAxisAlignment: isUser
                          ? MainAxisAlignment.start
                          : MainAxisAlignment.end,
                      children: [
                        if (isUser)
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: Theme.of(
                              context,
                            ).colorScheme.primary,
                            child: const Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isUser
                                  ? Theme.of(
                                      context,
                                    ).colorScheme.surfaceContainerHighest
                                  : Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  message['text'] ?? '',
                                  style: TextStyle(
                                    color: isUser
                                        ? Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant
                                        : Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  timestamp != null
                                      ? DateFormat(
                                          'MMM dd, HH:mm',
                                        ).format(timestamp.toDate())
                                      : 'Sending...',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: isUser
                                        ? Theme.of(context)
                                              .colorScheme
                                              .onSurfaceVariant
                                              .withValues(alpha: 0.7)
                                        : Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (!isUser)
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: Theme.of(
                              context,
                            ).colorScheme.secondary,
                            child: const Icon(
                              Icons.support_agent,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
        // Reply input
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _replyController,
                  decoration: const InputDecoration(
                    hintText: 'Type your reply...',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: null,
                ),
              ),
              const SizedBox(width: 8),
              FloatingActionButton(
                onPressed: _isSendingReply ? null : _sendReply,
                mini: true,
                child: _isSendingReply
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.send),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _markChatAsRead(String chatId) async {
    try {
      await _firestore.collection('support_chats').doc(chatId).update({
        'isReadBySupport': true,
      });
    } catch (e, stackTrace) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to mark chat as read: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
      debugPrint('Error marking chat as read: $e\n$stackTrace');
    }
  }

  Future<void> _sendReply() async {
    if (_replyController.text.trim().isEmpty || _selectedChatId == null) {
      return;
    }

    // Add message length validation
    final messageText = _replyController.text.trim();
    if (messageText.length > 1000) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Message is too long. Please keep it under 1000 characters.',
          ),
          duration: const Duration(seconds: 5),
        ),
      );
      return;
    }

    setState(() {
      _isSendingReply = true;
    });

    try {
      // Add message to messages subcollection
      await _firestore
          .collection('support_chats')
          .doc(_selectedChatId)
          .collection('messages')
          .add({
            'text': messageText,
            'senderId': 'support_agent',
            'senderEmail': '<EMAIL>',
            'senderName': 'Support Agent',
            'senderType': 'agent',
            'timestamp': FieldValue.serverTimestamp(),
            'status': 'sent',
            'isReadBySupport': true,
          });

      // Update chat metadata
      await _firestore.collection('support_chats').doc(_selectedChatId).update({
        'lastMessageAt': FieldValue.serverTimestamp(),
        'isReadBySupport': true,
      });

      _replyController.clear();
    } catch (e, stackTrace) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send reply: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
      debugPrint('Error sending reply: $e\n$stackTrace');
    } finally {
      if (mounted) {
        setState(() {
          _isSendingReply = false;
        });
      }
    }
  }

  Future<void> _resolveSupportChat(String chatId) async {
    try {
      await _firestore.collection('support_chats').doc(chatId).update({
        'status': 'resolved',
        'resolvedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        setState(() {
          _selectedChatId = null;
        });

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Support chat resolved')));
      }
    } catch (e, stackTrace) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resolve chat: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
      debugPrint('Error resolving chat: $e\n$stackTrace');
    }
  }
}
