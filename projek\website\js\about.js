// About Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
  
  // Initialize animations
  initializeAnimations();
  
  // Initialize timeline animations
  initializeTimeline();
  
  // Initialize team member interactions
  initializeTeamInteractions();
  
  // Initialize stats counter animation
  initializeStatsCounter();
  
  function initializeAnimations() {
    // Fade-in animations for elements
    const fadeElements = document.querySelectorAll('.mv-card, .team-member, .award-item');
    
    const fadeObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '0';
          entry.target.style.transform = 'translateY(30px)';
          entry.target.style.transition = 'all 0.6s ease';
          
          setTimeout(() => {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }, 100);
          
          fadeObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.1
    });

    fadeElements.forEach(element => {
      fadeObserver.observe(element);
    });
  }
  
  function initializeTimeline() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    const timelineObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '0';
          entry.target.style.transform = 'translateX(-50px)';
          entry.target.style.transition = 'all 0.8s ease';
          
          setTimeout(() => {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateX(0)';
          }, 200);
          
          timelineObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.2
    });

    timelineItems.forEach(item => {
      timelineObserver.observe(item);
    });
  }
  
  function initializeTeamInteractions() {
    const teamMembers = document.querySelectorAll('.team-member');
    
    teamMembers.forEach(member => {
      member.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.02)';
        
        // Add glow effect to member photo
        const photo = this.querySelector('.member-photo');
        if (photo) {
          photo.style.boxShadow = '0 10px 30px rgba(255, 153, 51, 0.5)';
        }
      });
      
      member.addEventListener('mouseleave', function() {
        this.style.transform = '';
        
        // Remove glow effect
        const photo = this.querySelector('.member-photo');
        if (photo) {
          photo.style.boxShadow = '';
        }
      });
      
      // Social links interaction
      const socialLinks = member.querySelectorAll('.member-social a');
      socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          
          // Add click animation
          this.style.transform = 'scale(0.9)';
          setTimeout(() => {
            this.style.transform = '';
          }, 150);
          
          // Show tooltip or action
          showTooltip(this, 'Feature coming soon!');
        });
      });
    });
  }
  
  function initializeStatsCounter() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const statsObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const target = entry.target;
          const finalValue = target.textContent;
          
          // Extract number from text (e.g., "1M+" -> 1000000)
          let numericValue = 0;
          if (finalValue.includes('M+')) {
            numericValue = parseInt(finalValue) * 1000000;
          } else if (finalValue.includes('K+')) {
            numericValue = parseInt(finalValue) * 1000;
          } else if (finalValue.includes('/')) {
            // For "24/7" type values, don't animate
            return;
          } else {
            numericValue = parseInt(finalValue);
          }
          
          // Animate counter
          animateCounter(target, 0, numericValue, finalValue, 2000);
          
          statsObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.5
    });

    statNumbers.forEach(stat => {
      statsObserver.observe(stat);
    });
  }
  
  function animateCounter(element, start, end, finalText, duration) {
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = Math.floor(start + (end - start) * easeOutQuart);
      
      // Format the number based on the final text format
      if (finalText.includes('M+')) {
        element.textContent = (current / 1000000).toFixed(1) + 'M+';
      } else if (finalText.includes('K+')) {
        element.textContent = (current / 1000).toFixed(0) + 'K+';
      } else {
        element.textContent = current.toLocaleString();
      }
      
      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      } else {
        element.textContent = finalText;
      }
    }
    
    requestAnimationFrame(updateCounter);
  }
  
  function showTooltip(element, message) {
    // Remove existing tooltip
    const existingTooltip = document.querySelector('.custom-tooltip');
    if (existingTooltip) {
      existingTooltip.remove();
    }
    
    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = message;
    tooltip.style.cssText = `
      position: absolute;
      background: var(--dark-gray);
      color: var(--white);
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.8rem;
      z-index: 1000;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    // Show tooltip
    setTimeout(() => {
      tooltip.style.opacity = '1';
    }, 10);
    
    // Hide tooltip after 2 seconds
    setTimeout(() => {
      tooltip.style.opacity = '0';
      setTimeout(() => {
        if (tooltip.parentNode) {
          tooltip.remove();
        }
      }, 300);
    }, 2000);
  }
  
  // CTA buttons interaction
  const ctaButtons = document.querySelectorAll('.cta-buttons .btn');
  ctaButtons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-3px) scale(1.05)';
    });
    
    button.addEventListener('mouseleave', function() {
      this.style.transform = '';
    });
    
    button.addEventListener('click', function(e) {
      // Add click ripple effect
      const ripple = document.createElement('span');
      ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
      `;
      
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = e.clientX - rect.left - size / 2 + 'px';
      ripple.style.top = e.clientY - rect.top - size / 2 + 'px';
      
      this.style.position = 'relative';
      this.style.overflow = 'hidden';
      this.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
  
  // Add ripple animation CSS
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
  
  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
  
  // Add parallax effect to hero section
  window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroImage = document.querySelector('.hero-image img');
    
    if (heroImage && scrolled < window.innerHeight) {
      heroImage.style.transform = `translateY(${scrolled * 0.3}px) rotate(${scrolled * 0.05}deg)`;
    }
  });
  
  console.log('About page initialized successfully!');
});
