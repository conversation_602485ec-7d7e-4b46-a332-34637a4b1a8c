import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_router.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../data/services/payment_service.dart';
import '../../domain/models/payment_method.dart';

class PaymentPage extends ConsumerStatefulWidget {
  final double totalAmount;
  final String orderId;

  const PaymentPage({
    super.key,
    required this.totalAmount,
    required this.orderId,
  });

  @override
  ConsumerState<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends ConsumerState<PaymentPage> {
  final PaymentService _paymentService = PaymentService();
  PaymentMethod? _selectedPaymentMethod;
  bool _isProcessing = false;

  final List<PaymentMethod> _paymentMethods = [
    PaymentMethod(
      id: 'upi',
      name: 'upi',
      displayName: 'UPI',
      type: PaymentType.upi,
      iconPath: 'assets/icons/payment/upi.png',
      isEnabled: true,
    ),
    PaymentMethod(
      id: 'card',
      name: 'card',
      displayName: 'Credit/Debit Card',
      type: PaymentType.card,
      iconPath: 'assets/icons/payment/credit_card.png',
      isEnabled: true,
    ),
    PaymentMethod(
      id: 'netbanking',
      name: 'netbanking',
      displayName: 'Net Banking',
      type: PaymentType.netBanking,
      iconPath: 'assets/icons/payment/net_banking.png',
      isEnabled: true,
    ),
    PaymentMethod(
      id: 'wallet',
      name: 'wallet',
      displayName: 'Digital Wallet',
      type: PaymentType.wallet,
      iconPath: 'assets/icons/payment/wallet.png',
      isEnabled: true,
    ),
    PaymentMethod(
      id: 'cod',
      name: 'cod',
      displayName: 'Cash on Delivery',
      type: PaymentType.cod,
      iconPath: 'assets/icons/payment/cod.png',
      isEnabled: true,
    ),
  ];

  @override
  void dispose() {
    _paymentService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Payment'), centerTitle: true),
      body: Column(
        children: [
          // Order Summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: colorScheme.surfaceContainerHighest,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Summary',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Order ID: ${widget.orderId}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      '₹${widget.totalAmount.toStringAsFixed(2)}',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Payment Methods
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: _paymentMethods.length,
              itemBuilder: (context, index) {
                final method = _paymentMethods[index];
                return _buildPaymentMethodTile(method);
              },
            ),
          ),

          // Pay Now Button
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _selectedPaymentMethod != null && !_isProcessing
                    ? _processPayment
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isProcessing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : Text(
                        'Pay ₹${widget.totalAmount.toStringAsFixed(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onPrimary,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethod method) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedPaymentMethod?.id == method.id;

    // Get icon based on payment type
    IconData getPaymentIcon(PaymentType type) {
      switch (type) {
        case PaymentType.upi:
          return Icons.account_balance_wallet;
        case PaymentType.card:
          return Icons.credit_card;
        case PaymentType.netBanking:
          return Icons.account_balance;
        case PaymentType.wallet:
          return Icons.wallet;
        case PaymentType.cod:
          return Icons.money;
        case PaymentType.emi:
          return Icons.payment;
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isSelected ? colorScheme.primaryContainer.withValues(alpha: 0.3) : null,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primary
                : method.isEnabled
                    ? colorScheme.primaryContainer
                    : colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            getPaymentIcon(method.type),
            color: isSelected
                ? colorScheme.onPrimary
                : method.isEnabled
                    ? colorScheme.onPrimaryContainer
                    : colorScheme.onSurfaceVariant,
          ),
        ),
        title: Text(
          method.displayName,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
            color: isSelected
                ? colorScheme.primary
                : method.isEnabled
                    ? colorScheme.onSurface
                    : colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: Radio<PaymentMethod>(
          value: method,
          groupValue: _selectedPaymentMethod,
          onChanged: method.isEnabled
              ? (PaymentMethod? value) {
                  setState(() {
                    _selectedPaymentMethod = value;
                  });
                }
              : null,
        ),
        onTap: method.isEnabled
            ? () {
                setState(() {
                  _selectedPaymentMethod = method;
                });
              }
            : null,
      ),
    );
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      PaymentResult result;

      switch (_selectedPaymentMethod!.type) {
        case PaymentType.upi:
          result = await _processUpiPayment();
          break;
        case PaymentType.card:
          result = await _processCardPayment();
          break;
        case PaymentType.netBanking:
          result = await _processNetBankingPayment();
          break;
        case PaymentType.wallet:
          result = await _processWalletPayment();
          break;
        case PaymentType.cod:
          result = await _processCodPayment();
          break;
        case PaymentType.emi:
          result = await _processEmiPayment();
          break;
      }

      if (mounted) {
        if (result.isSuccess) {
          _showPaymentSuccess(result);
        } else {
          _showPaymentError(result.errorMessage ?? 'Payment failed');
        }
      }
    } catch (e) {
      if (mounted) {
        _showPaymentError('Payment failed: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<PaymentResult> _processUpiPayment() async {
    // Simulate UPI payment
    await Future.delayed(const Duration(seconds: 2));
    return PaymentResult(
      isSuccess: true,
      transactionId: 'upi_${DateTime.now().millisecondsSinceEpoch}',
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  Future<PaymentResult> _processCardPayment() async {
    // Simulate card payment
    await Future.delayed(const Duration(seconds: 3));
    return PaymentResult(
      isSuccess: true,
      transactionId: 'card_${DateTime.now().millisecondsSinceEpoch}',
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  Future<PaymentResult> _processNetBankingPayment() async {
    // Simulate net banking payment
    await Future.delayed(const Duration(seconds: 2));
    return PaymentResult(
      isSuccess: true,
      transactionId: 'nb_${DateTime.now().millisecondsSinceEpoch}',
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  Future<PaymentResult> _processWalletPayment() async {
    // Simulate wallet payment
    await Future.delayed(const Duration(seconds: 1));
    return PaymentResult(
      isSuccess: true,
      transactionId: 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  Future<PaymentResult> _processCodPayment() async {
    // Process COD payment
    final transaction = await _paymentService.processCodPayment(
      orderId: widget.orderId,
      amount: widget.totalAmount,
    );

    return PaymentResult(
      isSuccess: transaction.status == PaymentStatus.success,
      transactionId: transaction.id,
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  Future<PaymentResult> _processEmiPayment() async {
    // Simulate EMI payment
    await Future.delayed(const Duration(seconds: 2));
    return PaymentResult(
      isSuccess: true,
      transactionId: 'emi_${DateTime.now().millisecondsSinceEpoch}',
      orderId: widget.orderId,
      paymentMethod: _selectedPaymentMethod!,
    );
  }

  void _showPaymentSuccess(PaymentResult result) {
    // Clear cart after successful payment
    ref.read(cartProvider.notifier).clearCart();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.check_circle,
          color: Theme.of(context).colorScheme.primary,
          size: 48,
        ),
        title: const Text('Payment Successful!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Transaction ID: ${result.transactionId}'),
            const SizedBox(height: 8),
            Text('Order ID: ${result.orderId}'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go(AppRoutes.home);
            },
            child: const Text('Continue Shopping'),
          ),
        ],
      ),
    );
  }

  void _showPaymentError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: SnackBarAction(
          label: 'Retry',
          onPressed: () {
            setState(() {
              _isProcessing = false;
            });
          },
        ),
      ),
    );
  }
}
