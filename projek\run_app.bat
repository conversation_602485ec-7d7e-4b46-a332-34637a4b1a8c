@echo off
echo ========================================
echo Proje<PERSON>t App - Quick Run
echo ========================================
echo.

echo Checking Flutter setup...
flutter doctor
echo.

echo Getting dependencies...
flutter pub get
echo.

echo Building and running app...
echo.
echo Choose your platform:
echo 1. Android Device/Emulator
echo 2. Web Browser
echo 3. Windows Desktop
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo Starting on Android...
    flutter run
) else if "%choice%"=="2" (
    echo Starting on Web...
    flutter run -d chrome
) else if "%choice%"=="3" (
    echo Starting on Windows...
    flutter run -d windows
) else (
    echo Invalid choice. Starting on default device...
    flutter run
)

echo.
echo App finished running.
pause
