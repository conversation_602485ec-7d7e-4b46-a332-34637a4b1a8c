// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wishlist_item.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WishlistItemAdapter extends TypeAdapter<WishlistItem> {
  @override
  final int typeId = 3;

  @override
  WishlistItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WishlistItem(
      id: fields[0] as String,
      productId: fields[1] as String,
      name: fields[2] as String,
      price: fields[3] as double,
      originalPrice: fields[4] as double,
      currency: fields[5] as String,
      imageUrl: fields[6] as String,
      category: fields[7] as String,
      brand: fields[8] as String,
      vendorId: fields[9] as String,
      vendorName: fields[10] as String,
      addedAt: fields[11] as DateTime,
      inStock: fields[12] as bool,
      rating: fields[13] as double,
      reviewCount: fields[14] as int,
    );
  }

  @override
  void write(BinaryWriter writer, WishlistItem obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.originalPrice)
      ..writeByte(5)
      ..write(obj.currency)
      ..writeByte(6)
      ..write(obj.imageUrl)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.brand)
      ..writeByte(9)
      ..write(obj.vendorId)
      ..writeByte(10)
      ..write(obj.vendorName)
      ..writeByte(11)
      ..write(obj.addedAt)
      ..writeByte(12)
      ..write(obj.inStock)
      ..writeByte(13)
      ..write(obj.rating)
      ..writeByte(14)
      ..write(obj.reviewCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WishlistItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WishlistAdapter extends TypeAdapter<Wishlist> {
  @override
  final int typeId = 4;

  @override
  Wishlist read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Wishlist(
      items: (fields[0] as List).cast<WishlistItem>(),
      updatedAt: fields[1] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, Wishlist obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.items)
      ..writeByte(1)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WishlistAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WishlistItem _$WishlistItemFromJson(Map<String, dynamic> json) => WishlistItem(
      id: json['id'] as String,
      productId: json['productId'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      imageUrl: json['imageUrl'] as String,
      category: json['category'] as String,
      brand: json['brand'] as String,
      vendorId: json['vendorId'] as String,
      vendorName: json['vendorName'] as String,
      addedAt: DateTime.parse(json['addedAt'] as String),
      inStock: json['inStock'] as bool? ?? true,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$WishlistItemToJson(WishlistItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'name': instance.name,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'currency': instance.currency,
      'imageUrl': instance.imageUrl,
      'category': instance.category,
      'brand': instance.brand,
      'vendorId': instance.vendorId,
      'vendorName': instance.vendorName,
      'addedAt': instance.addedAt.toIso8601String(),
      'inStock': instance.inStock,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
    };

Wishlist _$WishlistFromJson(Map<String, dynamic> json) => Wishlist(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => WishlistItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WishlistToJson(Wishlist instance) => <String, dynamic>{
      'items': instance.items,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
