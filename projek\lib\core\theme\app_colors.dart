import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primaryBlue = Color(0xFF2563EB);
  static const Color primaryBlueLight = Color(0xFF3B82F6);
  static const Color primaryBlueDark = Color(0xFF1D4ED8);

  // Secondary Colors
  static const Color secondaryOrange = Color(0xFFEA580C);
  static const Color secondaryOrangeLight = Color(0xFFF97316);
  static const Color secondaryOrangeDark = Color(0xFFDC2626);

  // Accent Colors
  static const Color accentGreen = Color(0xFF059669);
  static const Color accentPurple = Color(0xFF7C3AED);
  static const Color accentPink = Color(0xFFDB2777);

  // Neutral Colors
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFE5E5E5);
  static const Color neutral300 = Color(0xFFD4D4D4);
  static const Color neutral400 = Color(0xFFA3A3A3);
  static const Color neutral500 = Color(0xFF737373);
  static const Color neutral600 = Color(0xFF525252);
  static const Color neutral700 = Color(0xFF404040);
  static const Color neutral800 = Color(0xFF262626);
  static const Color neutral900 = Color(0xFF171717);

  // Semantic Colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // App-specific colors for compatibility
  static const Color userPrimary = primaryBlue;
  static const Color riderPrimary = accentGreen;
  static const Color sellerPrimary = secondaryOrange;

  // Text colors
  static const Color textPrimary = neutral900;
  static const Color textSecondary = neutral500;
  static const Color textOnPrimary = Colors.white;

  // Background colors
  static const Color background = neutral50;
  static const Color backgroundLight = Colors.white;
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = neutral50;
  static const Color surfaceDark = Color(0xFF1E1E1E);

  // Border colors
  static const Color borderLight = neutral300;
  static const Color borderDark = neutral600;

  // Additional colors for compatibility
  static const Color grey50 = neutral50;
  static const Color grey100 = neutral100;
  static const Color grey200 = neutral200;
  static const Color grey300 = neutral300;
  static const Color grey400 = neutral400;
  static const Color grey500 = neutral500;
  static const Color grey600 = neutral600;
  static const Color grey700 = neutral700;
  static const Color grey800 = neutral800;
  static const Color grey900 = neutral900;

  // Rating colors
  static const Color ratingGold = Color(0xFFFFD700);

  // Light Color Scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primaryBlue,
    onPrimary: Colors.white,
    primaryContainer: Color(0xFFDEEBFF),
    onPrimaryContainer: Color(0xFF001D36),
    secondary: secondaryOrange,
    onSecondary: Colors.white,
    secondaryContainer: Color(0xFFFFDCC2),
    onSecondaryContainer: Color(0xFF2A1800),
    tertiary: accentGreen,
    onTertiary: Colors.white,
    tertiaryContainer: Color(0xFFA7F3D0),
    onTertiaryContainer: Color(0xFF002114),
    error: error,
    onError: Colors.white,
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    surface: Colors.white,
    onSurface: neutral900,
    surfaceContainerHighest: neutral100,
    onSurfaceVariant: neutral600,
    outline: neutral300,
    outlineVariant: neutral200,
    shadow: Colors.black26,
    scrim: Colors.black54,
    inverseSurface: neutral800,
    onInverseSurface: neutral100,
    inversePrimary: Color(0xFF9ECAFF),
    surfaceTint: primaryBlue,
  );

  // Dark Color Scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF9ECAFF),
    onPrimary: Color(0xFF003258),
    primaryContainer: Color(0xFF004A77),
    onPrimaryContainer: Color(0xFFDEEBFF),
    secondary: Color(0xFFFFB77C),
    onSecondary: Color(0xFF452B00),
    secondaryContainer: Color(0xFF633F00),
    onSecondaryContainer: Color(0xFFFFDCC2),
    tertiary: Color(0xFF8BF5B4),
    onTertiary: Color(0xFF003826),
    tertiaryContainer: Color(0xFF005138),
    onTertiaryContainer: Color(0xFFA7F3D0),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF0F1419),
    onSurface: Color(0xFFE1E3E6),
    surfaceContainerHighest: Color(0xFF2B3036),
    onSurfaceVariant: Color(0xFFC1C7CE),
    outline: Color(0xFF8B9198),
    outlineVariant: Color(0xFF41474D),
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: Color(0xFFE1E3E6),
    onInverseSurface: Color(0xFF2E3135),
    inversePrimary: primaryBlue,
    surfaceTint: Color(0xFF9ECAFF),
  );

  // Category Colors
  static const Map<String, Color> categoryColors = {
    'Food & Beverages': Color(0xFFEF4444),
    'Clothing & Fashion': Color(0xFFEC4899),
    'Electronics': Color(0xFF3B82F6),
    'Home & Garden': Color(0xFF10B981),
    'Sports & Outdoors': Color(0xFFF59E0B),
    'Books & Media': Color(0xFF8B5CF6),
    'Health & Beauty': Color(0xFFF97316),
    'Automotive': Color(0xFF6B7280),
    'Vehicle Rentals': Color(0xFF059669),
    'Home Services': Color(0xFF7C3AED),
    'Professional Services': Color(0xFF2563EB),
    'Education & Tutoring': Color(0xFFDB2777),
    'Health & Wellness': Color(0xFF10B981),
    'Event Services': Color(0xFFEA580C),
    'Delivery Services': Color(0xFF3B82F6),
    'Maintenance & Repair': Color(0xFF6B7280),
  };

  // Status Colors
  static const Map<String, Color> statusColors = {
    'pending': warning,
    'confirmed': info,
    'processing': primaryBlue,
    'shipped': accentPurple,
    'delivered': success,
    'cancelled': error,
    'refunded': neutral500,
  };

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryBlue, primaryBlueLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryOrange, secondaryOrangeLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [accentGreen, Color(0xFF34D399)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, Color(0xFFFBBF24)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, Color(0xFFF87171)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Helper methods
  static Color getCategoryColor(String category) {
    return categoryColors[category] ?? primaryBlue;
  }

  static Color getStatusColor(String status) {
    return statusColors[status] ?? neutral500;
  }

  static Color getContrastColor(Color color) {
    // Calculate luminance
    final luminance = color.computeLuminance();
    // Return black or white based on luminance
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
