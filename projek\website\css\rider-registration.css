/* Rider Registration Page Styles */

/* Hero Section */
.rider-hero {
  background: linear-gradient(135deg, var(--green) 0%, var(--forest-green) 50%, var(--peacock-blue) 100%);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  position: relative;
  overflow: hidden;
}

.rider-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.rider-icon-large {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  animation: float 4s ease-in-out infinite;
}

.rider-icon-large img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.join-text {
  color: var(--white) !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.rider-text {
  background: linear-gradient(45deg, #32CD32, #90EE90);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.network-text {
  background: linear-gradient(45deg, #87CEEB, #4169E1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--saffron);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-form {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-apply h3 {
  margin-bottom: 1rem;
  color: var(--white);
}

.quick-apply p {
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.btn-large {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Benefits Section */
.benefits-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefit-card {
  background: var(--white);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 25px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(19, 136, 8, 0.1);
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
  border-color: rgba(19, 136, 8, 0.3);
}

.benefit-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-green);
  border-radius: 50%;
  color: var(--white);
}

.benefit-icon .material-icons {
  font-size: 2.5rem;
}

.benefit-card h3 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
  font-size: 1.3rem;
}

.benefit-card p {
  color: var(--medium-gray);
  line-height: 1.6;
}

/* Requirements Section */
.requirements-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.requirements-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
  align-items: start;
}

.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.requirement-category {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 5px 15px var(--shadow-light);
  border: 1px solid rgba(19, 136, 8, 0.1);
}

.requirement-category h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: var(--green);
  font-size: 1.2rem;
}

.requirement-category h3 .material-icons {
  font-size: 1.5rem;
}

.requirement-category ul {
  list-style: none;
  padding: 0;
}

.requirement-category li {
  padding: 0.5rem 0;
  color: var(--medium-gray);
  position: relative;
  padding-left: 1.5rem;
}

.requirement-category li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--green);
  font-weight: bold;
}

.requirements-image {
  text-align: center;
}

.requirements-image img {
  width: 200px;
  height: 200px;
  object-fit: contain;
  margin-bottom: 2rem;
}

.requirements-note {
  background: linear-gradient(135deg, #FFF3CD 0%, #FCF4A3 100%);
  border: 1px solid #F0E68C;
  border-radius: 12px;
  padding: 1.5rem;
  color: #856404;
}

/* Registration Form Section */
.registration-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 15px 40px var(--shadow);
  overflow: hidden;
}

.form-progress {
  display: flex;
  background: var(--gradient-green);
  padding: 2rem;
}

.progress-step {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
}

.progress-step.active {
  color: var(--white);
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.progress-step.active:not(:last-child)::after {
  background: var(--white);
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.progress-step.active .step-number {
  background: var(--white);
  color: var(--green);
}

.step-label {
  font-size: 0.9rem;
  font-weight: 500;
}

.registration-form {
  padding: 3rem;
}

.form-step {
  display: none;
}

.form-step.active {
  display: block;
}

.form-step h3 {
  margin-bottom: 2rem;
  color: var(--dark-gray);
  font-size: 1.5rem;
  text-align: center;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-gray);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--light-gray);
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--green);
  box-shadow: 0 0 0 3px rgba(19, 136, 8, 0.1);
}

.form-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

/* Document Upload Styles */
.document-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.upload-item label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-gray);
}

.upload-box {
  border: 2px dashed var(--light-gray);
  border-radius: 12px;
  padding: 2rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--cream);
  position: relative;
}

.upload-box:hover {
  border-color: var(--green);
  background: rgba(19, 136, 8, 0.05);
}

.upload-box.uploaded {
  border-color: var(--green);
  background: rgba(19, 136, 8, 0.1);
}

.upload-box .material-icons {
  font-size: 2rem;
  color: var(--medium-gray);
  display: block;
  margin-bottom: 0.5rem;
}

.upload-box span:last-child {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.upload-box input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Verification Section Styles */
.verification-section,
.agreement-section {
  background: var(--light-gray);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.verification-section h4,
.agreement-section h4 {
  color: var(--dark-gray);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.verification-list {
  list-style: none;
  padding: 0;
}

.verification-list li {
  padding: 0.5rem 0;
  color: var(--medium-gray);
  position: relative;
  padding-left: 1.5rem;
}

.verification-list li::before {
  content: '🔍';
  position: absolute;
  left: 0;
}

.agreement-box {
  background: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(19, 136, 8, 0.2);
}

.agreement-box ul {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.agreement-box li {
  padding: 0.3rem 0;
  color: var(--medium-gray);
  position: relative;
  padding-left: 1.5rem;
}

.agreement-box li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--green);
  font-weight: bold;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  color: var(--medium-gray);
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
  position: relative;
}

.checkbox-label a {
  color: var(--green);
  text-decoration: none;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

.final-note {
  background: linear-gradient(135deg, #FFF3CD 0%, #FCF4A3 100%);
  border: 1px solid #F0E68C;
  border-radius: 12px;
  padding: 1.5rem;
  color: #856404;
  margin-top: 2rem;
}

.final-note p {
  margin: 0;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .requirements-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-progress {
    padding: 1rem;
  }
  
  .step-label {
    display: none;
  }
  
  .registration-form {
    padding: 2rem;
  }
}
