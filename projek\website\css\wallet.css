/* Wallet System Styles */

:root {
  --primary-color: #ff9933;
  --secondary-color: #138808;
  --accent-color: #146eb4;
  --dark-gray: #2c3e50;
  --medium-gray: #666;
  --light-gray: #f8f9fa;
  --white: #ffffff;
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.05);
  --gradient-primary: linear-gradient(135deg, #ff9933, #138808);
  --gradient-secondary: linear-gradient(135deg, #146eb4, #667eea);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--dark-gray);
  background: var(--light-gray);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header */
.header {
  background: var(--white);
  box-shadow: 0 2px 10px var(--shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--dark-gray);
}

.logo img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
  color: var(--primary-color);
}

/* Wallet Hero */
.wallet-hero {
  background: var(--gradient-primary);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  margin-top: 70px;
}

.wallet-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 800;
}

.wallet-text {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wallet-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Wallet Dashboard */
.wallet-dashboard {
  padding: 5rem 0;
  background: var(--white);
}

.balance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.balance-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--shadow);
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.balance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px var(--shadow);
}

.pc-card {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(255, 153, 51, 0.05), rgba(255, 255, 255, 1));
}

.inr-card {
  border-color: var(--success);
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(255, 255, 255, 1));
}

.gaming-card {
  border-color: var(--accent-color);
  background: linear-gradient(135deg, rgba(20, 110, 180, 0.05), rgba(255, 255, 255, 1));
}

.staking-card {
  border-color: var(--warning);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 255, 255, 1));
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-gray);
}

.visibility-toggle {
  background: none;
  border: none;
  color: var(--medium-gray);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.visibility-toggle:hover {
  background: var(--light-gray);
  color: var(--dark-gray);
}

.balance-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.balance-inr,
.balance-subtitle {
  color: var(--medium-gray);
  font-size: 1rem;
  margin-bottom: 2rem;
}

.card-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn.primary {
  background: var(--gradient-primary);
  color: var(--white);
}

.action-btn.secondary {
  background: var(--light-gray);
  color: var(--dark-gray);
  border: 2px solid var(--medium-gray);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Quick Actions */
.quick-actions {
  background: var(--light-gray);
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
}

.quick-actions h3 {
  margin-bottom: 2rem;
  font-size: 1.8rem;
  color: var(--dark-gray);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: var(--white);
  border: none;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px var(--shadow-light);
}

.quick-action-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow);
  background: var(--gradient-primary);
  color: var(--white);
}

.quick-action-btn .material-icons {
  font-size: 2rem;
}

.quick-action-btn span:last-child {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Conversion Section */
.conversion-section {
  padding: 3rem 0;
  background: var(--light-gray);
}

.conversion-card {
  background: var(--white);
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px var(--shadow);
}

.conversion-card h3 {
  margin-bottom: 2rem;
  font-size: 1.8rem;
  color: var(--dark-gray);
}

.rate-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.rate-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.rate-status {
  background: var(--success);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Staking Section */
.staking-section {
  padding: 5rem 0;
  background: var(--white);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.section-subtitle {
  text-align: center;
  color: var(--medium-gray);
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

.staking-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.staking-plan {
  background: var(--white);
  border: 3px solid var(--light-gray);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.staking-plan:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px var(--shadow);
  border-color: var(--primary-color);
}

.staking-plan.featured {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(255, 153, 51, 0.05), rgba(255, 255, 255, 1));
}

.featured-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-primary);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.staking-plan h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: var(--dark-gray);
}

.apy-rate {
  font-size: 3rem;
  font-weight: 700;
  color: var(--success);
  margin-bottom: 1.5rem;
}

.plan-details {
  margin-bottom: 2rem;
}

.plan-details p {
  margin-bottom: 0.5rem;
  color: var(--medium-gray);
}

.stake-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

/* Transaction History */
.transaction-history {
  padding: 5rem 0;
  background: var(--light-gray);
}

.history-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  background: var(--white);
  border: 2px solid var(--light-gray);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: transparent;
}

.transactions-list {
  background: var(--white);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--shadow);
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background: var(--light-gray);
  border-radius: 10px;
}

.transaction-icon {
  width: 50px;
  height: 50px;
  background: var(--light-gray);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.transaction-details {
  flex: 1;
}

.transaction-details h4 {
  margin-bottom: 0.3rem;
  color: var(--dark-gray);
  font-weight: 600;
}

.transaction-details p {
  color: var(--medium-gray);
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.transaction-time {
  color: var(--medium-gray);
  font-size: 0.8rem;
}

.transaction-amount {
  font-weight: 700;
  font-size: 1.1rem;
  color: var(--dark-gray);
}

.transaction-amount.win {
  color: var(--success);
}

.transaction-amount.loss {
  color: var(--danger);
}

/* Footer */
.footer {
  background: var(--dark-gray);
  color: var(--white);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo-img {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.footer-links h4 {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.footer-links a {
  display: block;
  color: var(--white);
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--medium-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
  .wallet-hero h1 {
    font-size: 2rem;
  }
  
  .balance-cards {
    grid-template-columns: 1fr;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .staking-plans {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .history-filters {
    flex-direction: column;
    align-items: center;
  }
}
