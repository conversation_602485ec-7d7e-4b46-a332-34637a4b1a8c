import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

import '../../../marketplace/domain/models/product.dart';

part 'wishlist_item.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class WishlistItem extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String productId;

  @HiveField(2)
  final String name;

  @HiveField(3)
  final double price;

  @HiveField(4)
  final double originalPrice;

  @HiveField(5)
  final String currency;

  @HiveField(6)
  final String imageUrl;

  @HiveField(7)
  final String category;

  @HiveField(8)
  final String brand;

  @HiveField(9)
  final String vendorId;

  @HiveField(10)
  final String vendorName;

  @HiveField(11)
  final DateTime addedAt;

  @HiveField(12)
  final bool inStock;

  @HiveField(13)
  final double rating;

  @HiveField(14)
  final int reviewCount;

  const WishlistItem({
    required this.id,
    required this.productId,
    required this.name,
    required this.price,
    required this.originalPrice,
    this.currency = 'INR',
    required this.imageUrl,
    required this.category,
    required this.brand,
    required this.vendorId,
    required this.vendorName,
    required this.addedAt,
    this.inStock = true,
    this.rating = 0.0,
    this.reviewCount = 0,
  });

  factory WishlistItem.fromProduct(Product product) {
    return WishlistItem(
      id: '${product.id}_${DateTime.now().millisecondsSinceEpoch}',
      productId: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      currency: product.currency,
      imageUrl: product.primaryImage,
      category: product.category,
      brand: product.brand,
      vendorId: product.vendorId,
      vendorName: product.vendorName,
      addedAt: DateTime.now(),
      inStock: product.inStock,
      rating: product.rating,
      reviewCount: product.reviewCount,
    );
  }

  factory WishlistItem.fromJson(Map<String, dynamic> json) => _$WishlistItemFromJson(json);
  Map<String, dynamic> toJson() => _$WishlistItemToJson(this);

  // Helper getters
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedOriginalPrice => '₹${originalPrice.toStringAsFixed(2)}';

  bool get hasDiscount => originalPrice > price;
  
  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice - price) / originalPrice) * 100;
  }

  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(0)}% OFF';

  Product toProduct() {
    return Product(
      id: productId,
      name: name,
      description: '', // Will be fetched from API if needed
      price: price,
      originalPrice: originalPrice,
      currency: currency,
      images: [imageUrl],
      category: category,
      subcategory: '', // Will be fetched from API if needed
      brand: brand,
      rating: rating,
      reviewCount: reviewCount,
      inStock: inStock,
      stockQuantity: inStock ? 1 : 0,
      vendorId: vendorId,
      vendorName: vendorName,
      createdAt: addedAt,
      updatedAt: DateTime.now(),
    );
  }

  WishlistItem copyWith({
    String? id,
    String? productId,
    String? name,
    double? price,
    double? originalPrice,
    String? currency,
    String? imageUrl,
    String? category,
    String? brand,
    String? vendorId,
    String? vendorName,
    DateTime? addedAt,
    bool? inStock,
    double? rating,
    int? reviewCount,
  }) {
    return WishlistItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      currency: currency ?? this.currency,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      addedAt: addedAt ?? this.addedAt,
      inStock: inStock ?? this.inStock,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
    );
  }

  @override
  List<Object?> get props => [
        id,
        productId,
        name,
        price,
        originalPrice,
        currency,
        imageUrl,
        category,
        brand,
        vendorId,
        vendorName,
        addedAt,
        inStock,
        rating,
        reviewCount,
      ];
}

@HiveType(typeId: 4)
@JsonSerializable()
class Wishlist extends Equatable {
  @HiveField(0)
  final List<WishlistItem> items;

  @HiveField(1)
  final DateTime updatedAt;

  const Wishlist({
    this.items = const [],
    required this.updatedAt,
  });

  factory Wishlist.fromJson(Map<String, dynamic> json) => _$WishlistFromJson(json);
  Map<String, dynamic> toJson() => _$WishlistToJson(this);

  // Helper getters
  int get itemCount => items.length;
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  bool containsProduct(String productId) {
    return items.any((item) => item.productId == productId);
  }

  WishlistItem? getItemByProductId(String productId) {
    try {
      return items.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  List<WishlistItem> get inStockItems => items.where((item) => item.inStock).toList();
  List<WishlistItem> get outOfStockItems => items.where((item) => !item.inStock).toList();

  Wishlist copyWith({
    List<WishlistItem>? items,
    DateTime? updatedAt,
  }) {
    return Wishlist(
      items: items ?? this.items,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [items, updatedAt];
}
