import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/rider/presentation/pages/splash_page.dart';
import '../../features/rider/presentation/pages/auth/login_page.dart';
import '../../features/rider/presentation/pages/auth/register_page.dart';
import '../../features/rider/presentation/pages/kyc/kyc_page.dart';
import '../../features/rider/presentation/pages/dashboard/dashboard_page.dart';
import '../../features/rider/presentation/pages/orders/orders_page.dart';
import '../../features/rider/presentation/pages/profile/profile_page.dart';

final riderRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      // Splash
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const RiderSplashPage(),
      ),

      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const RiderLoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RiderRegisterPage(),
      ),
      GoRoute(
        path: '/phone-verification',
        name: 'phone-verification',
        builder: (context, state) {
          // final phoneNumber = state.extra as String? ?? '';
          return const Placeholder(); // RiderPhoneVerificationPage will be implemented
        },
      ),

      // KYC Process
      GoRoute(
        path: '/kyc',
        name: 'kyc',
        builder: (context, state) => const RiderKYCPage(),
        routes: [
          GoRoute(
            path: 'documents',
            name: 'kyc-documents',
            builder: (context, state) =>
                const Placeholder(), // RiderDocumentUploadPage(),
          ),
          GoRoute(
            path: 'vehicle',
            name: 'kyc-vehicle',
            builder: (context, state) =>
                const Placeholder(), // RiderVehicleRegistrationPage(),
          ),
          GoRoute(
            path: 'bank',
            name: 'kyc-bank',
            builder: (context, state) =>
                const Placeholder(), // RiderBankDetailsPage(),
          ),
        ],
      ),

      // Main Dashboard
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const RiderDashboardPage(),
        routes: [
          GoRoute(
            path: 'earnings',
            name: 'earnings',
            builder: (context, state) =>
                const Placeholder(), // RiderEarningsPage(),
          ),
          GoRoute(
            path: 'statistics',
            name: 'statistics',
            builder: (context, state) =>
                const Placeholder(), // RiderStatisticsPage(),
          ),
          GoRoute(
            path: 'performance',
            name: 'performance',
            builder: (context, state) =>
                const Placeholder(), // RiderPerformancePage(),
          ),
        ],
      ),

      // Orders Management
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const RiderOrdersPage(),
        routes: [
          GoRoute(
            path: 'detail/:orderId',
            name: 'order-detail',
            builder: (context, state) {
              // final orderId = state.pathParameters['orderId']!;
              return const Placeholder(); // RiderOrderDetailPage will be implemented
            },
          ),
          GoRoute(
            path: 'navigation/:orderId',
            name: 'order-navigation',
            builder: (context, state) {
              // final orderId = state.pathParameters['orderId']!;
              return const Placeholder(); // RiderNavigationPage will be implemented
            },
          ),
          GoRoute(
            path: 'history',
            name: 'order-history',
            builder: (context, state) =>
                const Placeholder(), // RiderOrderHistoryPage(),
          ),
        ],
      ),

      // Vehicle Management
      GoRoute(
        path: '/vehicle',
        name: 'vehicle',
        builder: (context, state) =>
            const Placeholder(), // RiderVehicleInfoPage(),
        routes: [
          GoRoute(
            path: 'documents',
            name: 'vehicle-documents',
            builder: (context, state) =>
                const Placeholder(), // RiderVehicleDocumentsPage(),
          ),
          GoRoute(
            path: 'maintenance',
            name: 'vehicle-maintenance',
            builder: (context, state) =>
                const Placeholder(), // RiderMaintenancePage(),
          ),
        ],
      ),

      // Insurance
      GoRoute(
        path: '/insurance',
        name: 'insurance',
        builder: (context, state) =>
            const Placeholder(), // RiderInsurancePage(),
        routes: [
          GoRoute(
            path: 'claims',
            name: 'insurance-claims',
            builder: (context, state) =>
                const Placeholder(), // RiderClaimsPage(),
          ),
        ],
      ),

      // Profile & Settings
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const RiderProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) =>
                const Placeholder(), // RiderSettingsPage(),
          ),
          GoRoute(
            path: 'help',
            name: 'help',
            builder: (context, state) =>
                const Placeholder(), // RiderHelpPage(),
          ),
          GoRoute(
            path: 'support',
            name: 'support',
            builder: (context, state) =>
                const Placeholder(), // RiderSupportPage(),
          ),
        ],
      ),

      // Emergency & Safety
      GoRoute(
        path: '/emergency',
        name: 'emergency',
        builder: (context, state) =>
            const Placeholder(), // RiderEmergencyPage(),
      ),

      // Training & Resources
      GoRoute(
        path: '/training',
        name: 'training',
        builder: (context, state) =>
            const Placeholder(), // RiderTrainingPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Page not found: ${state.uri}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods for Rider app
class RiderNavigation {
  static void goToSplash(BuildContext context) => context.go('/splash');
  static void goToLogin(BuildContext context) => context.go('/login');
  static void goToRegister(BuildContext context) => context.go('/register');
  static void goToPhoneVerification(BuildContext context, String phoneNumber) =>
      context.go('/phone-verification', extra: phoneNumber);
  static void goToKYC(BuildContext context) => context.go('/kyc');
  static void goToDashboard(BuildContext context) => context.go('/dashboard');
  static void goToOrders(BuildContext context) => context.go('/orders');
  static void goToOrderDetail(BuildContext context, String orderId) =>
      context.go('/orders/detail/$orderId');
  static void goToOrderNavigation(BuildContext context, String orderId) =>
      context.go('/orders/navigation/$orderId');
  static void goToVehicle(BuildContext context) => context.go('/vehicle');
  static void goToInsurance(BuildContext context) => context.go('/insurance');
  static void goToProfile(BuildContext context) => context.go('/profile');
  static void goToEmergency(BuildContext context) => context.go('/emergency');
}
