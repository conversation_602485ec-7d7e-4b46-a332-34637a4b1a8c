@echo off
title Projek Development Environment Setup
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              PROJEK DEVELOPMENT ENVIRONMENT SETUP           ║
echo ║                Complete Development Configuration            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Step 1: Configuring development environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
set FLUTTER_WEB_USE_SKIA=true
set FLUTTER_WEB_AUTO_DETECT=true
set FLUTTER_WEB_CANVASKIT_URL=https://unpkg.com/canvaskit-wasm@latest/bin/
echo ✅ Environment variables configured

echo.
echo 📱 Step 2: Checking device connection...
echo Scanning for connected devices...
flutter devices
echo.

echo 🔍 Step 3: Verifying Flutter installation...
echo Running Flutter doctor...
flutter doctor --verbose
echo.

echo 🧹 Step 4: Cleaning previous builds...
echo Removing old build artifacts...
flutter clean
echo ✅ Clean completed

echo.
echo 📦 Step 5: Installing dependencies...
echo Getting fresh dependencies...
flutter pub get
echo ✅ Dependencies installed

echo.
echo 🔨 Step 6: Pre-building for Android...
echo Preparing Android build tools...
flutter precache --android
echo ✅ Android precache completed

echo.
echo 🎯 Step 7: Verifying project structure...
if exist "lib\main_user.dart" (
    echo ✅ User app entry point found
) else (
    echo ❌ User app entry point missing
)

if exist "firebase_options_user.dart" (
    echo ✅ Firebase configuration found
) else (
    echo ❌ Firebase configuration missing
)

if exist "android\app\build.gradle" (
    echo ✅ Android configuration found
) else (
    echo ❌ Android configuration missing
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 DEVELOPMENT ENVIRONMENT READY!              ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  📱 Your Android Device: V2130 (1397182984001HG)            ║
echo ║  🎯 Target App: Projek User App                             ║
echo ║  ⚡ Hot Reload: Enabled                                     ║
echo ║  🔧 Custom Pub Cache: E:\Appdata\flutter_pub_cache         ║
echo ║                                                              ║
echo ║  🚀 START DEVELOPMENT:                                      ║
echo ║     .\dev_user_app.bat                                      ║
echo ║                                                              ║
echo ║  🔧 MANUAL COMMAND:                                         ║
echo ║     flutter run --target lib/main_user.dart \              ║
echo ║                 -d 1397182984001HG --hot                   ║
echo ║                                                              ║
echo ║  ⚡ HOT RELOAD COMMANDS:                                    ║
echo ║     r = Hot reload (instant UI changes)                    ║
echo ║     R = Hot restart (full app restart)                     ║
echo ║     h = Help and more commands                              ║
echo ║     q = Quit development session                            ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Press any key to continue...
pause >nul
