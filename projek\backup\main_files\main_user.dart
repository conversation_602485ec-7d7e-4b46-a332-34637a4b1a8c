import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:projek/core/config/app_config.dart' show AppConfig, AppType;
import 'package:projek/core/config/environment.dart' show EnvironmentConfig, Environment;
import 'package:projek/core/database/hive_service.dart' show HiveService;
import 'package:projek/core/providers/theme_provider.dart' show themeModeProvider;
import 'package:projek/core/router/app_router.dart' show appRouterProvider;
import 'package:projek/core/services/analytics_service.dart' show AnalyticsService;
import 'package:projek/core/services/multi_app_integration_service.dart' hide AppType;
import 'package:projek/core/services/notification_service.dart' show NotificationService;
import 'package:projek/core/theme/app_theme.dart' show AppTheme;
import '../config_files/firebase_options.dart' show DefaultFirebaseOptionsUser;
import 'firebase_options.dart';
import 'core/config/app_config.dart';
import 'core/config/environment.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/theme_provider.dart';
import 'core/router/app_router.dart';
import 'core/services/notification_service.dart';
import 'core/services/analytics_service.dart';
import 'core/database/hive_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set environment
  EnvironmentConfig.setEnvironment(Environment.development);

  // Set app type
  AppConfig.setAppType(AppType.user);

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptionsUser.currentPlatform,
  );

  // Initialize Hive
  await Hive.initFlutter();
  await HiveService.initialize();

  // Initialize services
  await NotificationService.initialize();
  await AnalyticsService.initialize();

  runApp(const ProviderScope(child: ProjekUserApp()));
}

class ProjekUserApp extends ConsumerWidget {
  const ProjekUserApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp.router(
      title: AppConfig.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0), // Prevent text scaling
          ),
          child: child!,
        );
      },
    );
  }
}
