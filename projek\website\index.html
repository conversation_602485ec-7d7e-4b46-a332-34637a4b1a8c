<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> PROJEK Super app - Connecting All of India | One Platform, Infinite Possibilities</title>
  <meta name="description" content="Super Vision connects every Indian - users, riders, sellers, and service providers on one platform. Earn, Learn, Grow with India's most trusted service app.">
  <meta name="keywords" content="Super Vision, India service app, delivery, riders, sellers, booking, Monuj Protim Saikia, Projek, Assam">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="css/home.css">
  <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
</head>
<body>
  <!-- Navigation Header -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <img src="assets/image/logo/app_logo.png" alt="PROJEK Logo" class="logo-image">
        <span class="logo-text">PROJEK </span>
        <span class="logo-tagline">Connecting India </span>
      </div>

      <div class="nav-menu">
        <a href="#home" class="nav-link active">Home</a>
        <a href="#about" class="nav-link">About</a>
        <a href="#services" class="nav-link">Services</a>
        <a href="#apps" class="nav-link">Apps</a>
        <a href="founder.html" class="nav-link">Founder</a>
        <a href="booking.html" class="nav-link">Book Now</a>
        <a href="#gaming" class="nav-link gaming-btn">🎮 Gaming</a>
        <a href="wallet.html" class="nav-link wallet-btn">💰 Wallet</a>
        <a href="#contact" class="nav-link">Contact</a>
        <a href="admin/login.html" class="nav-link admin-btn">Admin</a>
      </div>

      <div class="nav-toggle">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-background">
      <div class="hero-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>

    <div class="hero-container">
      <div class="hero-content">
        <div class="hero-text">
          <div class="hero-badge">
            <span class="badge-icon">🇮🇳</span>
            <span>Made in India, For India</span>
          </div>

          <h1 class="hero-title">
            <span class="title-main">👁️ PROJEK Super Vision</span>
            <span class="title-sub">हर भारतीय का सपना, एक ही प्लेटफॉर्म में!</span>
            <span class="title-english">Every Indian's Dream, On One Platform!</span>
          </h1>

          <p class="hero-description">
            Connect with millions of users, riders, and service providers across India.
            Whether you want to <strong>earn, learn, or grow</strong> - Super Vision is your gateway to success!
          </p>

          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">45.2K+</span>
              <span class="stat-label">Happy Users</span>
            </div>
            <div class="stat">
              <span class="stat-number">2.1K+</span>
              <span class="stat-label">Active Riders</span>
            </div>
            <div class="stat">
              <span class="stat-number">1.2K+</span>
              <span class="stat-label">Trusted Sellers</span>
            </div>
          </div>

          <div class="hero-buttons">
            <a href="booking.html" class="btn btn-primary">
              <span class="material-icons">explore</span>
              Explore Services
            </a>
            <a href="#apps" class="btn btn-secondary">
              <span class="material-icons">download</span>
              Download Apps
            </a>
          </div>
        </div>

        <div class="hero-visual">
          <div class="phone-mockup">
            <div class="phone-screen">
              <div class="app-interface">
                <div class="app-header">
                  <img src="assets/image/logo/app_logo.png" alt="PROJEK Logo" class="app-logo-img">
                  <span class="app-name">PROJEK</span>
                </div>
                <div class="app-content">
                  <div class="service-card">
                    <span class="service-icon">🍕</span>
                    <span class="service-name">Food Delivery</span>
                  </div>
                  <div class="service-card">
                    <span class="service-icon">🚴‍♂️</span>
                    <span class="service-name">Ride Booking</span>
                  </div>
                  <div class="service-card">
                    <span class="service-icon">🔧</span>
                    <span class="service-name">Home Services</span>
                  </div>
                  <div class="service-card">
                    <span class="service-icon">🛒</span>
                    <span class="service-name">Shopping</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Founder Introduction -->
  <section class="founder-intro-section">
    <div class="container">
      <div class="founder-intro-content">
        <div class="founder-image-small">
          <img src="assets/image/founder/monuj.jpg" alt="Monuj Protim Saikia">
          <div class="founder-badge">
            <span class="material-icons">verified</span>
          </div>
        </div>

        <div class="founder-message">
          <h3>Message from our Co-Founder</h3>
          <blockquote>
            "Namaste India! I'm <strong>Monuj Protim Saikia</strong> from Golaghat, Assam.
            👁️ PROJEK Super Vision isn't just an app - it's our mission to connect every corner of India,
            empowering every skilled individual to earn, grow, and succeed together!"
          </blockquote>
          <div class="founder-signature">
            <span class="signature-name">- Monuj Protim Saikia</span>
            <span class="signature-title">Co-Founder, Projek</span>
          </div>
          <a href="founder.html" class="founder-link">
            <span class="material-icons">person</span>
            Meet Our Founder
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Why Choose 👁️ PROJEK Super Vision?</h2>
        <p class="section-subtitle">More than just an app - we're building India's digital future</p>
      </div>

      <div class="about-content">
        <div class="about-cards">
          <div class="about-card">
            <div class="card-icon">
              <span class="material-icons">connect_without_contact</span>
            </div>
            <h3>Connect All India</h3>
            <p>From Kashmir to Kanyakumari, from Gujarat to Assam - we're connecting every state, every city, every village in one platform.</p>
          </div>

          <div class="about-card">
            <div class="card-icon">
              <span class="material-icons">work</span>
            </div>
            <h3>Create Employment</h3>
            <p>Empowering millions of Indians with flexible work opportunities. Be your own boss, work on your schedule, earn with dignity.</p>
          </div>

          <div class="about-card">
            <div class="card-icon">
              <span class="material-icons">trending_up</span>
            </div>
            <h3>Digital Growth</h3>
            <p>Supporting local businesses to go digital, helping them reach new customers and grow beyond geographical boundaries.</p>
          </div>

          <div class="about-card">
            <div class="card-icon">
              <span class="material-icons">security</span>
            </div>
            <h3>Trust & Safety</h3>
            <p>Advanced verification, secure payments, and 24/7 support ensure safe transactions for all users across the platform.</p>
          </div>
        </div>

        <div class="about-vision">
          <div class="vision-content">
            <h3>🎯 Our Mission</h3>
            <p class="mission-text">
              "To create an ecosystem where every Indian can <strong>earn with dignity</strong>,
              <strong>access services easily</strong>, and <strong>grow their business digitally</strong> -
              all while staying connected to their roots and culture."
            </p>

            <div class="mission-stats">
              <div class="mission-stat">
                <span class="stat-icon">🏆</span>
                <span class="stat-text">Trusted by 45K+ Users</span>
              </div>
              <div class="mission-stat">
                <span class="stat-icon">⭐</span>
                <span class="stat-text">4.8/5 Average Rating</span>
              </div>
              <div class="mission-stat">
                <span class="stat-icon">🚀</span>
                <span class="stat-text">Growing 200% Monthly</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="services-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">All Services, One Platform</h2>
        <p class="section-subtitle">Everything you need, delivered to your doorstep</p>
      </div>

      <div class="services-grid">
        <div class="service-category">
          <div class="category-header">
            <span class="category-icon">🍕</span>
            <h3>Food & Dining</h3>
          </div>
          <div class="category-services">
            <span class="service-item">Restaurant Delivery</span>
            <span class="service-item">Grocery Shopping</span>
            <span class="service-item">Fresh Vegetables</span>
            <span class="service-item">Sweets & Snacks</span>
          </div>
          <a href="booking.html?category=food" class="category-btn">Order Now</a>
        </div>

        <div class="service-category">
          <div class="category-header">
            <span class="category-icon">🚗</span>
            <h3>Transportation</h3>
          </div>
          <div class="category-services">
            <span class="service-item">Bike Rides</span>
            <span class="service-item">Car Booking</span>
            <span class="service-item">Auto Rickshaw</span>
            <span class="service-item">Goods Transport</span>
          </div>
          <a href="booking.html?category=transport" class="category-btn">Book Ride</a>
        </div>

        <div class="service-category">
          <div class="category-header">
            <span class="category-icon">🔧</span>
            <h3>Home Services</h3>
          </div>
          <div class="category-services">
            <span class="service-item">Plumber</span>
            <span class="service-item">Electrician</span>
            <span class="service-item">Cleaning</span>
            <span class="service-item">Appliance Repair</span>
          </div>
          <a href="booking.html?category=home" class="category-btn">Get Service</a>
        </div>

        <div class="service-category">
          <div class="category-header">
            <span class="category-icon">📚</span>
            <h3>Education & Health</h3>
          </div>
          <div class="category-services">
            <span class="service-item">Home Tuition</span>
            <span class="service-item">Doctor Consultation</span>
            <span class="service-item">Nursing Care</span>
            <span class="service-item">Skill Training</span>
          </div>
          <a href="booking.html?category=education" class="category-btn">Book Now</a>
        </div>
      </div>

      <div class="services-cta">
        <h3>Can't find what you're looking for?</h3>
        <p>We're constantly adding new services based on your needs!</p>
        <a href="booking.html" class="btn btn-primary">
          <span class="material-icons">search</span>
          Explore All Services
        </a>
      </div>
    </div>
  </section>

  <!-- Apps Section -->
  <section id="apps" class="apps-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Three Apps, One Vision</h2>
        <p class="section-subtitle">Choose your path to success with 👁️ PROJEK Super Vision</p>
      </div>

      <div class="apps-grid">
        <div class="app-card user-app">
          <div class="app-icon">
            <span class="material-icons">person</span>
          </div>
          <div class="app-content">
            <h3>User App</h3>
            <p class="app-tagline">"Click करो, Service पाओ, Happy रहो!"</p>
            <p class="app-description">
              Get any service at your fingertips. From food delivery to home repairs,
              from tutoring to healthcare - everything you need, when you need it.
            </p>

            <div class="app-features">
              <div class="feature">
                <span class="feature-icon">🔍</span>
                <span>Find Services Instantly</span>
              </div>
              <div class="feature">
                <span class="feature-icon">💳</span>
                <span>Secure Payments</span>
              </div>
              <div class="feature">
                <span class="feature-icon">⭐</span>
                <span>Rate & Review</span>
              </div>
              <div class="feature">
                <span class="feature-icon">📱</span>
                <span>24/7 Support</span>
              </div>
            </div>

            <div class="app-download">
              <a href="#" class="download-btn android">
                <img src="assets/images/google-play.png" alt="Download on Google Play" onerror="this.style.display='none'">
                <span>Download for Android</span>
              </a>
              <a href="#" class="download-btn ios">
                <img src="assets/images/app-store.png" alt="Download on App Store" onerror="this.style.display='none'">
                <span>Download for iOS</span>
              </a>
            </div>
          </div>
        </div>

        <div class="app-card rider-app">
          <div class="app-icon">
            <span class="material-icons">delivery_dining</span>
          </div>
          <div class="app-content">
            <h3>Rider App</h3>
            <p class="app-tagline">"Ride करो, Earn करो, Smile करो!"</p>
            <p class="app-description">
              Join thousands of riders earning daily income with flexible timings.
              Be your own boss, work when you want, earn with respect and dignity.
            </p>

            <div class="app-features">
              <div class="feature">
                <span class="feature-icon">💰</span>
                <span>Daily Earnings</span>
              </div>
              <div class="feature">
                <span class="feature-icon">⏰</span>
                <span>Flexible Timing</span>
              </div>
              <div class="feature">
                <span class="feature-icon">📍</span>
                <span>GPS Navigation</span>
              </div>
              <div class="feature">
                <span class="feature-icon">🏆</span>
                <span>Performance Rewards</span>
              </div>
            </div>

            <div class="app-download">
              <a href="#" class="download-btn android">
                <img src="assets/images/google-play.png" alt="Download on Google Play" onerror="this.style.display='none'">
                <span>Download for Android</span>
              </a>
              <a href="#" class="download-btn ios">
                <img src="assets/images/app-store.png" alt="Download on App Store" onerror="this.style.display='none'">
                <span>Download for iOS</span>
              </a>
            </div>
          </div>
        </div>

        <div class="app-card seller-app">
          <div class="app-icon">
            <span class="material-icons">store</span>
          </div>
          <div class="app-content">
            <h3>Seller App</h3>
            <p class="app-tagline">"Sell करो, Grow करो, Success पाओ!"</p>
            <p class="app-description">
              Transform your business digitally. Reach millions of customers,
              manage orders efficiently, and grow beyond geographical boundaries.
            </p>

            <div class="app-features">
              <div class="feature">
                <span class="feature-icon">🛒</span>
                <span>Easy Store Setup</span>
              </div>
              <div class="feature">
                <span class="feature-icon">📊</span>
                <span>Sales Analytics</span>
              </div>
              <div class="feature">
                <span class="feature-icon">🚚</span>
                <span>Order Management</span>
              </div>
              <div class="feature">
                <span class="feature-icon">💼</span>
                <span>Business Growth</span>
              </div>
            </div>

            <div class="app-download">
              <a href="#" class="download-btn android">
                <img src="assets/images/google-play.png" alt="Download on Google Play" onerror="this.style.display='none'">
                <span>Download for Android</span>
              </a>
              <a href="#" class="download-btn ios">
                <img src="assets/images/app-store.png" alt="Download on App Store" onerror="this.style.display='none'">
                <span>Download for iOS</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="apps-benefits">
        <h3>🎁 Special Launch Offers</h3>
        <div class="benefits-grid">
          <div class="benefit">
            <span class="benefit-icon">🆓</span>
            <h4>Zero Commission</h4>
            <p>First month completely free for all new riders and sellers</p>
          </div>
          <div class="benefit">
            <span class="benefit-icon">🎯</span>
            <h4>Instant Approval</h4>
            <p>Quick verification and same-day approval for genuine users</p>
          </div>
          <div class="benefit">
            <span class="benefit-icon">📚</span>
            <h4>Free Training</h4>
            <p>Complete digital training and skill development programs</p>
          </div>
          <div class="benefit">
            <span class="benefit-icon">🏅</span>
            <h4>Recognition Program</h4>
            <p>Monthly awards and recognition for top performers</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Gaming & Wallet Section -->
  <section id="gaming" class="gaming-wallet-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">🎮 Gaming & 💰 Wallet</h2>
        <p class="section-subtitle">Play, Earn, and Manage your ProjekCoin in one place</p>
      </div>

      <div class="gaming-wallet-grid">
        <!-- Wallet Card -->
        <div class="wallet-card">
          <div class="card-header">
            <h3>💰 PROJEK Wallet</h3>
            <div class="balance-toggle">
              <button class="visibility-btn" onclick="toggleWalletBalance()">
                <span class="material-icons" id="walletVisibility">visibility</span>
              </button>
            </div>
          </div>

          <div class="wallet-balances">
            <div class="balance-item">
              <span class="balance-label">ProjekCoin (PC)</span>
              <span class="balance-amount" id="pcBalance">1,250 PC</span>
              <span class="balance-inr">≈ ₹1,250</span>
            </div>
            <div class="balance-item">
              <span class="balance-label">Gaming Balance</span>
              <span class="balance-amount" id="gamingBalance">450 PC</span>
              <span class="balance-inr">≈ ₹450</span>
            </div>
          </div>

          <div class="wallet-actions">
            <button class="wallet-btn primary" onclick="openDepositModal()">
              <span class="material-icons">add</span>
              Deposit INR
            </button>
            <button class="wallet-btn secondary" onclick="openWithdrawModal()">
              <span class="material-icons">account_balance</span>
              Withdraw
            </button>
          </div>

          <div class="conversion-rate">
            <span class="rate-text">💱 1 INR = 1 PC</span>
            <span class="rate-status">🟢 Live</span>
          </div>
        </div>

        <!-- Gaming Card -->
        <div class="gaming-card">
          <div class="card-header">
            <h3>🎮 Spin-to-Earn Game</h3>
            <div class="game-status">
              <span class="status-badge">🔥 Live</span>
            </div>
          </div>

          <div class="wheel-container-mini">
            <div class="mini-wheel" id="miniWheel">
              <div class="wheel-segment">₹10</div>
              <div class="wheel-segment">₹25</div>
              <div class="wheel-segment">₹50</div>
              <div class="wheel-segment">₹100</div>
              <div class="wheel-segment">₹250</div>
              <div class="wheel-segment">₹500</div>
              <div class="wheel-segment">Try Again</div>
              <div class="wheel-segment">₹1500</div>
            </div>
            <div class="mini-pointer"></div>
          </div>

          <div class="game-options">
            <div class="spin-option">
              <h4>🎯 Regular Spin</h4>
              <p>Entry: ₹10 | Max Win: ₹500</p>
              <button class="game-btn" onclick="handleSpin('regular')">Spin Now</button>
            </div>
            <div class="spin-option">
              <h4>🎯 Max Spin</h4>
              <p>Entry: ₹50 | Max Win: ₹1500</p>
              <button class="game-btn max" onclick="handleSpin('max')">Max Spin</button>
            </div>
          </div>
        </div>

        <!-- Staking Card -->
        <div class="staking-card">
          <div class="card-header">
            <h3>📈 Stake & Earn</h3>
            <div class="apy-badge">12% APY</div>
          </div>

          <div class="staking-info">
            <div class="staked-amount">
              <span class="label">Staked Amount</span>
              <span class="amount">2,100 PC</span>
              <span class="value">≈ ₹2,100</span>
            </div>
            <div class="daily-reward">
              <span class="label">Daily Reward</span>
              <span class="reward">+6.9 PC</span>
            </div>
          </div>

          <div class="staking-plans">
            <div class="plan bronze">
              <span class="plan-name">🥉 Bronze</span>
              <span class="plan-apy">8% APY</span>
            </div>
            <div class="plan silver active">
              <span class="plan-name">🥈 Silver</span>
              <span class="plan-apy">12% APY</span>
            </div>
            <div class="plan gold">
              <span class="plan-name">🥇 Gold</span>
              <span class="plan-apy">18% APY</span>
            </div>
          </div>

          <button class="stake-btn" onclick="openStakeModal()">
            <span class="material-icons">trending_up</span>
            Stake More PC
          </button>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="quick-stats">
        <div class="stat-item">
          <span class="stat-number">₹4,650</span>
          <span class="stat-label">Total Portfolio</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">127</span>
          <span class="stat-label">Games Played</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">68%</span>
          <span class="stat-label">Win Rate</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">₹2,450</span>
          <span class="stat-label">Total Winnings</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Get In Touch</h2>
        <p class="section-subtitle">Ready to join the 👁️ PROJEK Super Vision family? We're here to help!</p>
      </div>

      <div class="contact-content">
        <div class="contact-info">
          <div class="contact-card">
            <div class="contact-icon">
              <span class="material-icons">location_on</span>
            </div>
            <h3>Our Office</h3>
            <p>Golaghat, Assam, India</p>
            <p>Serving all of India 🇮🇳</p>
          </div>

          <div class="contact-card">
            <div class="contact-icon">
              <span class="material-icons">email</span>
            </div>
            <h3>Email Us</h3>
            <p><EMAIL></p>
            <p><EMAIL></p>
          </div>

          <div class="contact-card">
            <div class="contact-icon">
              <span class="material-icons">phone</span>
            </div>
            <h3>Call Us</h3>
            <p>+91 98765 43210</p>
            <p>24/7 Support Available</p>
          </div>
        </div>

        <div class="contact-form">
          <h3>Send us a Message</h3>
          <form id="contactForm">
            <div class="form-group">
              <input type="text" placeholder="Your Name" required>
            </div>
            <div class="form-group">
              <input type="email" placeholder="Your Email" required>
            </div>
            <div class="form-group">
              <input type="tel" placeholder="Your Phone Number">
            </div>
            <div class="form-group">
              <select required>
                <option value="">I want to...</option>
                <option value="user">Use Services (User App)</option>
                <option value="rider">Become a Rider</option>
                <option value="seller">Start Selling</option>
                <option value="partner">Business Partnership</option>
                <option value="support">Get Support</option>
              </select>
            </div>
            <div class="form-group">
              <textarea placeholder="Your Message" rows="4" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">
              <span class="material-icons">send</span>
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-main">
          <div class="footer-logo">
            <span class="logo-icon">👁️</span>
            <span class="logo-text">Super Vision</span>
            <p class="footer-tagline">Connecting All of India</p>
          </div>

          <div class="footer-description">
            <p>
              Super Vision is India's most comprehensive service platform, connecting users,
              riders, sellers, and service providers across the nation. From Assam to every
              corner of India, we're building a digital ecosystem where everyone can earn,
              learn, and grow together.
            </p>

            <div class="social-links">
              <a href="#" class="social-link">
                <span class="material-icons">facebook</span>
              </a>
              <a href="#" class="social-link">
                <span class="material-icons">twitter</span>
              </a>
              <a href="#" class="social-link">
                <span class="material-icons">instagram</span>
              </a>
              <a href="#" class="social-link">
                <span class="material-icons">linkedin</span>
              </a>
            </div>
          </div>
        </div>

        <div class="footer-links">
          <div class="footer-column">
            <h4>Platform</h4>
            <a href="#home">Home</a>
            <a href="#about">About Us</a>
            <a href="#services">Services</a>
            <a href="founder.html">Our Founder</a>
            <a href="booking.html">Book Services</a>
          </div>

          <div class="footer-column">
            <h4>Apps</h4>
            <a href="#apps">User App</a>
            <a href="#apps">Rider App</a>
            <a href="#apps">Seller App</a>
            <a href="#">Download Android</a>
            <a href="#">Download iOS</a>
          </div>

          <div class="footer-column">
            <h4>Support</h4>
            <a href="#contact">Contact Us</a>
            <a href="#">Help Center</a>
            <a href="#">Safety Guidelines</a>
            <a href="#">Terms of Service</a>
            <a href="#">Privacy Policy</a>
          </div>

          <div class="footer-column">
            <h4>Business</h4>
            <a href="#">Become a Rider</a>
            <a href="#">Become a Seller</a>
            <a href="#">Business Solutions</a>
            <a href="#">Partner with Us</a>
            <a href="admin/login.html">Admin Portal</a>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; 2024 Super Vision by Projek. Made with ❤️ in Assam, India</p>
          <div class="footer-badges">
            <span class="badge">🇮🇳 Proudly Indian</span>
            <span class="badge">🔒 Secure Platform</span>
            <span class="badge">⭐ 4.8/5 Rated</span>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Floating Action Buttons -->
  <div class="floating-actions">
    <a href="booking.html" class="fab primary" title="Book Service">
      <span class="material-icons">add</span>
    </a>
    <a href="#contact" class="fab secondary" title="Contact Us">
      <span class="material-icons">chat</span>
    </a>
    <a href="admin/login.html" class="fab admin" title="Admin Login">
      <span class="material-icons">admin_panel_settings</span>
    </a>
  </div>

  <!-- Scripts -->
  <script src="js/home.js"></script>
</body>
</html>