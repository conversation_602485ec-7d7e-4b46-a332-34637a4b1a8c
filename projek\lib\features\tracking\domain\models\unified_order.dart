import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'unified_order.g.dart';

@HiveType(typeId: 50)
enum OrderType {
  @HiveField(0)
  marketplace,
  @HiveField(1)
  service,
  @HiveField(2)
  food,
  @HiveField(3)
  grocery,
  @HiveField(4)
  medicine,
  @HiveField(5)
  emergency,
}

@HiveType(typeId: 51)
enum OrderStatus {
  @HiveField(0)
  created,
  @HiveField(1)
  confirmed,
  @HiveField(2)
  preparing,
  @HiveField(3)
  ready,
  @HiveField(4)
  assigned,
  @HiveField(5)
  pickedUp,
  @HiveField(6)
  inTransit,
  @HiveField(7)
  nearDestination,
  @HiveField(8)
  delivered,
  @HiveField(9)
  completed,
  @HiveField(10)
  cancelled,
  @HiveField(11)
  failed,
  @HiveField(12)
  refunded,
}

@HiveType(typeId: 52)
enum DeliveryType {
  @HiveField(0)
  standard,
  @HiveField(1)
  express,
  @HiveField(2)
  scheduled,
  @HiveField(3)
  emergency,
  @HiveField(4)
  contactless,
}

@HiveType(typeId: 53)
@JsonSerializable()
class LocationPoint extends Equatable {
  @HiveField(0)
  final double latitude;

  @HiveField(1)
  final double longitude;

  @HiveField(2)
  final String? address;

  @HiveField(3)
  final String? landmark;

  @HiveField(4)
  final DateTime timestamp;

  const LocationPoint({
    required this.latitude,
    required this.longitude,
    this.address,
    this.landmark,
    required this.timestamp,
  });

  factory LocationPoint.fromJson(Map<String, dynamic> json) =>
      _$LocationPointFromJson(json);

  Map<String, dynamic> toJson() => _$LocationPointToJson(this);

  @override
  List<Object?> get props => [
    latitude,
    longitude,
    address,
    landmark,
    timestamp,
  ];
}

@HiveType(typeId: 54)
@JsonSerializable()
class OrderMilestone extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final OrderStatus status;

  @HiveField(2)
  final DateTime timestamp;

  @HiveField(3)
  final String? description;

  @HiveField(4)
  final String? performedBy;

  @HiveField(5)
  final LocationPoint? location;

  @HiveField(6)
  final Map<String, dynamic> metadata;

  const OrderMilestone({
    required this.id,
    required this.status,
    required this.timestamp,
    this.description,
    this.performedBy,
    this.location,
    this.metadata = const {},
  });

  factory OrderMilestone.fromJson(Map<String, dynamic> json) =>
      _$OrderMilestoneFromJson(json);

  Map<String, dynamic> toJson() => _$OrderMilestoneToJson(this);

  @override
  List<Object?> get props => [
    id,
    status,
    timestamp,
    description,
    performedBy,
    location,
    metadata,
  ];
}

@HiveType(typeId: 55)
@JsonSerializable()
class DeliveryTracking extends Equatable {
  @HiveField(0)
  final String? riderId;

  @HiveField(1)
  final String? riderName;

  @HiveField(2)
  final String? riderPhone;

  @HiveField(3)
  final String? vehicleNumber;

  @HiveField(4)
  final LocationPoint? currentLocation;

  @HiveField(5)
  final List<LocationPoint> route;

  @HiveField(6)
  final DateTime? estimatedDeliveryTime;

  @HiveField(7)
  final double? distanceRemaining;

  @HiveField(8)
  final int? timeRemaining; // in minutes

  @HiveField(9)
  final DeliveryType deliveryType;

  @HiveField(10)
  final Map<String, dynamic> additionalInfo;

  const DeliveryTracking({
    this.riderId,
    this.riderName,
    this.riderPhone,
    this.vehicleNumber,
    this.currentLocation,
    this.route = const [],
    this.estimatedDeliveryTime,
    this.distanceRemaining,
    this.timeRemaining,
    this.deliveryType = DeliveryType.standard,
    this.additionalInfo = const {},
  });

  factory DeliveryTracking.fromJson(Map<String, dynamic> json) =>
      _$DeliveryTrackingFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryTrackingToJson(this);

  @override
  List<Object?> get props => [
    riderId,
    riderName,
    riderPhone,
    vehicleNumber,
    currentLocation,
    route,
    estimatedDeliveryTime,
    distanceRemaining,
    timeRemaining,
    deliveryType,
    additionalInfo,
  ];
}

@HiveType(typeId: 56)
@JsonSerializable()
class OrderParticipant extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? phone;

  @HiveField(3)
  final String? email;

  @HiveField(4)
  final String? imageUrl;

  @HiveField(5)
  final String role; // user, seller, rider

  @HiveField(6)
  final double? rating;

  @HiveField(7)
  final Map<String, dynamic> additionalInfo;

  const OrderParticipant({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.imageUrl,
    required this.role,
    this.rating,
    this.additionalInfo = const {},
  });

  factory OrderParticipant.fromJson(Map<String, dynamic> json) =>
      _$OrderParticipantFromJson(json);

  Map<String, dynamic> toJson() => _$OrderParticipantToJson(this);

  @override
  List<Object?> get props => [
    id,
    name,
    phone,
    email,
    imageUrl,
    role,
    rating,
    additionalInfo,
  ];
}

@HiveType(typeId: 57)
@JsonSerializable()
class UnifiedOrder extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String orderNumber;

  @HiveField(2)
  final OrderType type;

  @HiveField(3)
  final OrderStatus status;

  @HiveField(4)
  final OrderParticipant user;

  @HiveField(5)
  final OrderParticipant seller;

  @HiveField(6)
  final OrderParticipant? rider;

  @HiveField(7)
  final LocationPoint pickupLocation;

  @HiveField(8)
  final LocationPoint deliveryLocation;

  @HiveField(9)
  final DeliveryTracking? tracking;

  @HiveField(10)
  final List<OrderMilestone> milestones;

  @HiveField(11)
  final double totalAmount;

  @HiveField(12)
  final double deliveryFee;

  @HiveField(13)
  final double taxes;

  @HiveField(14)
  final String currency;

  @HiveField(15)
  final String? paymentId;

  @HiveField(16)
  final String paymentStatus;

  @HiveField(17)
  final DateTime createdAt;

  @HiveField(18)
  final DateTime updatedAt;

  @HiveField(19)
  final DateTime? scheduledDeliveryTime;

  @HiveField(20)
  final String? specialInstructions;

  @HiveField(21)
  final List<String> tags;

  @HiveField(22)
  final int priority; // 1-5, 5 being highest

  @HiveField(23)
  final Map<String, dynamic> orderData; // Original order details

  @HiveField(24)
  final Map<String, dynamic> metadata;

  const UnifiedOrder({
    required this.id,
    required this.orderNumber,
    required this.type,
    required this.status,
    required this.user,
    required this.seller,
    this.rider,
    required this.pickupLocation,
    required this.deliveryLocation,
    this.tracking,
    this.milestones = const [],
    required this.totalAmount,
    this.deliveryFee = 0.0,
    this.taxes = 0.0,
    this.currency = 'INR',
    this.paymentId,
    this.paymentStatus = 'pending',
    required this.createdAt,
    required this.updatedAt,
    this.scheduledDeliveryTime,
    this.specialInstructions,
    this.tags = const [],
    this.priority = 3,
    this.orderData = const {},
    this.metadata = const {},
  });

  factory UnifiedOrder.fromJson(Map<String, dynamic> json) =>
      _$UnifiedOrderFromJson(json);

  Map<String, dynamic> toJson() => _$UnifiedOrderToJson(this);

  /// Create from Firestore document
  factory UnifiedOrder.fromFirestore(dynamic doc) {
    if (doc == null || !doc.exists) {
      throw Exception('Document does not exist');
    }

    final data = doc.data() as Map<String, dynamic>;
    return UnifiedOrder.fromJson({'id': doc.id, ...data});
  }

  UnifiedOrder copyWith({
    String? id,
    String? orderNumber,
    OrderType? type,
    OrderStatus? status,
    OrderParticipant? user,
    OrderParticipant? seller,
    OrderParticipant? rider,
    LocationPoint? pickupLocation,
    LocationPoint? deliveryLocation,
    DeliveryTracking? tracking,
    List<OrderMilestone>? milestones,
    double? totalAmount,
    double? deliveryFee,
    double? taxes,
    String? currency,
    String? paymentId,
    String? paymentStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? scheduledDeliveryTime,
    String? specialInstructions,
    List<String>? tags,
    int? priority,
    Map<String, dynamic>? orderData,
    Map<String, dynamic>? metadata,
  }) {
    return UnifiedOrder(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      type: type ?? this.type,
      status: status ?? this.status,
      user: user ?? this.user,
      seller: seller ?? this.seller,
      rider: rider ?? this.rider,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      tracking: tracking ?? this.tracking,
      milestones: milestones ?? this.milestones,
      totalAmount: totalAmount ?? this.totalAmount,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      taxes: taxes ?? this.taxes,
      currency: currency ?? this.currency,
      paymentId: paymentId ?? this.paymentId,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      scheduledDeliveryTime:
          scheduledDeliveryTime ?? this.scheduledDeliveryTime,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      tags: tags ?? this.tags,
      priority: priority ?? this.priority,
      orderData: orderData ?? this.orderData,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  bool get isActive => [
    OrderStatus.created,
    OrderStatus.confirmed,
    OrderStatus.preparing,
    OrderStatus.ready,
    OrderStatus.assigned,
    OrderStatus.pickedUp,
    OrderStatus.inTransit,
    OrderStatus.nearDestination,
  ].contains(status);

  bool get isCompleted =>
      [OrderStatus.delivered, OrderStatus.completed].contains(status);

  bool get isCancelled => [
    OrderStatus.cancelled,
    OrderStatus.failed,
    OrderStatus.refunded,
  ].contains(status);

  bool get hasRider => rider != null;

  bool get isTracking => tracking != null && hasRider;

  String get statusDisplayText {
    switch (status) {
      case OrderStatus.created:
        return 'Order Placed';
      case OrderStatus.confirmed:
        return 'Order Confirmed';
      case OrderStatus.preparing:
        return 'Preparing Order';
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.assigned:
        return 'Rider Assigned';
      case OrderStatus.pickedUp:
        return 'Order Picked Up';
      case OrderStatus.inTransit:
        return 'On the Way';
      case OrderStatus.nearDestination:
        return 'Almost There';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.failed:
        return 'Failed';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  @override
  List<Object?> get props => [
    id,
    orderNumber,
    type,
    status,
    user,
    seller,
    rider,
    pickupLocation,
    deliveryLocation,
    tracking,
    milestones,
    totalAmount,
    deliveryFee,
    taxes,
    currency,
    paymentId,
    paymentStatus,
    createdAt,
    updatedAt,
    scheduledDeliveryTime,
    specialInstructions,
    tags,
    priority,
    orderData,
    metadata,
  ];
}
