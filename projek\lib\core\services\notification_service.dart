import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';
import '../config/app_config.dart';
import '../config/constants.dart';
import '../utils/logger.dart';

class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final AudioPlayer _audioPlayer = AudioPlayer();

  static Future<void> initialize() async {
    // Request permissions
    await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    // Initialize local notifications
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings();
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _handleNotificationResponse,
    );

    // Create notification channels
    await _createNotificationChannels();

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  static Future<void> _createNotificationChannels() async {
    // Order notifications channel
    const orderChannel = AndroidNotificationChannel(
      AppConstants.orderNotificationChannel,
      'Order Notifications',
      description: 'Notifications for order updates',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('alarm'),
    );

    // General notifications channel
    const generalChannel = AndroidNotificationChannel(
      AppConstants.generalNotificationChannel,
      'General Notifications',
      description: 'General app notifications',
      importance: Importance.defaultImportance,
    );

    // Promotion notifications channel
    const promotionChannel = AndroidNotificationChannel(
      AppConstants.promotionNotificationChannel,
      'Promotions',
      description: 'Promotional notifications and offers',
      importance: Importance.low,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(orderChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(generalChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(promotionChannel);
  }

  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    Logger.notification('Received foreground message: ${message.messageId}');

    // Play alarm sound for rider/seller apps on order notifications
    if ((AppConfig.isRiderApp || AppConfig.isSellerApp) &&
        message.data['type'] == 'new_order') {
      await _playAlarmSound();
    }

    // Show local notification
    await _showLocalNotification(message);
  }

  static void _handleNotificationResponse(NotificationResponse response) {
    Logger.notification('Notification tapped: ${response.payload}');
    // Handle notification tap based on app type and payload
    // This would typically navigate to specific screens
  }

  static Future<void> _handleNotificationTap(RemoteMessage message) async {
    Logger.notification('Notification opened app: ${message.messageId}');
    // Handle notification tap when app is opened from background
    final data = message.data;

    if (AppConfig.isRiderApp && data['type'] == 'new_order') {
      // Navigate to orders page
    } else if (AppConfig.isSellerApp && data['type'] == 'new_order') {
      // Navigate to orders page
    } else if (AppConfig.isUserApp && data['type'] == 'order_update') {
      // Navigate to order tracking
    }
  }

  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    if (notification == null) return;

    // Determine channel based on message type
    String channelId = AppConstants.generalNotificationChannel;
    if (message.data['type'] == 'new_order' ||
        message.data['type'] == 'order_update') {
      channelId = AppConstants.orderNotificationChannel;
    } else if (message.data['type'] == 'promotion') {
      channelId = AppConstants.promotionNotificationChannel;
    }

    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: _getImportance(channelId),
      priority: Priority.high,
      sound: channelId == AppConstants.orderNotificationChannel
          ? const RawResourceAndroidNotificationSound('alarm')
          : null,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      details,
      payload: message.data.toString(),
    );
  }

  static Future<void> _playAlarmSound() async {
    try {
      await _audioPlayer.play(AssetSource('sounds/alarm.mp3'));
    } catch (e) {
      Logger.error('Error playing alarm sound', e);
    }
  }

  static Future<String?> getToken() async {
    return await _messaging.getToken();
  }

  static Future<void> subscribeToTopic(String topic) async {
    await _messaging.subscribeToTopic(topic);
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    await _messaging.unsubscribeFromTopic(topic);
  }

  // Helper methods
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case AppConstants.orderNotificationChannel:
        return 'Order Notifications';
      case AppConstants.promotionNotificationChannel:
        return 'Promotions';
      default:
        return 'General Notifications';
    }
  }

  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case AppConstants.orderNotificationChannel:
        return 'Notifications for order updates';
      case AppConstants.promotionNotificationChannel:
        return 'Promotional notifications and offers';
      default:
        return 'General app notifications';
    }
  }

  static Importance _getImportance(String channelId) {
    switch (channelId) {
      case AppConstants.orderNotificationChannel:
        return Importance.high;
      case AppConstants.promotionNotificationChannel:
        return Importance.low;
      default:
        return Importance.defaultImportance;
    }
  }

  // Show custom local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = AppConstants.generalNotificationChannel,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: _getImportance(channelId),
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();
    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  Logger.notification('Handling background message: ${message.messageId}');
  // Handle background messages
}
