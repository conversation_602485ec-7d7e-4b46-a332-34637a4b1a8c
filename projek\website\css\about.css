/* About Page Styles */

/* Hero Section */
.about-hero {
  background: linear-gradient(135deg, var(--peacock-blue) 0%, var(--green) 50%, var(--saffron) 100%);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  position: relative;
  overflow: hidden;
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.about-text {
  color: var(--white) !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.super-text {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin: 1.5rem 0 3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--saffron);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 0.5rem;
}

.hero-image {
  text-align: center;
}

.hero-image img {
  width: 300px;
  height: 300px;
  object-fit: contain;
  animation: float 4s ease-in-out infinite;
}

/* Our Story Section */
.our-story {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
}

.story-intro {
  font-size: 1.2rem;
  color: var(--medium-gray);
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  line-height: 1.8;
}

.story-timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.story-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-saffron);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 4rem;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-year {
  width: 100px;
  height: 100px;
  background: var(--gradient-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--white);
  position: relative;
  z-index: 2;
  box-shadow: 0 5px 15px rgba(20, 110, 180, 0.3);
}

.timeline-content {
  flex: 1;
  padding: 2rem;
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 15px var(--shadow-light);
  margin: 0 2rem;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.timeline-content h3 {
  color: var(--dark-gray);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.timeline-content p {
  color: var(--medium-gray);
  line-height: 1.6;
}

/* Mission & Vision Section */
.mission-vision {
  padding: 5rem 2rem;
  background: var(--white);
}

.mv-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.mv-card {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 10px 30px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.mv-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px var(--shadow);
}

.mv-card.mission {
  border-top: 4px solid var(--peacock-blue);
}

.mv-card.vision {
  border-top: 4px solid var(--green);
}

.mv-card.values {
  border-top: 4px solid var(--saffron);
}

.mv-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--gradient-saffron);
  color: var(--white);
}

.mv-card.mission .mv-icon {
  background: var(--gradient-blue);
}

.mv-card.vision .mv-icon {
  background: var(--gradient-green);
}

.mv-icon .material-icons {
  font-size: 2.5rem;
}

.mv-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--dark-gray);
}

.mv-card p {
  color: var(--medium-gray);
  line-height: 1.7;
  font-size: 1rem;
}

/* Team Section */
.team-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.team-member {
  background: var(--white);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 25px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(19, 136, 8, 0.1);
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
}

.member-photo {
  width: 120px;
  height: 120px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--saffron);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

.member-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
}

.member-role {
  color: var(--green);
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.member-bio {
  color: var(--medium-gray);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.member-social {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.member-social a {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--medium-gray);
  transition: all 0.3s ease;
}

.member-social a:hover {
  background: var(--saffron);
  color: var(--white);
  transform: translateY(-2px);
}

/* Awards Section */
.awards-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.awards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.award-item {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.award-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px var(--shadow);
}

.award-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.award-item h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
}

.award-item p {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  padding: 5rem 2rem;
  background: var(--gradient-blue);
  color: var(--white);
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .timeline-item {
    flex-direction: column !important;
    text-align: center;
  }
  
  .timeline-content {
    margin: 1rem 0;
  }
  
  .story-timeline::before {
    display: none;
  }
  
  .mv-grid,
  .team-grid,
  .awards-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
