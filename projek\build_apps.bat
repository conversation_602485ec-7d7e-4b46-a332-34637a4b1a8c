@echo off
echo ========================================
echo PROJEK APPS - BUILD SCRIPT
echo ========================================

echo.
echo Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo ========================================
echo BUILDING USER APP
echo ========================================
echo Building User App (Debug)...
flutter build apk --debug --flavor user -t lib/main_user.dart
if %ERRORLEVEL% EQU 0 (
    echo ✅ User App built successfully!
    echo APK Location: build\app\outputs\flutter-apk\app-user-debug.apk
) else (
    echo ❌ User App build failed!
)

echo.
echo ========================================
echo BUILDING RIDER APP
echo ========================================
echo Building Rider App (Debug)...
flutter build apk --debug --flavor rider -t lib/main_rider.dart
if %ERRORLEVEL% EQU 0 (
    echo ✅ Rider App built successfully!
    echo APK Location: build\app\outputs\flutter-apk\app-rider-debug.apk
) else (
    echo ❌ Rider App build failed!
)

echo.
echo ========================================
echo BUILDING SELLER APP
echo ========================================
echo Building Seller App (Debug)...
flutter build apk --debug --flavor seller -t lib/main_seller.dart
if %ERRORLEVEL% EQU 0 (
    echo ✅ Seller App built successfully!
    echo APK Location: build\app\outputs\flutter-apk\app-seller-debug.apk
) else (
    echo ❌ Seller App build failed!
)

echo.
echo ========================================
echo INSTALLING APPS TO DEVICE
echo ========================================

echo Checking connected devices...
adb devices

echo.
echo Installing User App...
adb install -r build\app\outputs\flutter-apk\app-user-debug.apk
if %ERRORLEVEL% EQU 0 (
    echo ✅ User App installed successfully!
) else (
    echo ❌ User App installation failed!
)

echo.
echo Installing Rider App...
adb install -r build\app\outputs\flutter-apk\app-rider-debug.apk
if %ERRORLEVEL% EQU 0 (
    echo ✅ Rider App installed successfully!
) else (
    echo ❌ Rider App installation failed!
)

echo.
echo Installing Seller App...
adb install -r build\app\outputs\flutter-apk\app-seller-debug.apk
if %ERRORLEVEL% EQU 0 (
    echo ✅ Seller App installed successfully!
) else (
    echo ❌ Seller App installation failed!
)

echo.
echo ========================================
echo BUILD SUMMARY
echo ========================================
echo User App: com.projek.user
echo Rider App: com.projek.rider  
echo Seller App: com.projek.seller
echo.
echo All APKs are in: build\app\outputs\flutter-apk\
echo ========================================

pause
