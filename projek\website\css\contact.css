/* Contact Page Styles */

/* Hero Section */
.contact-hero {
  background: linear-gradient(135deg, var(--saffron) 0%, var(--deep-saffron) 50%, var(--peacock-blue) 100%);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.contact-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.contact-hero .container {
  position: relative;
  z-index: 2;
}

.get-text {
  color: var(--white) !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.in-touch-text {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.contact-hero p {
  font-size: 1.2rem;
  margin-top: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Contact Methods Section */
.contact-methods {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.method-card {
  background: var(--white);
  border-radius: 16px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: 0 8px 25px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.method-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
  border-color: rgba(255, 153, 51, 0.3);
}

.method-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--gradient-saffron);
  color: var(--white);
}

.method-icon .material-icons {
  font-size: 2.5rem;
}

.method-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.method-card p {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.method-details a {
  color: var(--peacock-blue);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
}

.method-details a:hover {
  text-decoration: underline;
}

.method-details address {
  font-style: normal;
  color: var(--dark-gray);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.availability {
  display: block;
  color: var(--green);
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Contact Form Section */
.contact-form-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.form-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
}

.form-info h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.form-info p {
  color: var(--medium-gray);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.contact-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: var(--medium-gray);
}

.feature-item .material-icons {
  color: var(--green);
  font-size: 1.2rem;
}

.emergency-contact {
  background: linear-gradient(135deg, #FFF3CD 0%, #FCF4A3 100%);
  border: 1px solid #F0E68C;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.emergency-contact h4 {
  color: #856404;
  margin-bottom: 0.5rem;
}

.emergency-contact p {
  color: #856404;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.emergency-number {
  color: #dc3545;
  font-weight: 700;
  font-size: 1.2rem;
  text-decoration: none;
}

.emergency-number:hover {
  text-decoration: underline;
}

.form-container {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px var(--shadow-light);
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.contact-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-form .form-group {
  margin-bottom: 1.5rem;
}

.contact-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-gray);
}

.contact-form input,
.contact-form select,
.contact-form textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--light-gray);
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--saffron);
  box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.1);
}

.contact-form textarea {
  resize: vertical;
  min-height: 120px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  color: var(--medium-gray);
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-label a {
  color: var(--peacock-blue);
  text-decoration: none;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

.btn-large {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* FAQ Section */
.faq-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.faq-container {
  max-width: 800px;
  margin: 3rem auto 0;
}

.faq-item {
  background: var(--white);
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 5px 15px var(--shadow-light);
  overflow: hidden;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.faq-question {
  padding: 1.5rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.faq-question:hover {
  background: var(--cream);
}

.faq-question h3 {
  margin: 0;
  color: var(--dark-gray);
  font-size: 1.1rem;
}

.faq-question .material-icons {
  color: var(--saffron);
  transition: transform 0.3s ease;
}

.faq-item.active .faq-question .material-icons {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 2rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 0 2rem 1.5rem;
  max-height: 200px;
}

.faq-answer p {
  color: var(--medium-gray);
  line-height: 1.6;
  margin: 0;
}

/* Map Section */
.map-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.map-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.map-placeholder {
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
  border-radius: 16px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--medium-gray);
}

.map-content {
  text-align: center;
  color: var(--medium-gray);
}

.map-content .material-icons {
  font-size: 4rem;
  color: var(--saffron);
  margin-bottom: 1rem;
}

.map-content h3 {
  color: var(--dark-gray);
  margin-bottom: 1rem;
}

.office-info h3 {
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.hours-list {
  background: var(--cream);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 153, 51, 0.2);
}

.hours-item:last-child {
  border-bottom: none;
}

.office-contacts h4 {
  color: var(--dark-gray);
  margin-bottom: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: 8px;
}

.contact-item .material-icons {
  color: var(--saffron);
  font-size: 1.5rem;
}

.contact-item strong {
  color: var(--dark-gray);
  display: block;
  margin-bottom: 0.25rem;
}

.contact-item p {
  margin: 0;
  color: var(--medium-gray);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .methods-grid {
    grid-template-columns: 1fr;
  }
  
  .form-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .contact-form .form-row {
    grid-template-columns: 1fr;
  }
  
  .map-container {
    grid-template-columns: 1fr;
  }
  
  .map-placeholder {
    height: 300px;
  }
}
