import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/models/working_wallet_models.dart';
import '../../../../core/services/analytics_service.dart';

class WorkingWalletService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  static const String _walletsCollection = 'working_wallets';
  static const String _transactionsCollection = 'wallet_transactions';

  // Get or create wallet for user
  static Future<WorkingWallet> getOrCreateWallet({String? userId}) async {
    try {
      final currentUserId = userId ?? _auth.currentUser?.uid;
      if (currentUserId == null) throw Exception('User not authenticated');

      final doc = await _firestore
          .collection(_walletsCollection)
          .doc(currentUserId)
          .get();

      if (doc.exists) {
        return WorkingWallet.fromFirestore(doc);
      } else {
        // Create new wallet
        final wallet = WorkingWallet(
          userId: currentUserId,
          lastUpdated: DateTime.now(),
        );

        await _firestore
            .collection(_walletsCollection)
            .doc(currentUserId)
            .set(wallet.toFirestore());
        return wallet;
      }
    } catch (e) {
      debugPrint('❌ Error getting wallet: $e');
      throw Exception('Failed to get wallet: ${e.toString()}');
    }
  }

  // Get wallet stream
  static Stream<WorkingWallet> getWalletStream({String? userId}) {
    final currentUserId = userId ?? _auth.currentUser?.uid;
    if (currentUserId == null) return Stream.error('User not authenticated');

    return _firestore
        .collection(_walletsCollection)
        .doc(currentUserId)
        .snapshots()
        .map((doc) {
          if (doc.exists) {
            return WorkingWallet.fromFirestore(doc);
          } else {
            // Return default wallet if doesn't exist
            return WorkingWallet(
              userId: currentUserId,
              lastUpdated: DateTime.now(),
            );
          }
        });
  }

  // Add ProjekCoin
  static Future<void> addProjekCoin({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['projekCoinBalance'] ?? 0.0).toDouble()
            : 0.0;

        final newBalance = currentBalance + amount;

        transaction.set(walletRef, {
          'userId': currentUser.uid,
          'projekCoinBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = _firestore
            .collection(_transactionsCollection)
            .doc();
        transaction.set(transactionRef, {
          'userId': currentUser.uid,
          'type': 'credit',
          'walletType': 'projekCoin',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'orderId': orderId,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });

      // Log analytics
      await AnalyticsService.logEvent('projekcoin_added', {
        'amount': amount,
        'description': description,
        'has_order': orderId != null,
      });
    } catch (e) {
      debugPrint('❌ Error adding ProjekCoin: $e');
      throw Exception('Failed to add ProjekCoin: ${e.toString()}');
    }
  }

  // Spend ProjekCoin
  static Future<bool> spendProjekCoin({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      bool success = false;

      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['projekCoinBalance'] ?? 0.0).toDouble()
            : 0.0;

        if (currentBalance >= amount) {
          final newBalance = currentBalance - amount;

          transaction.update(walletRef, {
            'projekCoinBalance': newBalance,
            'lastUpdated': FieldValue.serverTimestamp(),
          });

          // Record transaction
          final transactionRef = _firestore
              .collection(_transactionsCollection)
              .doc();
          transaction.set(transactionRef, {
            'userId': currentUser.uid,
            'type': 'debit',
            'walletType': 'projekCoin',
            'amount': amount,
            'balanceBefore': currentBalance,
            'balanceAfter': newBalance,
            'description': description,
            'orderId': orderId,
            'timestamp': FieldValue.serverTimestamp(),
            'status': 'completed',
          });

          success = true;
        }
      });

      if (success) {
        await AnalyticsService.logEvent('projekcoin_spent', {
          'amount': amount,
          'description': description,
          'has_order': orderId != null,
        });
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error spending ProjekCoin: $e');
      throw Exception('Failed to spend ProjekCoin: ${e.toString()}');
    }
  }

  // Add INR balance
  static Future<void> addINRBalance({
    required double amount,
    required String description,
    String? paymentId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['inrBalance'] ?? 0.0).toDouble()
            : 0.0;

        final newBalance = currentBalance + amount;

        transaction.set(walletRef, {
          'userId': currentUser.uid,
          'inrBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = _firestore
            .collection(_transactionsCollection)
            .doc();
        transaction.set(transactionRef, {
          'userId': currentUser.uid,
          'type': 'credit',
          'walletType': 'inr',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'paymentId': paymentId,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });

      await AnalyticsService.logEvent('inr_balance_added', {
        'amount': amount,
        'description': description,
        'has_payment': paymentId != null,
      });
    } catch (e) {
      debugPrint('❌ Error adding INR balance: $e');
      throw Exception('Failed to add INR balance: ${e.toString()}');
    }
  }

  // Add rewards
  static Future<void> addRewards({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['rewardsBalance'] ?? 0.0).toDouble()
            : 0.0;

        final newBalance = currentBalance + amount;

        transaction.set(walletRef, {
          'userId': currentUser.uid,
          'rewardsBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = _firestore
            .collection(_transactionsCollection)
            .doc();
        transaction.set(transactionRef, {
          'userId': currentUser.uid,
          'type': 'credit',
          'walletType': 'rewards',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'orderId': orderId,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });

      await AnalyticsService.logEvent('rewards_added', {
        'amount': amount,
        'description': description,
        'has_order': orderId != null,
      });
    } catch (e) {
      debugPrint('❌ Error adding rewards: $e');
      throw Exception('Failed to add rewards: ${e.toString()}');
    }
  }

  // Add cashback
  static Future<void> addCashback({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['cashbackBalance'] ?? 0.0).toDouble()
            : 0.0;

        final newBalance = currentBalance + amount;

        transaction.set(walletRef, {
          'userId': currentUser.uid,
          'cashbackBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = _firestore
            .collection(_transactionsCollection)
            .doc();
        transaction.set(transactionRef, {
          'userId': currentUser.uid,
          'type': 'credit',
          'walletType': 'cashback',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'orderId': orderId,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });

      await AnalyticsService.logEvent('cashback_added', {
        'amount': amount,
        'description': description,
        'has_order': orderId != null,
      });
    } catch (e) {
      debugPrint('❌ Error adding cashback: $e');
      throw Exception('Failed to add cashback: ${e.toString()}');
    }
  }

  // Get transaction history
  static Stream<List<Map<String, dynamic>>> getTransactionHistoryStream({
    String? userId,
  }) {
    final currentUserId = userId ?? _auth.currentUser?.uid;
    if (currentUserId == null) return Stream.value([]);

    return _firestore
        .collection(_transactionsCollection)
        .where('userId', isEqualTo: currentUserId)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return {
              'id': doc.id,
              ...data,
              'timestamp': (data['timestamp'] as Timestamp?)?.toDate(),
            };
          }).toList();
        });
  }

  // Aliases for test compatibility
  static Future<bool> addINR({
    required double amount,
    required String description,
  }) async {
    try {
      await addINRBalance(amount: amount, description: description);
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> spendINR({
    required double amount,
    required String description,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      // Check balance first
      final wallet = await getOrCreateWallet();
      if (wallet.inrBalance < amount) {
        return false;
      }

      // Create debit transaction
      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore
            .collection(_walletsCollection)
            .doc(currentUser.uid);
        final walletDoc = await transaction.get(walletRef);

        final currentBalance = walletDoc.exists
            ? (walletDoc.data()?['inrBalance'] ?? 0.0).toDouble()
            : 0.0;

        if (currentBalance < amount) {
          throw Exception('Insufficient balance');
        }

        final newBalance = currentBalance - amount;

        transaction.set(walletRef, {
          'userId': currentUser.uid,
          'inrBalance': newBalance,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Record transaction
        final transactionRef = _firestore
            .collection(_transactionsCollection)
            .doc();
        transaction.set(transactionRef, {
          'userId': currentUser.uid,
          'type': 'debit',
          'walletType': 'inr',
          'amount': amount,
          'balanceBefore': currentBalance,
          'balanceAfter': newBalance,
          'description': description,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      });

      return true;
    } catch (e) {
      debugPrint('❌ Error spending INR: $e');
      return false;
    }
  }
}
