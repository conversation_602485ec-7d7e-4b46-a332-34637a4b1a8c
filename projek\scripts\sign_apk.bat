@echo off
echo ========================================
echo   APK SIGNING TOOL FOR PROJEK MARKETPLACE
echo ========================================
echo.

REM Check if APK files exist
if not exist "APK_RELEASE\app-arm64-v8a-prod-release.apk" (
    echo ERROR: APK files not found in APK_RELEASE directory
    pause
    exit /b 1
)

echo Step 1: Creating debug keystore if not exists...
if not exist "%USERPROFILE%\.android\debug.keystore" (
    echo Creating debug keystore...
    keytool -genkey -v -keystore "%USERPROFILE%\.android\debug.keystore" -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US"
)

echo.
echo Step 2: Signing APK files...
echo.

REM Sign ARM64 APK
echo Signing ARM64 APK...
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore "%USERPROFILE%\.android\debug.keystore" -storepass android -keypass android "APK_RELEASE\app-arm64-v8a-prod-release.apk" androiddebugkey

REM Sign ARMv7a APK  
echo.
echo Signing ARMv7a APK...
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore "%USERPROFILE%\.android\debug.keystore" -storepass android -keypass android "APK_RELEASE\app-armeabi-v7a-prod-release.apk" androiddebugkey

echo.
echo Step 3: Verifying signatures...
jarsigner -verify -verbose -certs "APK_RELEASE\app-arm64-v8a-prod-release.apk"
jarsigner -verify -verbose -certs "APK_RELEASE\app-armeabi-v7a-prod-release.apk"

echo.
echo ========================================
echo   APK SIGNING COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Your signed APKs are ready for installation:
echo - APK_RELEASE\app-arm64-v8a-prod-release.apk (for Vivo V23 5G)
echo - APK_RELEASE\app-armeabi-v7a-prod-release.apk (for older devices)
echo.
echo Next steps:
echo 1. Transfer the ARM64 APK to your Vivo V23 5G
echo 2. Enable "Install from Unknown Sources" 
echo 3. Install the APK
echo.
pause
