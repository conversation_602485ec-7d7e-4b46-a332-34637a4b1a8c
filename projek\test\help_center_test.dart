// Help Center Widget Tests
//
// Tests for the Help Center functionality

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Help Center Tests', () {
    testWidgets('Help Center welcome section displays correctly', (
      WidgetTester tester,
    ) async {
      final welcomeSection = MaterialApp(
        home: Scaffold(
          body: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colors.blue, Colors.lightBlue],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.support_agent, color: Colors.white, size: 48),
                SizedBox(height: 12),
                Text(
                  'Welcome to Help Center',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'We\'re here to help you with any questions or issues you might have.',
                  style: TextStyle(color: Colors.white70, fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpWidget(welcomeSection);

      // Verify welcome section elements
      expect(find.byIcon(Icons.support_agent), findsOneWidget);
      expect(find.text('Welcome to Help Center'), findsOneWidget);
      expect(
        find.text(
          'We\'re here to help you with any questions or issues you might have.',
        ),
        findsOneWidget,
      );
    });

    testWidgets('Quick action cards display correctly', (
      WidgetTester tester,
    ) async {
      final quickActionCard = MaterialApp(
        home: Scaffold(
          body: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: const Column(
              children: [
                Icon(Icons.chat_bubble_outline, color: Colors.green, size: 32),
                SizedBox(height: 8),
                Text(
                  'Live Chat',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Chat with support',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpWidget(quickActionCard);

      // Verify quick action card elements
      expect(find.byIcon(Icons.chat_bubble_outline), findsOneWidget);
      expect(find.text('Live Chat'), findsOneWidget);
      expect(find.text('Chat with support'), findsOneWidget);
    });

    testWidgets('FAQ expansion tile works correctly', (
      WidgetTester tester,
    ) async {
      const faqItem = MaterialApp(
        home: Scaffold(
          body: Card(
            child: ExpansionTile(
              title: Text(
                'How do I reset my password?',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              children: [
                Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    'Go to the login screen and tap "Forgot Password". Enter your email address and we\'ll send you a reset link.',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpWidget(faqItem);

      // Verify FAQ question is visible
      expect(find.text('How do I reset my password?'), findsOneWidget);

      // Initially, the answer should not be visible
      expect(
        find.text(
          'Go to the login screen and tap "Forgot Password". Enter your email address and we\'ll send you a reset link.',
        ),
        findsNothing,
      );

      // Tap to expand
      await tester.tap(find.byType(ExpansionTile));
      await tester.pumpAndSettle();

      // Now the answer should be visible
      expect(
        find.text(
          'Go to the login screen and tap "Forgot Password". Enter your email address and we\'ll send you a reset link.',
        ),
        findsOneWidget,
      );
    });

    testWidgets('Contact information displays correctly', (
      WidgetTester tester,
    ) async {
      final contactItem = MaterialApp(
        home: Scaffold(
          body: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.email, color: Colors.white),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Email Support',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      '<EMAIL>',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );

      await tester.pumpWidget(contactItem);

      // Verify contact information elements
      expect(find.byIcon(Icons.email), findsOneWidget);
      expect(find.text('Email Support'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('Chat message bubble structure', (WidgetTester tester) async {
      final chatMessage = MaterialApp(
        home: Scaffold(
          body: Align(
            alignment: Alignment.centerRight,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              constraints: const BoxConstraints(maxWidth: 250),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hello, I need help with my account',
                    style: TextStyle(color: Colors.white),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Just now',
                    style: TextStyle(fontSize: 10, color: Colors.white70),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpWidget(chatMessage);

      // Verify chat message elements
      expect(find.text('Hello, I need help with my account'), findsOneWidget);
      expect(find.text('Just now'), findsOneWidget);
    });

    test('Auto response generation logic', () {
      // Test auto response logic without UI
      String generateTestResponse(String userMessage) {
        final message = userMessage.toLowerCase();

        if (message.contains('password') || message.contains('reset')) {
          return 'To reset your password, go to the login screen and tap "Forgot Password".';
        } else if (message.contains('profile') || message.contains('update')) {
          return 'You can update your profile by tapping your profile picture.';
        } else if (message.contains('hello') || message.contains('hi')) {
          return 'Hello! I\'m here to help you. What can I assist you with today?';
        } else {
          return 'Thank you for contacting support! A human agent will respond shortly.';
        }
      }

      // Test different message types
      expect(
        generateTestResponse('I forgot my password'),
        contains('reset your password'),
      );
      expect(
        generateTestResponse('How do I update my profile?'),
        contains('update your profile'),
      );
      expect(
        generateTestResponse('Hello there'),
        contains('Hello! I\'m here to help'),
      );
      expect(
        generateTestResponse('Random question'),
        contains('human agent will respond'),
      );
    });

    test('Message validation logic', () {
      // Test message validation
      bool isValidMessage(String message) {
        return message.trim().isNotEmpty && message.length <= 1000;
      }

      expect(isValidMessage('Hello'), isTrue);
      expect(isValidMessage(''), isFalse);
      expect(isValidMessage('   '), isFalse);
      expect(isValidMessage('A' * 1000), isTrue);
      expect(isValidMessage('A' * 1001), isFalse);
    });

    test('Email validation logic', () {
      // Test email validation
      bool isValidEmail(String email) {
        return email.contains('@') &&
            email.contains('.') &&
            email.indexOf('@') > 0 &&
            email.indexOf('@') < email.lastIndexOf('.');
      }

      expect(isValidEmail('<EMAIL>'), isTrue);
      expect(isValidEmail('invalid-email'), isFalse);
      expect(isValidEmail('test@domain'), isFalse);
      expect(isValidEmail('@example.com'), isFalse);
    });
  });
}
