import 'package:flutter_test/flutter_test.dart';
import 'package:projek/features/wallet/domain/models/working_wallet_models.dart';
import 'package:projek/features/wallet/data/services/working_wallet_service.dart';

void main() {
  group('Wallet System Tests', () {
    test('WorkingWallet model should create correctly', () {
      final wallet = WorkingWallet(
        userId: 'test_user_123',
        projekCoinBalance: 100.0,
        inrBalance: 500.0,
        rewardsBalance: 25.0,
        cashbackBalance: 15.0,
        lastUpdated: DateTime.now(),
      );

      expect(wallet.userId, 'test_user_123');
      expect(wallet.projekCoinBalance, 100.0);
      expect(wallet.inrBalance, 500.0);
      expect(wallet.rewardsBalance, 25.0);
      expect(wallet.cashbackBalance, 15.0);
    });

    test('WorkingWallet formatted getters should work correctly', () {
      final wallet = WorkingWallet(
        userId: 'test_user_123',
        projekCoinBalance: 100.0,
        inrBalance: 500.50,
        rewardsBalance: 25.25,
        cashbackBalance: 15.75,
        lastUpdated: DateTime.now(),
      );

      expect(wallet.formattedProjekCoinBalance, '100 PC');
      expect(wallet.formattedInrBalance, '₹500.50');
      expect(wallet.formattedRewardsBalance, '₹25.25');
      expect(wallet.formattedCashbackBalance, '₹15.75');
      expect(wallet.formattedTotalBalance, '₹641.50');
    });

    test('WorkingWallet copyWith should work correctly', () {
      final originalWallet = WorkingWallet(
        userId: 'test_user_123',
        projekCoinBalance: 100.0,
        inrBalance: 500.0,
        lastUpdated: DateTime.now(),
      );

      final updatedWallet = originalWallet.copyWith(
        projekCoinBalance: 150.0,
        inrBalance: 600.0,
      );

      expect(updatedWallet.userId, 'test_user_123');
      expect(updatedWallet.projekCoinBalance, 150.0);
      expect(updatedWallet.inrBalance, 600.0);
      expect(updatedWallet.rewardsBalance, 0.0); // Should remain unchanged
    });

    test('WorkingWallet toJson and fromJson should work correctly', () {
      final originalWallet = WorkingWallet(
        userId: 'test_user_123',
        projekCoinBalance: 100.0,
        inrBalance: 500.0,
        rewardsBalance: 25.0,
        cashbackBalance: 15.0,
        lastUpdated: DateTime.now(),
      );

      final json = originalWallet.toJson();
      final recreatedWallet = WorkingWallet.fromJson(json);

      expect(recreatedWallet.userId, originalWallet.userId);
      expect(recreatedWallet.projekCoinBalance, originalWallet.projekCoinBalance);
      expect(recreatedWallet.inrBalance, originalWallet.inrBalance);
      expect(recreatedWallet.rewardsBalance, originalWallet.rewardsBalance);
      expect(recreatedWallet.cashbackBalance, originalWallet.cashbackBalance);
    });

    test('WorkingTransaction model should create correctly', () {
      final transaction = WorkingTransaction(
        id: 'txn_123',
        userId: 'user_123',
        type: 'topup',
        amount: 100.0,
        description: 'Add money to wallet',
        timestamp: DateTime.now(),
        status: 'completed',
      );

      expect(transaction.id, 'txn_123');
      expect(transaction.userId, 'user_123');
      expect(transaction.type, 'topup');
      expect(transaction.amount, 100.0);
      expect(transaction.description, 'Add money to wallet');
      expect(transaction.status, 'completed');
    });

    test('WorkingTransaction formatted getters should work correctly', () {
      final transaction = WorkingTransaction(
        id: 'txn_123',
        userId: 'user_123',
        type: 'topup',
        amount: 100.50,
        description: 'Add money to wallet',
        timestamp: DateTime.now(),
        status: 'completed',
      );

      expect(transaction.formattedAmount, '₹100.50');
      expect(transaction.isCredit, true);
      expect(transaction.isCompleted, true);
    });

    test('WorkingTransaction isCredit should work for different types', () {
      final creditTransaction = WorkingTransaction(
        id: 'txn_1',
        userId: 'user_123',
        type: 'topup',
        amount: 100.0,
        description: 'Add money',
        timestamp: DateTime.now(),
        status: 'completed',
      );

      final debitTransaction = WorkingTransaction(
        id: 'txn_2',
        userId: 'user_123',
        type: 'transfer',
        amount: 50.0,
        description: 'Send money',
        timestamp: DateTime.now(),
        status: 'completed',
      );

      expect(creditTransaction.isCredit, true);
      expect(debitTransaction.isCredit, false);
    });

    test('WorkingTransaction status checks should work correctly', () {
      final completedTransaction = WorkingTransaction(
        id: 'txn_1',
        userId: 'user_123',
        type: 'topup',
        amount: 100.0,
        description: 'Add money',
        timestamp: DateTime.now(),
        status: 'completed',
      );

      final pendingTransaction = WorkingTransaction(
        id: 'txn_2',
        userId: 'user_123',
        type: 'transfer',
        amount: 50.0,
        description: 'Send money',
        timestamp: DateTime.now(),
        status: 'pending',
      );

      final failedTransaction = WorkingTransaction(
        id: 'txn_3',
        userId: 'user_123',
        type: 'transfer',
        amount: 50.0,
        description: 'Send money',
        timestamp: DateTime.now(),
        status: 'failed',
      );

      expect(completedTransaction.isCompleted, true);
      expect(completedTransaction.isPending, false);
      expect(completedTransaction.isFailed, false);

      expect(pendingTransaction.isCompleted, false);
      expect(pendingTransaction.isPending, true);
      expect(pendingTransaction.isFailed, false);

      expect(failedTransaction.isCompleted, false);
      expect(failedTransaction.isPending, false);
      expect(failedTransaction.isFailed, true);
    });
  });

  group('Wallet Service Tests', () {
    test('WorkingWalletService should have required methods', () {
      // Test that the service class exists and has the expected methods
      expect(WorkingWalletService.getOrCreateWallet, isA<Function>());
      expect(WorkingWalletService.addProjekCoin, isA<Function>());
      expect(WorkingWalletService.spendProjekCoin, isA<Function>());
      expect(WorkingWalletService.addINR, isA<Function>());
      expect(WorkingWalletService.spendINR, isA<Function>());
      expect(WorkingWalletService.getTransactionHistoryStream, isA<Function>());
      expect(WorkingWalletService.getWalletStream, isA<Function>());
    });
  });
}
