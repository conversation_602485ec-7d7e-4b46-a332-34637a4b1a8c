# Build System Organization

## Overview
This document describes the organized build system for the Projek Flutter application, including directory structure, build scripts, and cleanup procedures.

## Directory Structure

### Build Output Locations

#### Primary Android Build Location
```
android/app/build/outputs/
├── flutter-apk/          # Flutter-generated APK files
├── apk/
│   ├── debug/           # Debug APK files
│   └── release/         # Release APK files
├── logs/                # Build logs
├── mapping/             # ProGuard mapping files
├── native-debug-symbols/ # Native debug symbols
└── sdk-dependencies/    # SDK dependency information
```

#### Consolidated Build Location
```
build/
├── android/
│   └── outputs/
│       └── apk/
│           ├── debug/   # Copied debug APKs
│           └── release/ # Copied release APKs
└── native_assets/       # Flutter native assets
```

## Build Scripts

### Development Build
**File:** `scripts/dev_build.bat`
- Builds debug APK with development optimizations
- Includes split APKs per ABI for smaller file sizes
- Automatically copies outputs to consolidated location

### Production Build
**File:** `scripts/prod_build.bat`
- Builds release APK with full optimizations
- Includes ProGuard/R8 minification and obfuscation
- Automatically copies outputs to consolidated location

### APK Copy Utility
**File:** `scripts/copy_apk_outputs.bat`
- Copies APK files from Android build location to consolidated location
- Handles both debug and release APKs
- Creates necessary directories automatically

### Cleanup Utility
**File:** `scripts/cleanup_build.bat`
- Removes unnecessary build artifacts and cache files
- Preserves essential output files (APKs)
- Cleans Gradle cache, intermediates, and temporary files

## Usage Instructions

### Building APKs

#### For Development/Testing:
```bash
scripts\dev_build.bat
```

#### For Production/Release:
```bash
scripts\prod_build.bat
```

### Cleaning Build Artifacts:
```bash
scripts\cleanup_build.bat
```

### Manual APK Copy:
```bash
scripts\copy_apk_outputs.bat
```

## APK File Locations

After building, APK files will be available in:

1. **Primary Location:** `android/app/build/outputs/flutter-apk/`
2. **Consolidated Location:** `build/android/outputs/apk/debug/` or `build/android/outputs/apk/release/`

## Benefits of This Organization

1. **Centralized Outputs:** All APK files are copied to a single, easily accessible location
2. **Clean Structure:** Unnecessary build artifacts are regularly cleaned up
3. **Automated Process:** Build scripts handle copying and organization automatically
4. **Space Efficient:** Cleanup scripts remove large intermediate files while preserving outputs
5. **Easy Access:** APK files are available in predictable locations

## Troubleshooting

### If APK files are not generated:
1. Run `scripts\cleanup_build.bat` to clean all build artifacts
2. Ensure Flutter SDK is properly installed and in PATH
3. Run `flutter doctor` to check for issues
4. Try building again with `scripts\dev_build.bat`

### If build fails:
1. Check that all dependencies are installed
2. Ensure Android SDK is properly configured
3. Run `flutter pub get` to update dependencies
4. Check the build logs in `android/app/build/outputs/logs/`

## File Size Optimization

The build system includes several optimizations:
- **Split APKs:** Separate APKs for different CPU architectures (arm64-v8a, armeabi-v7a, x86_64)
- **ProGuard/R8:** Code minification and obfuscation for release builds
- **Resource Shrinking:** Removes unused resources in release builds
- **Asset Optimization:** Compresses and optimizes assets

This organization ensures efficient builds while maintaining easy access to output files.
