import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/real_time_tracking_models.dart';
import '../../domain/models/unified_order.dart';
import '../../data/services/real_time_tracking_service.dart';
import '../../data/services/unified_tracking_service.dart';

// Real-time tracking provider
final trackingProvider = StreamProvider.family<RealTimeTracking, String>((
  ref,
  orderId,
) {
  return RealTimeTrackingService.getTrackingStream(orderId);
});

// Unified order tracking provider
final unifiedOrderProvider = StreamProvider.family<UnifiedOrder, String>((
  ref,
  orderId,
) {
  return UnifiedTrackingService.getOrderStream(orderId).map((doc) {
    if (doc.exists) {
      return UnifiedOrder.fromFirestore(doc);
    } else {
      throw Exception('Order not found');
    }
  });
});

// Active tracking for rider
final riderActiveTrackingProvider =
    StreamProvider.family<List<RealTimeTracking>, String>((ref, riderId) {
      return RealTimeTrackingService.getRiderActiveTracking(riderId);
    });

// Tracking state provider
final trackingStateProvider =
    StateNotifierProvider<TrackingStateNotifier, TrackingState>((ref) {
      return TrackingStateNotifier();
    });

class TrackingState {
  final bool isTracking;
  final String? activeOrderId;
  final String? activeRiderId;
  final RealTimeLocation? lastLocation;
  final String? error;

  const TrackingState({
    this.isTracking = false,
    this.activeOrderId,
    this.activeRiderId,
    this.lastLocation,
    this.error,
  });

  TrackingState copyWith({
    bool? isTracking,
    String? activeOrderId,
    String? activeRiderId,
    RealTimeLocation? lastLocation,
    String? error,
  }) {
    return TrackingState(
      isTracking: isTracking ?? this.isTracking,
      activeOrderId: activeOrderId ?? this.activeOrderId,
      activeRiderId: activeRiderId ?? this.activeRiderId,
      lastLocation: lastLocation ?? this.lastLocation,
      error: error ?? this.error,
    );
  }
}

class TrackingStateNotifier extends StateNotifier<TrackingState> {
  TrackingStateNotifier() : super(const TrackingState());

  Future<void> startTracking(String orderId, String riderId) async {
    try {
      state = state.copyWith(
        isTracking: true,
        activeOrderId: orderId,
        activeRiderId: riderId,
        error: null,
      );

      await RealTimeTrackingService.startTracking(
        orderId: orderId,
        riderId: riderId,
      );
    } catch (e) {
      state = state.copyWith(isTracking: false, error: e.toString());
    }
  }

  Future<void> stopTracking() async {
    try {
      await RealTimeTrackingService.stopTracking();
      state = const TrackingState();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> pauseTracking() async {
    if (state.activeOrderId != null) {
      try {
        await RealTimeTrackingService.pauseTracking(state.activeOrderId!);
        state = state.copyWith(isTracking: false);
      } catch (e) {
        state = state.copyWith(error: e.toString());
      }
    }
  }

  Future<void> resumeTracking() async {
    if (state.activeOrderId != null && state.activeRiderId != null) {
      try {
        await RealTimeTrackingService.resumeTracking(
          state.activeOrderId!,
          state.activeRiderId!,
        );
        state = state.copyWith(isTracking: true, error: null);
      } catch (e) {
        state = state.copyWith(error: e.toString());
      }
    }
  }

  void updateLocation(RealTimeLocation location) {
    state = state.copyWith(lastLocation: location);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}
