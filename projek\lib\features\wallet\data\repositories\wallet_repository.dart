import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/projek_coin.dart';

class WalletRepository {
  static const String _boxName = 'projek_coin_wallet_box';
  static const String _walletKey = 'projek_coin_wallet';
  static const String _statisticsKey = 'wallet_statistics';

  Future<Box<ProjekCoinWallet>> get _walletBox async {
    if (Hive.isBoxOpen(_boxName)) {
      return Hive.box<ProjekCoinWallet>(_boxName);
    }
    return await Hive.openBox<ProjekCoinWallet>(_boxName);
  }

  Future<Box<WalletStatistics>> get _statisticsBox async {
    const statisticsBoxName = 'wallet_statistics_box';
    if (Hive.isBoxOpen(statisticsBoxName)) {
      return Hive.box<WalletStatistics>(statisticsBoxName);
    }
    return await Hive.openBox<WalletStatistics>(statisticsBoxName);
  }

  Future<ProjekCoinWallet> getWallet() async {
    try {
      final box = await _walletBox;
      final wallet = box.get(_walletKey);

      if (wallet == null) {
        // Create initial wallet with demo data
        final initialWallet = await _createInitialWallet();
        await saveWallet(initialWallet);
        return initialWallet;
      }

      return wallet;
    } catch (e) {
      // Return default wallet if error occurs
      return _createDefaultWallet();
    }
  }

  Future<void> saveWallet(ProjekCoinWallet wallet) async {
    try {
      final box = await _walletBox;
      await box.put(_walletKey, wallet);
    } catch (e) {
      rethrow;
    }
  }

  Future<WalletStatistics> getStatistics() async {
    try {
      final box = await _statisticsBox;
      final stats = box.get(_statisticsKey);

      if (stats == null) {
        final defaultStats = WalletStatistics(calculatedAt: DateTime.now());
        await saveStatistics(defaultStats);
        return defaultStats;
      }

      return stats;
    } catch (e) {
      return WalletStatistics(calculatedAt: DateTime.now());
    }
  }

  Future<void> saveStatistics(WalletStatistics statistics) async {
    try {
      final box = await _statisticsBox;
      await box.put(_statisticsKey, statistics);
    } catch (e) {
      rethrow;
    }
  }

  Future<ProjekCoinWallet> addTransaction(
    ProjekCoinTransaction transaction,
  ) async {
    final wallet = await getWallet();
    final updatedTransactions = [transaction, ...wallet.transactions];

    // Calculate new balance
    double newBalance = wallet.balance;
    if (transaction.isCredit) {
      newBalance += transaction.amount;
    } else {
      newBalance -= transaction.amount;
    }

    // Calculate totals
    double newTotalEarned = wallet.totalEarned;
    double newTotalSpent = wallet.totalSpent;

    if (transaction.isCredit) {
      newTotalEarned += transaction.amount;
    } else {
      newTotalSpent += transaction.amount;
    }

    final updatedWallet = wallet.copyWith(
      balance: newBalance,
      transactions: updatedTransactions,
      lastUpdated: DateTime.now(),
      totalEarned: newTotalEarned,
      totalSpent: newTotalSpent,
      transactionCount: wallet.transactionCount + 1,
    );

    await saveWallet(updatedWallet);
    await _updateStatistics(updatedWallet);

    return updatedWallet;
  }

  Future<void> _updateStatistics(ProjekCoinWallet wallet) async {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);

    // Calculate monthly statistics
    double monthlyEarnings = 0.0;
    double monthlySpending = 0.0;
    int monthlyTransactions = 0;
    double pendingAmount = 0.0;

    for (final transaction in wallet.transactions) {
      final transactionMonth = DateTime(
        transaction.timestamp.year,
        transaction.timestamp.month,
      );

      if (transactionMonth == currentMonth) {
        monthlyTransactions++;
        if (transaction.isCredit) {
          monthlyEarnings += transaction.amount;
        } else {
          monthlySpending += transaction.amount;
        }
      }

      if (transaction.status == TransactionStatus.pending) {
        pendingAmount += transaction.amount;
      }
    }

    final statistics = WalletStatistics(
      monthlyEarnings: monthlyEarnings,
      monthlySpending: monthlySpending,
      monthlyTransactions: monthlyTransactions,
      pendingAmount: pendingAmount,
      calculatedAt: now,
    );

    await saveStatistics(statistics);
  }

  Future<ProjekCoinWallet> _createInitialWallet() async {
    const uuid = Uuid();
    final now = DateTime.now();

    // Create demo transactions
    final demoTransactions = [
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.reward,
        amount: 1000.0,
        description: 'Welcome bonus - New user reward',
        timestamp: now.subtract(const Duration(days: 7)),
        status: TransactionStatus.completed,
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.purchase,
        amount: 150.0,
        description: 'Purchase - Electronics item',
        timestamp: now.subtract(const Duration(days: 5)),
        status: TransactionStatus.completed,
        orderId: 'ORD-${now.millisecondsSinceEpoch}',
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.cashback,
        amount: 25.0,
        description: 'Cashback - Electronics purchase',
        timestamp: now.subtract(const Duration(days: 5, hours: 1)),
        status: TransactionStatus.completed,
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.referral,
        amount: 100.0,
        description: 'Referral bonus - Friend joined',
        timestamp: now.subtract(const Duration(days: 3)),
        status: TransactionStatus.completed,
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.purchase,
        amount: 75.0,
        description: 'Purchase - Food delivery',
        timestamp: now.subtract(const Duration(days: 2)),
        status: TransactionStatus.completed,
        orderId: 'ORD-${now.millisecondsSinceEpoch + 1}',
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.reward,
        amount: 50.0,
        description: 'Daily check-in reward',
        timestamp: now.subtract(const Duration(days: 1)),
        status: TransactionStatus.completed,
      ),
      ProjekCoinTransaction(
        id: uuid.v4(),
        type: TransactionType.topup,
        amount: 200.0,
        description: 'Wallet top-up via UPI',
        timestamp: now.subtract(const Duration(hours: 12)),
        status: TransactionStatus.completed,
      ),
    ];

    // Calculate initial balance and totals
    double balance = 0.0;
    double totalEarned = 0.0;
    double totalSpent = 0.0;

    for (final transaction in demoTransactions) {
      if (transaction.isCredit) {
        balance += transaction.amount;
        totalEarned += transaction.amount;
      } else {
        balance -= transaction.amount;
        totalSpent += transaction.amount;
      }
    }

    return ProjekCoinWallet(
      balance: balance,
      transactions: demoTransactions,
      lastUpdated: now,
      totalEarned: totalEarned,
      totalSpent: totalSpent,
      transactionCount: demoTransactions.length,
    );
  }

  ProjekCoinWallet _createDefaultWallet() {
    return ProjekCoinWallet(
      balance: 1000.0,
      transactions: const [],
      lastUpdated: DateTime.now(),
      totalEarned: 1000.0,
      totalSpent: 0.0,
      transactionCount: 0,
    );
  }

  /// Process game transactions atomically (entry fee + winnings)
  Future<ProjekCoinWallet> processGameTransactions({
    required ProjekCoinTransaction entryTransaction,
    ProjekCoinTransaction? winningsTransaction,
  }) async {
    try {
      final wallet = await getWallet();

      // Validate entry transaction (must be debit)
      if (entryTransaction.amount > 0) {
        throw Exception('Entry transaction must be negative (debit)');
      }

      // Check sufficient balance for entry fee
      if (wallet.balance < entryTransaction.amount.abs()) {
        throw Exception('Insufficient balance for game entry');
      }

      // Prepare all transactions
      final allTransactions = <ProjekCoinTransaction>[entryTransaction];
      if (winningsTransaction != null) {
        allTransactions.add(winningsTransaction);
      }

      // Calculate new balance and totals
      double newBalance = wallet.balance;
      double newTotalEarned = wallet.totalEarned;
      double newTotalSpent = wallet.totalSpent;

      for (final transaction in allTransactions) {
        if (transaction.isCredit) {
          newBalance += transaction.amount;
          newTotalEarned += transaction.amount;
        } else {
          newBalance -= transaction.amount;
          newTotalSpent += transaction.amount;
        }
      }

      // Create updated wallet with all transactions
      final updatedTransactions = [...allTransactions, ...wallet.transactions];
      final updatedWallet = wallet.copyWith(
        balance: newBalance,
        transactions: updatedTransactions,
        lastUpdated: DateTime.now(),
        totalEarned: newTotalEarned,
        totalSpent: newTotalSpent,
        transactionCount: wallet.transactionCount + allTransactions.length,
      );

      // Save atomically
      await saveWallet(updatedWallet);
      await _updateStatistics(updatedWallet);

      return updatedWallet;
    } catch (e) {
      debugPrint('Error processing game transactions: $e');
      rethrow;
    }
  }

  /// Validate if user can afford a game entry
  Future<bool> canAffordGameEntry(double entryAmount) async {
    try {
      final wallet = await getWallet();
      return wallet.balance >= entryAmount;
    } catch (e) {
      debugPrint('Error checking game affordability: $e');
      return false;
    }
  }

  /// Get transactions by category (for game history)
  Future<List<ProjekCoinTransaction>> getTransactionsByCategory(
    String category,
  ) async {
    try {
      final wallet = await getWallet();
      return wallet.transactions
          .where((transaction) => transaction.category == category)
          .toList();
    } catch (e) {
      debugPrint('Error getting transactions by category: $e');
      return [];
    }
  }

  /// Get gaming statistics
  Future<Map<String, dynamic>> getGamingStatistics() async {
    try {
      final wallet = await getWallet();
      final gamingTransactions = wallet.transactions
          .where((t) => t.category == 'gaming')
          .toList();

      double totalSpentOnGames = 0.0;
      double totalWonFromGames = 0.0;
      int totalGames = 0;

      for (final transaction in gamingTransactions) {
        if (transaction.description.contains('Entry Fee')) {
          totalSpentOnGames += transaction.amount;
          totalGames++;
        } else if (transaction.description.contains('Winnings')) {
          totalWonFromGames += transaction.amount;
        }
      }

      final netGamingProfit = totalWonFromGames - totalSpentOnGames;

      return {
        'totalGames': totalGames,
        'totalSpent': totalSpentOnGames,
        'totalWon': totalWonFromGames,
        'netProfit': netGamingProfit,
        'winRate': totalGames > 0 ? (totalWonFromGames > 0 ? 1.0 : 0.0) : 0.0,
      };
    } catch (e) {
      debugPrint('Error getting gaming statistics: $e');
      return {
        'totalGames': 0,
        'totalSpent': 0.0,
        'totalWon': 0.0,
        'netProfit': 0.0,
        'winRate': 0.0,
      };
    }
  }

  Future<void> clearWallet() async {
    try {
      final walletBox = await _walletBox;
      final statsBox = await _statisticsBox;
      await walletBox.clear();
      await statsBox.clear();
    } catch (e) {
      rethrow;
    }
  }
}
