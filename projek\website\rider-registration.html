<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rider Registration - Super Vision Platform</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/animations.css">
  <link rel="stylesheet" href="css/rider-registration.css">
  <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="logo">
      <img src="assets/images/common/app_logo.png" alt="Super Vision Platform Logo">
      <span>Super Vision</span>
    </div>
    
    <ul class="nav-links">
      <li><a href="public/index.html">Home</a></li>
      <li><a href="booking.html">Book Services</a></li>
      <li><a href="#" class="active">Rider Registration</a></li>
      <li><a href="public/index.html#support">Support</a></li>
    </ul>
    
    <div class="menu-toggle">
      <span class="material-icons">menu</span>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="rider-hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <div class="rider-icon-large">
            <img src="assets/images/logos/rider-logo.svg" alt="Rider Logo">
          </div>
          <h1><span class="join-text">Join</span> Our <span class="rider-text">Rider</span> <span class="network-text">Network</span></h1>
          <p>Become a delivery partner and earn money on your own schedule. Flexible hours, competitive earnings, and full support.</p>
          
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">10,000+</span>
              <span class="stat-label">Active Riders</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">₹25,000</span>
              <span class="stat-label">Avg Monthly Earning</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">4.8★</span>
              <span class="stat-label">Rider Rating</span>
            </div>
          </div>
        </div>
        
        <div class="hero-form">
          <div class="quick-apply">
            <h3>Quick Registration</h3>
            <p>Start earning in just 3 simple steps</p>
            <button class="btn btn-primary btn-large" id="startRegistration">
              <span class="material-icons">person_add</span>
              <span>Start Registration</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="benefits-section">
    <div class="container">
      <h2 class="section-title">Why Join Super Vision?</h2>
      <p class="section-subtitle">Discover the benefits of being a Super Vision delivery partner</p>
      
      <div class="benefits-grid">
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">schedule</span>
          </div>
          <h3>Flexible Schedule</h3>
          <p>Work when you want, where you want. Set your own hours and take breaks whenever needed.</p>
        </div>
        
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">payments</span>
          </div>
          <h3>Competitive Earnings</h3>
          <p>Earn ₹15,000 - ₹35,000 per month with bonuses, incentives, and surge pricing opportunities.</p>
        </div>
        
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">support_agent</span>
          </div>
          <h3>24/7 Support</h3>
          <p>Get help whenever you need it with our dedicated rider support team available round the clock.</p>
        </div>
        
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">security</span>
          </div>
          <h3>Insurance Coverage</h3>
          <p>Comprehensive insurance coverage for accidents, vehicle damage, and medical emergencies.</p>
        </div>
        
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">trending_up</span>
          </div>
          <h3>Growth Opportunities</h3>
          <p>Advance to team leader, trainer, or area manager positions with performance-based promotions.</p>
        </div>
        
        <div class="benefit-card fade-in">
          <div class="benefit-icon">
            <span class="material-icons">phone_android</span>
          </div>
          <h3>Easy-to-Use App</h3>
          <p>Intuitive rider app with GPS navigation, order management, and real-time earnings tracking.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Requirements Section -->
  <section class="requirements-section">
    <div class="container">
      <h2 class="section-title">Requirements</h2>
      <p class="section-subtitle">What you need to become a Super Vision rider</p>
      
      <div class="requirements-content">
        <div class="requirements-list">
          <div class="requirement-category">
            <h3><span class="material-icons">person</span> Personal Requirements</h3>
            <ul>
              <li>Age between 18-60 years</li>
              <li>Valid government ID (Aadhaar/PAN/Voter ID)</li>
              <li>Bank account with IFSC code</li>
              <li>Mobile phone with internet connection</li>
              <li>Basic English/Hindi communication skills</li>
            </ul>
          </div>
          
          <div class="requirement-category">
            <h3><span class="material-icons">two_wheeler</span> Vehicle Requirements</h3>
            <ul>
              <li>Own motorcycle/scooter (100cc or above)</li>
              <li>Valid driving license (minimum 1 year old)</li>
              <li>Vehicle registration certificate (RC)</li>
              <li>Valid vehicle insurance</li>
              <li>Pollution Under Control (PUC) certificate</li>
            </ul>
          </div>
          
          <div class="requirement-category">
            <h3><span class="material-icons">description</span> Documentation</h3>
            <ul>
              <li>Recent passport-size photographs</li>
              <li>Address proof (utility bill/rent agreement)</li>
              <li>Emergency contact details</li>
              <li>Previous work experience (if any)</li>
              <li>Character reference (optional)</li>
            </ul>
          </div>
        </div>
        
        <div class="requirements-image">
          <img src="assets/icons/rider/delivery_boy.png" alt="Rider Requirements">
          <div class="requirements-note">
            <p><strong>Note:</strong> All documents will be verified during the onboarding process. Fake documents will lead to immediate rejection.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Registration Form Section -->
  <section class="registration-section" id="registrationForm">
    <div class="container">
      <h2 class="section-title">Rider Registration Form</h2>
      <p class="section-subtitle">Fill in your details to start your journey as a Super Vision rider</p>
      
      <div class="form-container">
        <div class="form-progress">
          <div class="progress-step active" data-step="1">
            <span class="step-number">1</span>
            <span class="step-label">Personal Info</span>
          </div>
          <div class="progress-step" data-step="2">
            <span class="step-number">2</span>
            <span class="step-label">Vehicle Details</span>
          </div>
          <div class="progress-step" data-step="3">
            <span class="step-number">3</span>
            <span class="step-label">Documents</span>
          </div>
          <div class="progress-step" data-step="4">
            <span class="step-number">4</span>
            <span class="step-label">Verification</span>
          </div>
        </div>
        
        <form id="riderRegistrationForm" class="registration-form">
          <!-- Step 1: Personal Information -->
          <div class="form-step active" data-step="1">
            <h3>Personal Information</h3>
            
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" name="firstName" required>
              </div>
              <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
              </div>
              <div class="form-group">
                <label for="phone">Phone Number *</label>
                <input type="tel" id="phone" name="phone" required>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="dateOfBirth">Date of Birth *</label>
                <input type="date" id="dateOfBirth" name="dateOfBirth" required>
              </div>
              <div class="form-group">
                <label for="gender">Gender *</label>
                <select id="gender" name="gender" required>
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
            
            <div class="form-group">
              <label for="address">Complete Address *</label>
              <textarea id="address" name="address" rows="3" required placeholder="Enter your complete address with pincode"></textarea>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="city">City *</label>
                <input type="text" id="city" name="city" required>
              </div>
              <div class="form-group">
                <label for="pincode">Pincode *</label>
                <input type="text" id="pincode" name="pincode" required>
              </div>
            </div>
            
            <div class="form-group">
              <label for="emergencyContact">Emergency Contact Number *</label>
              <input type="tel" id="emergencyContact" name="emergencyContact" required>
            </div>
          </div>

          <!-- Step 2: Vehicle Details -->
          <div class="form-step" data-step="2">
            <h3>Vehicle Information</h3>

            <div class="form-row">
              <div class="form-group">
                <label for="vehicleType">Vehicle Type *</label>
                <select id="vehicleType" name="vehicleType" required>
                  <option value="">Select Vehicle Type</option>
                  <option value="motorcycle">Motorcycle</option>
                  <option value="scooter">Scooter</option>
                  <option value="bicycle">Bicycle</option>
                </select>
              </div>
              <div class="form-group">
                <label for="vehicleBrand">Vehicle Brand *</label>
                <input type="text" id="vehicleBrand" name="vehicleBrand" required placeholder="e.g., Honda, Bajaj, TVS">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="vehicleModel">Vehicle Model *</label>
                <input type="text" id="vehicleModel" name="vehicleModel" required placeholder="e.g., Activa, Pulsar, Apache">
              </div>
              <div class="form-group">
                <label for="vehicleYear">Manufacturing Year *</label>
                <input type="number" id="vehicleYear" name="vehicleYear" min="2000" max="2024" required>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="vehicleNumber">Vehicle Registration Number *</label>
                <input type="text" id="vehicleNumber" name="vehicleNumber" required placeholder="e.g., MH12AB1234">
              </div>
              <div class="form-group">
                <label for="licenseNumber">Driving License Number *</label>
                <input type="text" id="licenseNumber" name="licenseNumber" required>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="insuranceNumber">Insurance Policy Number *</label>
                <input type="text" id="insuranceNumber" name="insuranceNumber" required>
              </div>
              <div class="form-group">
                <label for="insuranceExpiry">Insurance Expiry Date *</label>
                <input type="date" id="insuranceExpiry" name="insuranceExpiry" required>
              </div>
            </div>

            <div class="form-group">
              <label for="pucNumber">PUC Certificate Number *</label>
              <input type="text" id="pucNumber" name="pucNumber" required>
            </div>
          </div>

          <!-- Step 3: Document Upload -->
          <div class="form-step" data-step="3">
            <h3>Document Upload</h3>
            <p style="text-align: center; color: var(--medium-gray); margin-bottom: 2rem;">
              Please upload clear photos of your documents. All documents will be verified during onboarding.
            </p>

            <div class="document-upload-grid">
              <div class="upload-item">
                <label for="profilePhoto">Profile Photo *</label>
                <div class="upload-box" data-target="profilePhoto">
                  <span class="material-icons">person</span>
                  <span>Upload Photo</span>
                  <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*" required>
                </div>
              </div>

              <div class="upload-item">
                <label for="aadhaarCard">Aadhaar Card *</label>
                <div class="upload-box" data-target="aadhaarCard">
                  <span class="material-icons">credit_card</span>
                  <span>Upload Aadhaar</span>
                  <input type="file" id="aadhaarCard" name="aadhaarCard" accept="image/*" required>
                </div>
              </div>

              <div class="upload-item">
                <label for="drivingLicense">Driving License *</label>
                <div class="upload-box" data-target="drivingLicense">
                  <span class="material-icons">badge</span>
                  <span>Upload License</span>
                  <input type="file" id="drivingLicense" name="drivingLicense" accept="image/*" required>
                </div>
              </div>

              <div class="upload-item">
                <label for="vehicleRC">Vehicle RC *</label>
                <div class="upload-box" data-target="vehicleRC">
                  <span class="material-icons">description</span>
                  <span>Upload RC</span>
                  <input type="file" id="vehicleRC" name="vehicleRC" accept="image/*" required>
                </div>
              </div>

              <div class="upload-item">
                <label for="vehicleInsurance">Vehicle Insurance *</label>
                <div class="upload-box" data-target="vehicleInsurance">
                  <span class="material-icons">security</span>
                  <span>Upload Insurance</span>
                  <input type="file" id="vehicleInsurance" name="vehicleInsurance" accept="image/*" required>
                </div>
              </div>

              <div class="upload-item">
                <label for="bankPassbook">Bank Passbook/Cheque *</label>
                <div class="upload-box" data-target="bankPassbook">
                  <span class="material-icons">account_balance</span>
                  <span>Upload Bank Details</span>
                  <input type="file" id="bankPassbook" name="bankPassbook" accept="image/*" required>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="bankAccountNumber">Bank Account Number *</label>
              <input type="text" id="bankAccountNumber" name="bankAccountNumber" required>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="ifscCode">IFSC Code *</label>
                <input type="text" id="ifscCode" name="ifscCode" required placeholder="e.g., SBIN0001234">
              </div>
              <div class="form-group">
                <label for="bankName">Bank Name *</label>
                <input type="text" id="bankName" name="bankName" required>
              </div>
            </div>
          </div>

          <!-- Step 4: Verification & Agreement -->
          <div class="form-step" data-step="4">
            <h3>Verification & Agreement</h3>

            <div class="verification-section">
              <h4>Background Verification</h4>
              <p>We will conduct a background verification which includes:</p>
              <ul class="verification-list">
                <li>Identity verification through government databases</li>
                <li>Address verification</li>
                <li>Criminal background check</li>
                <li>Driving record verification</li>
                <li>Reference checks (if provided)</li>
              </ul>
            </div>

            <div class="agreement-section">
              <h4>Terms & Conditions</h4>
              <div class="agreement-box">
                <p><strong>By registering as a Super Vision rider, you agree to:</strong></p>
                <ul>
                  <li>Provide accurate and truthful information</li>
                  <li>Maintain professional behavior with customers</li>
                  <li>Follow all traffic rules and safety guidelines</li>
                  <li>Keep your documents and vehicle in valid condition</li>
                  <li>Maintain a minimum rating of 4.0 stars</li>
                  <li>Complete deliveries in a timely manner</li>
                  <li>Wear the provided uniform and carry ID card</li>
                  <li>Report any incidents or issues immediately</li>
                </ul>
              </div>

              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                  <span class="checkmark"></span>
                  I agree to the <a href="#" target="_blank">Terms & Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
                </label>

                <label class="checkbox-label">
                  <input type="checkbox" id="agreeBackground" name="agreeBackground" required>
                  <span class="checkmark"></span>
                  I consent to background verification and document checks
                </label>

                <label class="checkbox-label">
                  <input type="checkbox" id="agreeMarketing" name="agreeMarketing">
                  <span class="checkmark"></span>
                  I agree to receive promotional messages and updates (Optional)
                </label>
              </div>
            </div>

            <div class="final-note">
              <p><strong>Important:</strong> Your application will be reviewed within 24-48 hours. You will receive updates via SMS and email. Please ensure all information is accurate as false information may lead to rejection.</p>
            </div>
          </div>

          <!-- Navigation Buttons -->
          <div class="form-navigation">
            <button type="button" class="btn btn-secondary" id="prevStep" style="display: none;">
              <span class="material-icons">arrow_back</span>
              <span>Previous</span>
            </button>
            <button type="button" class="btn btn-primary" id="nextStep">
              <span>Next</span>
              <span class="material-icons">arrow_forward</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <script src="js/main.js"></script>
  <script src="js/rider-registration.js"></script>
</body>
</html>
