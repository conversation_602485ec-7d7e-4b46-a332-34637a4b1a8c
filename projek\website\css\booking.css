/* Booking Page Specific Styles */

/* Logo Styles */
.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--dark-gray);
}

.logo img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-icon {
  font-size: 1.5rem;
  color: var(--saffron);
}

.gaming-link {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white !important;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.gaming-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

/* Booking Hero Section */
.booking-hero {
  background: linear-gradient(135deg, var(--saffron) 0%, var(--deep-saffron) 50%, var(--green) 100%);
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.booking-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.booking-hero .container {
  position: relative;
  z-index: 2;
}

.book-text {
  color: var(--white) !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.products-text {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.services-text {
  background: linear-gradient(45deg, #32CD32, #228B22);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Search Container */
.search-container {
  margin-top: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-box {
  display: flex;
  align-items: center;
  background: var(--white);
  border-radius: 50px;
  padding: 1rem 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.search-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.search-box .material-icons {
  color: var(--medium-gray);
  margin-right: 1rem;
}

.search-box input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 1.1rem;
  color: var(--dark-gray);
  font-family: 'Poppins', sans-serif;
}

.search-box input::placeholder {
  color: var(--medium-gray);
}

.search-btn {
  background: var(--gradient-blue);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(20, 110, 180, 0.4);
}

/* Quick Navigation */
.quick-nav {
  background: var(--white);
  padding: 2rem 0;
  box-shadow: 0 2px 10px var(--shadow-light);
  position: sticky;
  top: 70px;
  z-index: 100;
}

.nav-tabs {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--medium-gray);
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
}

.nav-tab:hover {
  background: var(--light-gray);
  color: var(--peacock-blue);
  transform: translateY(-2px);
}

.nav-tab.active {
  background: var(--gradient-blue);
  color: var(--white);
  box-shadow: 0 5px 15px rgba(20, 110, 180, 0.3);
}

.nav-tab .material-icons {
  font-size: 2rem;
}

/* Categories Section */
.categories-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
}

.section-subtitle {
  text-align: center;
  color: var(--medium-gray);
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.category-card {
  background: var(--white);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px var(--shadow);
}

.category-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
  transform: scale(1.1);
}

.category-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--peacock-blue);
}

.category-content {
  padding: 2rem;
}

.category-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.category-content p {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.category-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.category-stats .material-icons {
  font-size: 1rem;
}

.category-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
}

/* Services Section */
.services-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px var(--shadow);
  border-color: rgba(255, 153, 51, 0.3);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--light-gray), var(--white));
  border-radius: 20px;
  box-shadow: 0 5px 15px var(--shadow-light);
}

.service-icon img {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.service-content h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
  text-align: center;
}

.service-content p {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  text-align: center;
  line-height: 1.6;
}

.service-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.service-features span {
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.service-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
}

.service-pricing .price {
  font-weight: 600;
  color: var(--green);
  font-size: 1.1rem;
}

.service-pricing .rating {
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.service-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
}

/* Popular Section */
.popular-section {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.popular-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.popular-item {
  background: var(--white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
}

.popular-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px var(--shadow);
}

.popular-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.popular-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.popular-item:hover .popular-image img {
  transform: scale(1.1);
}

.popular-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--gradient-saffron);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.popular-content {
  padding: 1.5rem;
}

.popular-content h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
}

.popular-content p {
  color: var(--medium-gray);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.popular-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--green);
  font-size: 1.1rem;
}

.original-price {
  text-decoration: line-through;
  color: var(--medium-gray);
  font-size: 0.9rem;
  font-weight: 400;
}

.btn-sm {
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-hero {
    padding: 6rem 1rem 3rem;
  }
  
  .booking-hero h1 {
    font-size: 2rem;
  }
  
  .nav-tabs {
    gap: 1rem;
  }
  
  .nav-tab {
    padding: 0.8rem 1rem;
  }
  
  .categories-grid,
  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .search-container {
    margin-top: 2rem;
  }
  
  .search-box {
    padding: 0.8rem 1rem;
  }
}

/* Compact Design Styles */
.nav-tabs-small {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.nav-tab-small {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.6rem 1.2rem;
  background: transparent;
  border: 2px solid #e0e0e0;
  border-radius: 20px;
  color: #666;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-tab-small:hover,
.nav-tab-small.active {
  background: linear-gradient(135deg, #ff9933, #138808);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 153, 51, 0.3);
}

.nav-tab-small .material-icons {
  font-size: 1.1rem;
}

.section-title-small {
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--dark-gray);
  font-weight: 700;
}

.section-subtitle-small {
  text-align: center;
  color: var(--medium-gray);
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* Products Section */
.products-section {
  padding: 3rem 0;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.products-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.product-card-small {
  background: var(--white);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 20px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
  text-align: center;
}

.product-card-small:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--shadow);
  border-color: rgba(255, 153, 51, 0.3);
}

.product-icon-small {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--light-gray), var(--white));
  border-radius: 15px;
  box-shadow: 0 3px 10px var(--shadow-light);
}

.product-icon-small img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.product-content-small h4 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
  font-weight: 600;
}

.product-content-small p {
  color: var(--medium-gray);
  margin-bottom: 1rem;
  font-size: 0.85rem;
  line-height: 1.4;
}

.product-pricing-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.services-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.service-card-small {
  background: var(--white);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 20px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
  text-align: center;
}

.service-card-small:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--shadow);
  border-color: rgba(255, 153, 51, 0.3);
}

.service-icon-small {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--light-gray), var(--white));
  border-radius: 15px;
  box-shadow: 0 3px 10px var(--shadow-light);
}

.service-icon-small img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

.service-content-small h4 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
  font-weight: 600;
}

.service-content-small p {
  color: var(--medium-gray);
  margin-bottom: 1rem;
  font-size: 0.85rem;
  line-height: 1.4;
}

.service-pricing-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.price-small {
  font-weight: 600;
  color: var(--green);
  font-size: 0.95rem;
}

.rating-small {
  font-size: 0.8rem;
  color: var(--medium-gray);
}

.btn-small {
  padding: 0.6rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.btn-primary-small {
  background: var(--gradient-blue);
  color: var(--white);
}

.btn-secondary-small {
  background: var(--gradient-saffron);
  color: var(--white);
}

.btn-tertiary-small {
  background: var(--gradient-green);
  color: var(--white);
}

.btn-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.popular-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.popular-item-small {
  background: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
}

.popular-item-small:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow);
}

.popular-image-small {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.popular-image-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.popular-item-small:hover .popular-image-small img {
  transform: scale(1.1);
}

.popular-badge-small {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  background: var(--gradient-saffron);
  color: var(--white);
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.popular-content-small {
  padding: 1rem;
  text-align: center;
}

.popular-content-small h5 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
  color: var(--dark-gray);
  font-weight: 600;
}

.popular-content-small p {
  color: var(--medium-gray);
  margin-bottom: 0.8rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.popular-price-small {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  font-weight: 600;
  color: var(--green);
  font-size: 0.95rem;
}

.original-price-small {
  text-decoration: line-through;
  color: var(--medium-gray);
  font-size: 0.8rem;
  font-weight: 400;
}

@media (max-width: 480px) {
  .category-content,
  .service-card {
    padding: 1.5rem;
  }

  .service-pricing {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .products-grid-compact,
  .services-grid-compact,
  .popular-grid-compact {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nav-tabs-small {
    gap: 0.5rem;
  }

  .nav-tab-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}
