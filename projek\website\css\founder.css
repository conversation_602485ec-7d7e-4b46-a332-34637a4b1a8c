/* Founder Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.logo-image {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: 0.3rem;
}

.logo-icon {
    font-size: 2rem;
}

.footer-logo-img {
    width: 30px;
    height: 30px;
    object-fit: contain;
    margin-right: 0.3rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #3498db;
}

.nav-link.admin-link {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.hero-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.founder-image-container {
    display: flex;
    justify-content: center;
}

.founder-image {
    position: relative;
    width: 400px;
    height: 500px;
    border-radius: 25px;
    overflow: hidden;
    border: 5px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
}

.founder-image img {
    width: 120%;
    height: 120%;
    object-fit: cover;
    object-position: center top;
    transition: all 0.3s ease;
    filter: brightness(1.1) contrast(1.1) saturate(1.2);
    transform: translateX(-10%) translateY(-5%);
}

.founder-image:hover img {
    filter: brightness(1.2) contrast(1.2) saturate(1.3);
    transform: translateX(-10%) translateY(-5%) scale(1.02);
}

/* Founder Photo Overlay Effects */
.founder-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.overlay-decoration {
    position: relative;
    width: 100%;
    height: 100%;
}

.decoration-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 3px solid rgba(255, 153, 51, 0.6);
    border-radius: 30px;
    animation: rotate 15s linear infinite;
}

.decoration-ring::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(19, 136, 8, 0.4);
    border-radius: 35px;
    animation: rotate 20s linear infinite reverse;
}

.decoration-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.decoration-dots span {
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    animation: float 4s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.decoration-dots span:nth-child(1) {
    top: 15%;
    left: 85%;
    animation-delay: 0s;
    background: rgba(255, 153, 51, 0.8);
}

.decoration-dots span:nth-child(2) {
    top: 75%;
    left: 8%;
    animation-delay: 1.5s;
    background: rgba(255, 255, 255, 0.8);
}

.decoration-dots span:nth-child(3) {
    top: 45%;
    right: 3%;
    animation-delay: 3s;
    background: rgba(19, 136, 8, 0.8);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Professional Photo Frame Effect */
.founder-image::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: linear-gradient(45deg, #ff9933, #ffffff, #138808);
    border-radius: 30px;
    z-index: -1;
    animation: pulse-frame 3s ease-in-out infinite;
}

@keyframes pulse-frame {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.flag-colors {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    width: 60px;
    height: 20px;
    border-radius: 3px;
    overflow: hidden;
}

.flag-orange, .flag-white, .flag-green {
    flex: 1;
}

.flag-orange { background: #ff9933; }
.flag-white { background: #ffffff; }
.flag-green { background: #138808; }

.founder-intro {
    text-align: left;
}

.greeting h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 800;
}

.intro-text {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.founder-details h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.highlight {
    background: linear-gradient(45deg, #ff9933, #ffffff, #138808);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.founder-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.location {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.mission-statement {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.mission-intro {
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.platform-name {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.super-vision {
    background: linear-gradient(45deg, #ff9933, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.platform-status {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Vision Section */
.vision-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
    font-weight: 700;
}

.vision-statement {
    text-align: center;
    margin-bottom: 3rem;
}

.vision-statement .main-vision {
    font-size: 2rem;
    font-style: italic;
    color: #2c3e50;
    font-weight: 700;
    line-height: 1.4;
    position: relative;
    padding: 2.5rem;
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-left: 6px solid #ff9933;
    text-align: center;
}

.english-vision {
    display: block;
    margin-top: 1rem;
    font-size: 1.5rem;
    color: #3498db;
    font-weight: 600;
}

/* Vision Cards */
.vision-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.vision-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.vision-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.vision-card.riders {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #fff, #ffebee);
}

.vision-card.users {
    border-color: #3498db;
    background: linear-gradient(135deg, #fff, #e3f2fd);
}

.vision-card.sellers {
    border-color: #27ae60;
    background: linear-gradient(135deg, #fff, #e8f5e8);
}

.card-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.vision-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.vision-card p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.vision-card strong {
    color: #e74c3c;
    font-size: 1.1rem;
}

/* Happiness Message */
.happiness-message {
    background: linear-gradient(135deg, #ff9933, #138808);
    color: white;
    padding: 3rem;
    border-radius: 25px;
    margin: 3rem 0;
    text-align: center;
}

.happiness-content h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
}

.happiness-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.happiness-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 1.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.happiness-item .emoji {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 1rem;
}

.happiness-item p {
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

/* Unique Promise */
.unique-promise {
    background: white;
    padding: 3rem;
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 3px solid #667eea;
}

.unique-promise h3 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
}

.hindi-promise {
    font-size: 1.8rem;
    color: #e74c3c;
    font-weight: 700;
    margin-bottom: 1rem;
    font-style: italic;
}

.english-promise {
    font-size: 1.4rem;
    color: #3498db;
    font-weight: 600;
    margin-bottom: 2rem;
}

.promise-points {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.point {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: all 0.3s ease;
}

.point:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.vision-details {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.vision-details p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: #555;
    line-height: 1.8;
}

/* Apps Section */
.apps-section {
    padding: 5rem 0;
    background: white;
}

.section-subtitle {
    text-align: center;
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 3rem;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.app-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.app-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.user-app {
    border-color: #3498db;
}

.user-app:hover {
    border-color: #3498db;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.rider-app {
    border-color: #e74c3c;
}

.rider-app:hover {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.seller-app {
    border-color: #27ae60;
}

.seller-app:hover {
    border-color: #27ae60;
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.app-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
}

.app-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.app-card p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.app-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature {
    background: rgba(0, 0, 0, 0.05);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
}

.app-card:hover .feature {
    background: rgba(255, 255, 255, 0.2);
}

/* Mission Section */
.mission-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.mission-content {
    text-align: center;
}

.mission-content h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    font-weight: 700;
}

.mission-description {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.8;
}

.mission-goals {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.goal {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.goal-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

.goal h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.goal p {
    opacity: 0.9;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #ff9933, #138808);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.cta-description {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: white;
    color: #333;
}

.btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-secondary:hover {
    background: white;
    color: #333;
}

.patriotic-message {
    margin-top: 2rem;
}

.thank-you {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.jai-hind {
    font-size: 1.8rem;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo .logo-icon {
    font-size: 2rem;
}

.footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.link-group h4 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: #bdc3c7;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .founder-image {
        width: 300px;
        height: 400px;
    }

    .founder-image img {
        width: 130%;
        height: 130%;
        transform: translateX(-15%) translateY(-10%);
    }

    .founder-image:hover img {
        transform: translateX(-15%) translateY(-10%) scale(1.02);
    }
    
    .greeting h1 {
        font-size: 2rem;
    }
    
    .founder-details h2 {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .vision-statement .main-vision {
        font-size: 1.4rem;
        padding: 1.5rem;
    }

    .english-vision {
        font-size: 1.1rem;
    }

    .vision-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .happiness-message {
        padding: 2rem;
    }

    .happiness-content h3 {
        font-size: 1.5rem;
    }

    .happiness-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .unique-promise {
        padding: 2rem;
    }

    .unique-promise h3 {
        font-size: 1.5rem;
    }

    .hindi-promise {
        font-size: 1.3rem;
    }

    .english-promise {
        font-size: 1.1rem;
    }

    .promise-points {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }
    
    .apps-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
}
