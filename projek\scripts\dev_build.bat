@echo off
REM Script for building development APK with hot reload capabilities
echo Building development APK...
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get
echo.

REM Build development APK
echo Building debug APK with development optimizations...
flutter build apk --debug --split-per-abi

echo.
echo Development APK built successfully!
echo Location: android\app\build\outputs\flutter-apk\
echo.

REM Copy APK outputs to consolidated directory
echo Copying APK outputs to consolidated build directory...
call scripts\copy_apk_outputs.bat

REM List generated APKs
echo Generated APK files:
dir android\app\build\outputs\flutter-apk\*.apk /b 2>nul
dir android\app\build\outputs\apk\debug\*.apk /b 2>nul

echo.
echo APK files are also available in consolidated location:
echo - build\android\outputs\apk\debug\
echo - build\android\outputs\apk\release\
echo.
echo To install on device, run:
echo flutter install --debug
echo.
echo Or manually install using:
echo adb install android\app\build\outputs\flutter-apk\app-debug.apk
pause
