{"version": "0.2.0", "configurations": [{"name": "🚀 Projek User App (Debug + Hot Reload)", "request": "launch", "type": "dart", "program": "lib/main_user.dart", "deviceId": "1397182984001HG", "args": ["--hot"], "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache", "FLUTTER_WEB_USE_SKIA": "true"}}, {"name": "📊 Projek User App (Profile Mode)", "request": "launch", "type": "dart", "program": "lib/main_user.dart", "deviceId": "1397182984001HG", "flutterMode": "profile", "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache"}}, {"name": "🎯 Projek User App (Release)", "request": "launch", "type": "dart", "program": "lib/main_user.dart", "deviceId": "1397182984001HG", "flutterMode": "release", "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache"}}, {"name": "🌐 Projek User App (Web)", "request": "launch", "type": "dart", "program": "lib/main_user.dart", "deviceId": "chrome", "args": ["--hot"], "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache", "FLUTTER_WEB_USE_SKIA": "true"}}, {"name": "🖥️ Projek User App (Windows)", "request": "launch", "type": "dart", "program": "lib/main_user.dart", "deviceId": "windows", "args": ["--hot"], "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache"}}, {"name": "🏍️ <PERSON>jek <PERSON> App (Debug)", "request": "launch", "type": "dart", "program": "lib/main_rider.dart", "deviceId": "1397182984001HG", "args": ["--hot"], "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache"}}, {"name": "🛒 <PERSON><PERSON><PERSON> (Debug)", "request": "launch", "type": "dart", "program": "lib/main_seller.dart", "deviceId": "1397182984001HG", "args": ["--hot"], "env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache"}}]}