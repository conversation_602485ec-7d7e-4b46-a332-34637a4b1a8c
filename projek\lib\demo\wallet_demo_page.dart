import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/wallet/domain/models/working_wallet_models.dart';
import '../features/wallet/data/services/working_wallet_service.dart';
import '../features/wallet/presentation/widgets/quick_actions_widget.dart';
import '../features/wallet/presentation/widgets/transaction_list_widget.dart';
import '../features/wallet/presentation/widgets/rewards_section_widget.dart';
import '../core/theme/app_colors.dart';
import '../core/theme/app_text_styles.dart';

class WalletDemoPage extends ConsumerStatefulWidget {
  const WalletDemoPage({super.key});

  @override
  ConsumerState<WalletDemoPage> createState() => _WalletDemoPageState();
}

class _WalletDemoPageState extends ConsumerState<WalletDemoPage> {
  late WorkingWallet _demoWallet;
  List<Map<String, dynamic>> _demoTransactions = [];

  @override
  void initState() {
    super.initState();
    _initializeDemoData();
  }

  void _initializeDemoData() {
    // Create demo wallet
    _demoWallet = WorkingWallet(
      userId: 'demo_user_123',
      projekCoinBalance: 250.0,
      inrBalance: 1500.75,
      rewardsBalance: 125.50,
      cashbackBalance: 45.25,
      lastUpdated: DateTime.now(),
    );

    // Create demo transactions
    _demoTransactions = [
      {
        'id': 'txn_001',
        'type': 'topup',
        'amount': 500.0,
        'description': 'Added money to wallet',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'status': 'completed',
      },
      {
        'id': 'txn_002',
        'type': 'reward',
        'amount': 25.0,
        'description': 'Daily check-in bonus',
        'timestamp': DateTime.now().subtract(const Duration(hours: 6)),
        'status': 'completed',
      },
      {
        'id': 'txn_003',
        'type': 'transfer',
        'amount': 100.0,
        'description': 'Sent to friend',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'status': 'completed',
      },
      {
        'id': 'txn_004',
        'type': 'payment',
        'amount': 75.50,
        'description': 'Food order payment',
        'timestamp': DateTime.now().subtract(const Duration(days: 2)),
        'status': 'completed',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        title: const Text(
          'Wallet Demo',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _refreshDemo,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Wallet Balance Card
            _buildWalletBalanceCard(),
            
            const SizedBox(height: 20),
            
            // Quick Actions
            QuickActionsWidget(
              onAddMoney: _handleAddMoney,
              onSendMoney: _handleSendMoney,
              onPayBills: _handlePayBills,
              onScanQR: _handleScanQR,
            ),
            
            const SizedBox(height: 20),
            
            // Rewards Section
            RewardsSectionWidget(
              wallet: _demoWallet,
              onRewardTap: _handleRewardTap,
            ),
            
            const SizedBox(height: 20),
            
            // Recent Transactions
            _buildTransactionsSection(),
            
            const SizedBox(height: 20),
            
            // Demo Actions
            _buildDemoActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletBalanceCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue,
            AppColors.primaryBlue.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Balance',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
              Icon(
                Icons.visibility,
                color: Colors.white.withValues(alpha: 0.9),
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _demoWallet.formattedTotalBalance,
            style: AppTextStyles.headlineLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildBalanceItem(
                  'ProjekCoins',
                  _demoWallet.formattedProjekCoinBalance,
                  Icons.stars,
                ),
              ),
              Expanded(
                child: _buildBalanceItem(
                  'INR Balance',
                  _demoWallet.formattedInrBalance,
                  Icons.account_balance_wallet,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceItem(String label, String amount, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: Colors.white.withValues(alpha: 0.8),
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Transactions',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: _viewAllTransactions,
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        TransactionListWidget(
          transactions: _demoTransactions,
          onTransactionTap: _handleTransactionTap,
        ),
      ],
    );
  }

  Widget _buildDemoActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Demo Actions',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _simulateAddMoney,
                  icon: const Icon(Icons.add),
                  label: const Text('Add ₹100'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _simulateSpendMoney,
                  icon: const Icon(Icons.remove),
                  label: const Text('Spend ₹50'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _handleAddMoney() {
    _showSnackBar('Add Money feature - Coming Soon!', AppColors.info);
  }

  void _handleSendMoney() {
    _showSnackBar('Send Money feature - Coming Soon!', AppColors.info);
  }

  void _handlePayBills() {
    _showSnackBar('Pay Bills feature - Coming Soon!', AppColors.info);
  }

  void _handleScanQR() {
    _showSnackBar('QR Scanner feature - Coming Soon!', AppColors.info);
  }

  void _handleRewardTap(Map<String, dynamic> reward) {
    _showSnackBar('Reward: ${reward['title']}', AppColors.success);
  }

  void _handleTransactionTap(Map<String, dynamic> transaction) {
    _showSnackBar('Transaction: ${transaction['description']}', AppColors.info);
  }

  void _viewAllTransactions() {
    _showSnackBar('View All Transactions - Coming Soon!', AppColors.info);
  }

  void _refreshDemo() {
    setState(() {
      _initializeDemoData();
    });
    _showSnackBar('Demo data refreshed!', AppColors.success);
  }

  void _simulateAddMoney() {
    setState(() {
      _demoWallet = _demoWallet.copyWith(
        inrBalance: _demoWallet.inrBalance + 100.0,
        lastUpdated: DateTime.now(),
      );
      
      _demoTransactions.insert(0, {
        'id': 'txn_${DateTime.now().millisecondsSinceEpoch}',
        'type': 'topup',
        'amount': 100.0,
        'description': 'Demo: Added money to wallet',
        'timestamp': DateTime.now(),
        'status': 'completed',
      });
    });
    _showSnackBar('Added ₹100 to wallet!', AppColors.success);
  }

  void _simulateSpendMoney() {
    if (_demoWallet.inrBalance >= 50.0) {
      setState(() {
        _demoWallet = _demoWallet.copyWith(
          inrBalance: _demoWallet.inrBalance - 50.0,
          lastUpdated: DateTime.now(),
        );
        
        _demoTransactions.insert(0, {
          'id': 'txn_${DateTime.now().millisecondsSinceEpoch}',
          'type': 'payment',
          'amount': 50.0,
          'description': 'Demo: Payment made',
          'timestamp': DateTime.now(),
          'status': 'completed',
        });
      });
      _showSnackBar('Spent ₹50 from wallet!', AppColors.error);
    } else {
      _showSnackBar('Insufficient balance!', AppColors.warning);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
