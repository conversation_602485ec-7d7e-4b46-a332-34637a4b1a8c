import 'package:flutter/material.dart';
import '../../domain/models/working_wallet_models.dart';

class RewardsSectionWidget extends StatelessWidget {
  final WorkingWallet wallet;
  final Function(Map<String, dynamic>)? onRewardTap;

  const RewardsSectionWidget({
    super.key,
    required this.wallet,
    this.onRewardTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Rewards & Cashback',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildRewardsCard(context),
          const SizedBox(height: 16),
          _buildCashbackCard(context),
          const SizedBox(height: 16),
          _buildRecentRewards(context),
        ],
      ),
    );
  }

  Widget _buildRewardsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.stars,
                color: Colors.amber,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Rewards Balance',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '${wallet.rewardsBalance.toStringAsFixed(0)} PC',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.amber[700],
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _showRewardsDetails(context),
              icon: const Icon(Icons.arrow_forward_ios),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCashbackCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.account_balance_wallet,
                color: Colors.green,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Cashback Balance',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '₹${wallet.cashbackBalance.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _showCashbackDetails(context),
              icon: const Icon(Icons.arrow_forward_ios),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentRewards(BuildContext context) {
    // Mock recent rewards data
    final recentRewards = [
      {
        'title': 'Welcome Bonus',
        'amount': 100.0,
        'type': 'signup',
        'date': DateTime.now().subtract(const Duration(days: 1)),
      },
      {
        'title': 'Daily Check-in',
        'amount': 10.0,
        'type': 'daily',
        'date': DateTime.now().subtract(const Duration(hours: 2)),
      },
      {
        'title': 'Purchase Cashback',
        'amount': 25.0,
        'type': 'cashback',
        'date': DateTime.now().subtract(const Duration(days: 3)),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Rewards',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...recentRewards.map((reward) => _buildRewardItem(context, reward)),
      ],
    );
  }

  Widget _buildRewardItem(BuildContext context, Map<String, dynamic> reward) {
    final title = reward['title'] as String;
    final amount = reward['amount'] as double;
    final type = reward['type'] as String;
    final date = reward['date'] as DateTime;

    IconData icon;
    Color iconColor;

    switch (type) {
      case 'signup':
        icon = Icons.celebration;
        iconColor = Colors.purple;
        break;
      case 'daily':
        icon = Icons.calendar_today;
        iconColor = Colors.blue;
        break;
      case 'cashback':
        icon = Icons.money;
        iconColor = Colors.green;
        break;
      default:
        icon = Icons.star;
        iconColor = Colors.amber;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: iconColor.withOpacity(0.1),
          child: Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          _formatDate(date),
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        trailing: Text(
          type == 'cashback' ? '₹${amount.toStringAsFixed(2)}' : '${amount.toStringAsFixed(0)} PC',
          style: TextStyle(
            color: iconColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        onTap: () => onRewardTap?.call(reward),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  void _showRewardsDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Rewards Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You have ${wallet.rewardsBalance.toStringAsFixed(0)} ProjekCoins in rewards.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showCashbackDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Cashback Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You have ₹${wallet.cashbackBalance.toStringAsFixed(2)} in cashback.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
