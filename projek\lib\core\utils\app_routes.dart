class AppRoutes {
  // Authentication Routes
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String forgotPassword = '/forgot-password';
  static const String phoneAuth = '/auth/phone';
  static const String uidRegistration = '/uid-registration';

  // Main App Routes
  static const String home = '/home';
  static const String dashboard = '/home/<USER>';
  static const String profile = '/profile';

  // Marketplace Routes
  static const String marketplace = '/marketplace';
  static const String search = '/marketplace/search';
  static const String categories = '/marketplace/categories';
  static const String categoriesFiltered = '/marketplace/categories';
  static const String cart = '/marketplace/cart';
  static const String checkout = '/marketplace/checkout';
  static const String wishlist = '/wishlist';

  // Enhanced Routes
  static const String enhancedHelpCenter = '/help-center-enhanced';
  static const String payment = '/payment';
  static const String editProfile = '/edit-profile';
  static const String orders = '/orders';
  static const String orderDetail = '/orders/:id';
  static const String tracking = '/tracking/:id';
  static const String notifications = '/notifications';
  static const String helpCenter = '/help-center';
  static const String about = '/about';

  // Product Routes
  static String productDetail(String productId) =>
      '/marketplace/product/$productId';
  static String categoryItems(String categoryType, {String? title}) {
    final uri = '/marketplace/categories/$categoryType';
    return title != null ? '$uri?title=$title' : uri;
  }

  // Booking Routes
  static const String bookings = '/bookings';
  static String bookService({
    required String serviceId,
    required String serviceName,
    required String serviceType,
    required double basePrice,
  }) {
    return '/bookings/book/$serviceId?name=$serviceName&type=$serviceType&price=$basePrice';
  }

  static String bookingDetails(String bookingId) =>
      '/bookings/details/$bookingId';

  // Wallet Routes
  static const String wallet = '/wallet';
  static const String walletMain = '/wallet/main';
  static const String walletTopup = '/wallet/topup';
  static const String walletWithdraw = '/wallet/withdraw';
  static const String walletHistory = '/wallet/history';

  // Service Routes
  static const String services = '/services';
  static const String education = '/education';
  static const String bills = '/bills';
  static const String travel = '/travel';
  static const String insurance = '/insurance';

  // Utility Routes
  static const String settings = '/profile/settings';
  static const String help = '/profile/help';
}

// Navigation helper methods
class AppNavigation {
  // Quick navigation methods for common routes
  static String get homeRoute => AppRoutes.home;
  static String get marketplaceRoute => AppRoutes.marketplace;
  static String get bookingsRoute => AppRoutes.bookings;
  static String get walletRoute => AppRoutes.wallet;
  static String get profileRoute => AppRoutes.profile;

  // Service booking navigation
  static String bookCleaningService() => AppRoutes.bookService(
    serviceId: 'cleaning_001',
    serviceName: 'Home Cleaning',
    serviceType: 'cleaning',
    basePrice: 1500.0,
  );

  static String bookPlumbingService() => AppRoutes.bookService(
    serviceId: 'plumbing_001',
    serviceName: 'Plumbing Repair',
    serviceType: 'plumbing',
    basePrice: 800.0,
  );

  static String bookElectricalService() => AppRoutes.bookService(
    serviceId: 'electrical_001',
    serviceName: 'Electrical Repair',
    serviceType: 'electrical',
    basePrice: 600.0,
  );

  static String bookBeautyService() => AppRoutes.bookService(
    serviceId: 'beauty_001',
    serviceName: 'Beauty Service',
    serviceType: 'beauty',
    basePrice: 1200.0,
  );

  static String bookLaundryService() => AppRoutes.bookService(
    serviceId: 'laundry_001',
    serviceName: 'Laundry Service',
    serviceType: 'laundry',
    basePrice: 400.0,
  );

  // Category navigation
  static String electronicsCategory() =>
      AppRoutes.categoryItems('electronics', title: 'Electronics');
  static String fashionCategory() =>
      AppRoutes.categoryItems('fashion', title: 'Fashion');
  static String homeGardenCategory() =>
      AppRoutes.categoryItems('home-garden', title: 'Home & Garden');
  static String sportsCategory() =>
      AppRoutes.categoryItems('sports', title: 'Sports');
  static String booksCategory() =>
      AppRoutes.categoryItems('books', title: 'Books & Media');
  static String healthCategory() =>
      AppRoutes.categoryItems('health', title: 'Health & Beauty');
  static String foodCategory() =>
      AppRoutes.categoryItems('food', title: 'Food & Dining');
  static String groceryCategory() =>
      AppRoutes.categoryItems('grocery', title: 'Grocery');
}
