import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../data/repositories/wallet_repository.dart';
import '../../domain/models/projek_coin.dart';

// Wallet repository provider
final walletRepositoryProvider = Provider<WalletRepository>((ref) {
  return WalletRepository();
});

// Wallet state provider
final walletProvider = StateNotifierProvider<WalletNotifier, AsyncValue<ProjekCoinWallet>>((ref) {
  final repository = ref.watch(walletRepositoryProvider);
  return WalletNotifier(repository);
});

// Wallet statistics provider
final walletStatisticsProvider = StateNotifierProvider<WalletStatisticsNotifier, AsyncValue<WalletStatistics>>((ref) {
  final repository = ref.watch(walletRepositoryProvider);
  return WalletStatisticsNotifier(repository);
});

// Wallet balance provider (for easy access to balance)
final walletBalanceProvider = Provider<double>((ref) {
  final walletAsync = ref.watch(walletProvider);
  return walletAsync.when(
    data: (wallet) => wallet.balance,
    loading: () => 0.0,
    error: (_, __) => 0.0,
  );
});

// Formatted wallet balance provider
final formattedWalletBalanceProvider = Provider<String>((ref) {
  final balance = ref.watch(walletBalanceProvider);
  return '${balance.toStringAsFixed(2)} PC';
});

// Recent transactions provider
final recentTransactionsProvider = Provider<List<ProjekCoinTransaction>>((ref) {
  final walletAsync = ref.watch(walletProvider);
  return walletAsync.when(
    data: (wallet) => wallet.recentTransactions,
    loading: () => [],
    error: (_, __) => [],
  );
});

// Transaction filter provider
final transactionFilterProvider = StateProvider<TransactionFilter>((ref) {
  return TransactionFilter();
});

// Filtered transactions provider
final filteredTransactionsProvider = Provider<List<ProjekCoinTransaction>>((ref) {
  final walletAsync = ref.watch(walletProvider);
  final filter = ref.watch(transactionFilterProvider);
  
  return walletAsync.when(
    data: (wallet) {
      var transactions = wallet.transactions;
      
      // Filter by type
      if (filter.type != null) {
        transactions = transactions.where((t) => t.type == filter.type).toList();
      }
      
      // Filter by status
      if (filter.status != null) {
        transactions = transactions.where((t) => t.status == filter.status).toList();
      }
      
      // Filter by date range
      if (filter.startDate != null) {
        transactions = transactions.where((t) => t.timestamp.isAfter(filter.startDate!)).toList();
      }
      
      if (filter.endDate != null) {
        transactions = transactions.where((t) => t.timestamp.isBefore(filter.endDate!)).toList();
      }
      
      // Search by description
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        transactions = transactions.where((t) => 
          t.description.toLowerCase().contains(filter.searchQuery!.toLowerCase())
        ).toList();
      }
      
      return transactions;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

class WalletNotifier extends StateNotifier<AsyncValue<ProjekCoinWallet>> {
  final WalletRepository _repository;

  WalletNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadWallet();
  }

  Future<void> _loadWallet() async {
    try {
      state = const AsyncValue.loading();
      final wallet = await _repository.getWallet();
      state = AsyncValue.data(wallet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> addTransaction({
    required TransactionType type,
    required double amount,
    required String description,
    String? orderId,
    String? referenceId,
    Map<String, dynamic>? metadata,
    TransactionStatus status = TransactionStatus.completed,
  }) async {
    try {
      const uuid = Uuid();
      final transaction = ProjekCoinTransaction(
        id: uuid.v4(),
        type: type,
        amount: amount,
        description: description,
        timestamp: DateTime.now(),
        status: status,
        orderId: orderId,
        referenceId: referenceId,
        metadata: metadata,
      );

      final updatedWallet = await _repository.addTransaction(transaction);
      state = AsyncValue.data(updatedWallet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> addDemoCoins(double amount) async {
    await addTransaction(
      type: TransactionType.topup,
      amount: amount,
      description: 'Demo coins added for testing',
    );
  }

  Future<void> purchaseWithCoins({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    final currentWallet = state.value;
    if (currentWallet == null || currentWallet.balance < amount) {
      throw Exception('Insufficient balance');
    }

    await addTransaction(
      type: TransactionType.purchase,
      amount: amount,
      description: description,
      orderId: orderId,
    );
  }

  Future<void> addCashback({
    required double amount,
    required String description,
    String? orderId,
  }) async {
    await addTransaction(
      type: TransactionType.cashback,
      amount: amount,
      description: description,
      orderId: orderId,
    );
  }

  Future<void> addReferralBonus({
    required double amount,
    required String description,
    String? referenceId,
  }) async {
    await addTransaction(
      type: TransactionType.referral,
      amount: amount,
      description: description,
      referenceId: referenceId,
    );
  }

  Future<void> addReward({
    required double amount,
    required String description,
  }) async {
    await addTransaction(
      type: TransactionType.reward,
      amount: amount,
      description: description,
    );
  }

  Future<void> transferCoins({
    required double amount,
    required String description,
    String? referenceId,
  }) async {
    final currentWallet = state.value;
    if (currentWallet == null || currentWallet.balance < amount) {
      throw Exception('Insufficient balance');
    }

    await addTransaction(
      type: TransactionType.transfer,
      amount: amount,
      description: description,
      referenceId: referenceId,
    );
  }

  Future<void> refreshWallet() async {
    await _loadWallet();
  }

  Future<void> clearWallet() async {
    try {
      await _repository.clearWallet();
      await _loadWallet();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

class WalletStatisticsNotifier extends StateNotifier<AsyncValue<WalletStatistics>> {
  final WalletRepository _repository;

  WalletStatisticsNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    try {
      state = const AsyncValue.loading();
      final statistics = await _repository.getStatistics();
      state = AsyncValue.data(statistics);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> refreshStatistics() async {
    await _loadStatistics();
  }
}

class TransactionFilter {
  final TransactionType? type;
  final TransactionStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  TransactionFilter({
    this.type,
    this.status,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  TransactionFilter copyWith({
    TransactionType? type,
    TransactionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) {
    return TransactionFilter(
      type: type ?? this.type,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}
