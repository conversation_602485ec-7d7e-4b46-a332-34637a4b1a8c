<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Login - Super Vision Platform</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/animations.css">
  <link rel="stylesheet" href="css/admin-login.css">
  <link rel="icon" href="../assets/favicon.ico" type="image/x-icon">
</head>
<body class="admin-login-body">
  <!-- Background Animation -->
  <div class="login-background">
    <div class="floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
      <div class="shape shape-5"></div>
    </div>
  </div>

  <!-- Login Container -->
  <div class="login-container">
    <div class="login-card">
      <!-- Header -->
      <div class="login-header">
        <div class="admin-logo">
          <img src="../assets/images/logos/nav-logo.svg" alt="Super Vision Admin">
        </div>
        <h1>Admin Panel</h1>
        <p>Super Vision Management Dashboard</p>
      </div>

      <!-- Login Form -->
      <form id="adminLoginForm" class="login-form">
        <div class="form-group">
          <label for="email">
            <span class="material-icons">email</span>
            Email Address
          </label>
          <input type="email" id="email" name="email" required placeholder="Enter your admin email">
          <div class="input-underline"></div>
        </div>

        <div class="form-group">
          <label for="password">
            <span class="material-icons">lock</span>
            Password
          </label>
          <div class="password-input">
            <input type="password" id="password" name="password" required placeholder="Enter your password">
            <button type="button" class="password-toggle" id="togglePassword">
              <span class="material-icons">visibility</span>
            </button>
          </div>
          <div class="input-underline"></div>
        </div>

        <div class="form-options">
          <label class="remember-me">
            <input type="checkbox" id="rememberMe">
            <span class="checkmark"></span>
            Remember me
          </label>
          <a href="#" class="forgot-password">Forgot Password?</a>
        </div>

        <button type="submit" class="login-btn" id="loginBtn">
          <span class="btn-text">Sign In</span>
          <span class="material-icons btn-icon">arrow_forward</span>
        </button>

        <!-- Demo Login Section -->
        <div class="demo-login-section">
          <div class="demo-header">
            <h4>🚀 Quick Demo Login</h4>
            <p>Use these credentials to test the admin panel</p>
          </div>

          <div class="demo-accounts">
            <div class="demo-account" onclick="quickLogin('admin')">
              <div class="demo-icon">
                <span class="material-icons">admin_panel_settings</span>
              </div>
              <div class="demo-info">
                <h5>Super Admin</h5>
                <p><EMAIL></p>
                <small>Full access to all features</small>
              </div>
              <button class="demo-btn">Login</button>
            </div>

            <div class="demo-account" onclick="quickLogin('manager')">
              <div class="demo-icon">
                <span class="material-icons">manage_accounts</span>
              </div>
              <div class="demo-info">
                <h5>Manager</h5>
                <p><EMAIL></p>
                <small>Limited admin access</small>
              </div>
              <button class="demo-btn">Login</button>
            </div>
          </div>
        </div>

        <!-- Quick Access Section -->
        <div class="quick-access-section">
          <div class="quick-access-buttons">
            <a href="dashboard.html" class="quick-btn dashboard">
              🏠 Quick Access to Dashboard
            </a>
            <button type="button" onclick="autoLogin()" class="quick-btn auto-login">
              🔑 Auto Login
            </button>
          </div>
        </div>

        <div class="login-footer">
          <p>Secure admin access to Super Vision platform</p>
          <div class="security-badges">
            <span class="badge">
              <span class="material-icons">security</span>
              SSL Secured
            </span>
            <span class="badge">
              <span class="material-icons">verified_user</span>
              2FA Ready
            </span>
          </div>
        </div>
      </form>

      <!-- Loading Overlay -->
      <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <p>Authenticating...</p>
        </div>
      </div>
    </div>

    <!-- System Status -->
    <div class="system-status">
      <h3>System Status</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>API Server</span>
        </div>
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>Database</span>
        </div>
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>Payment Gateway</span>
        </div>
        <div class="status-item">
          <span class="status-indicator warning"></span>
          <span>SMS Service</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Modal -->
  <div class="error-modal" id="errorModal">
    <div class="modal-content">
      <div class="modal-header">
        <span class="material-icons error-icon">error</span>
        <h3>Login Failed</h3>
      </div>
      <div class="modal-body">
        <p id="errorMessage">Invalid credentials. Please try again.</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="closeErrorModal()">Try Again</button>
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div class="success-modal" id="successModal">
    <div class="modal-content">
      <div class="modal-header">
        <span class="material-icons success-icon">check_circle</span>
        <h3>Login Successful</h3>
      </div>
      <div class="modal-body">
        <p>Welcome back, Admin! Redirecting to dashboard...</p>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Action Button for Quick Access -->
  <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
    <button onclick="window.location.href='dashboard.html'" style="
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
    "
    onmouseover="this.style.transform='scale(1.1)'"
    onmouseout="this.style.transform='scale(1)'"
    title="Go to Dashboard">
      🏠
    </button>
  </div>

  <script src="js/admin-login.js"></script>
</body>
</html>
