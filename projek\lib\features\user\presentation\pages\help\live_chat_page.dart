import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier();
});

class ChatState {
  final List<ChatMessage> messages;
  final bool isTyping;
  final bool isConnected;
  final String? agentName;

  ChatState({
    this.messages = const [],
    this.isTyping = false,
    this.isConnected = false,
    this.agentName,
  });

  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isTyping,
    bool? isConnected,
    String? agentName,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isTyping: isTyping ?? this.isTyping,
      isConnected: isConnected ?? this.isConnected,
      agentName: agentName ?? this.agentName,
    );
  }
}

class ChatNotifier extends StateNotifier<ChatState> {
  ChatNotifier() : super(ChatState()) {
    _initializeChat();
  }

  void _initializeChat() {
    // Simulate connecting to support
    Future.delayed(const Duration(seconds: 2), () {
      state = state.copyWith(
        isConnected: true,
        agentName: 'Priya',
        messages: [
          ChatMessage(
            text: 'Hello! I\'m Priya from Projek Support. How can I help you today? 😊',
            isUser: false,
            timestamp: DateTime.now(),
            agentName: 'Priya',
          ),
        ],
      );
    });
  }

  void sendMessage(String text) {
    if (text.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      text: text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );

    state = state.copyWith(
      messages: [...state.messages, userMessage],
      isTyping: true,
    );

    // Simulate agent response
    Future.delayed(const Duration(seconds: 2), () {
      final response = _generateResponse(text);
      final agentMessage = ChatMessage(
        text: response,
        isUser: false,
        timestamp: DateTime.now(),
        agentName: state.agentName,
      );

      state = state.copyWith(
        messages: [...state.messages, agentMessage],
        isTyping: false,
      );
    });

    AnalyticsService.logEvent('live_chat_message_sent', {'message_length': text.length});
  }

  String _generateResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    if (message.contains('order') || message.contains('delivery')) {
      return 'I can help you with your order! Could you please provide your order ID? You can find it in the "My Orders" section of the app.';
    } else if (message.contains('payment') || message.contains('refund')) {
      return 'I understand you have a payment concern. Let me check that for you. Could you please share more details about the payment issue you\'re experiencing?';
    } else if (message.contains('spin') || message.contains('game') || message.contains('coin')) {
      return 'Great question about our games! You get 5 free spins daily on the Spin & Earn wheel. Spins reset at midnight. You can also earn coins through daily login rewards!';
    } else if (message.contains('account') || message.contains('profile')) {
      return 'I can help you with account-related issues. What specifically would you like to update or fix in your account?';
    } else if (message.contains('hello') || message.contains('hi')) {
      return 'Hello! I\'m here to help you with any questions about Projek. What can I assist you with today?';
    } else if (message.contains('thank')) {
      return 'You\'re very welcome! Is there anything else I can help you with today? 😊';
    } else {
      return 'I understand your concern. Let me connect you with a specialist who can better assist you with this specific issue. Please hold on for a moment.';
    }
  }

  void endChat() {
    state = state.copyWith(
      isConnected: false,
      messages: [...state.messages, 
        ChatMessage(
          text: 'Chat session ended. Thank you for contacting Projek Support!',
          isUser: false,
          timestamp: DateTime.now(),
          isSystem: true,
        ),
      ],
    );
    AnalyticsService.logEvent('live_chat_ended', {'message_count': state.messages.length});
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? agentName;
  final bool isSystem;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.agentName,
    this.isSystem = false,
  });
}

class LiveChatPage extends ConsumerStatefulWidget {
  const LiveChatPage({super.key});

  @override
  ConsumerState<LiveChatPage> createState() => _LiveChatPageState();
}

class _LiveChatPageState extends ConsumerState<LiveChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    AnalyticsService.logEvent('live_chat_opened', null);
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatProvider);
    final chatNotifier = ref.read(chatProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('💬 Live Chat'),
            if (chatState.isConnected && chatState.agentName != null)
              Text(
                'Connected to ${chatState.agentName}',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
              ),
          ],
        ),
        backgroundColor: AppColors.success,
        foregroundColor: Colors.white,
        actions: [
          if (chatState.isConnected)
            IconButton(
              icon: const Icon(Icons.call_end),
              onPressed: () {
                chatNotifier.endChat();
                Navigator.pop(context);
              },
            ),
        ],
      ),
      body: Column(
        children: [
          // Connection Status
          if (!chatState.isConnected)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: AppColors.warning.withOpacity(0.1),
              child: const Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 12),
                  Text('Connecting to support agent...'),
                ],
              ),
            ),

          // Messages
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: chatState.messages.length + (chatState.isTyping ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == chatState.messages.length && chatState.isTyping) {
                  return _buildTypingIndicator();
                }
                
                final message = chatState.messages[index];
                return _buildMessageBubble(message);
              },
            ),
          ),

          // Message Input
          if (chatState.isConnected)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Type your message...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      maxLines: null,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (text) => _sendMessage(chatNotifier),
                    ),
                  ),
                  const SizedBox(width: 8),
                  CircleAvatar(
                    backgroundColor: AppColors.success,
                    child: IconButton(
                      icon: const Icon(Icons.send, color: Colors.white),
                      onPressed: () => _sendMessage(chatNotifier),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: message.isUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        children: [
          if (!message.isUser && !message.isSystem)
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.success,
              child: Text(
                message.agentName?.substring(0, 1) ?? 'S',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          if (!message.isUser && !message.isSystem)
            const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isSystem
                    ? Colors.grey[300]
                    : message.isUser
                        ? AppColors.userPrimary
                        : Colors.grey[200],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!message.isUser && !message.isSystem && message.agentName != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        message.agentName!,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ),
                  Text(
                    message.text,
                    style: TextStyle(
                      color: message.isUser ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: message.isUser 
                          ? Colors.white70 
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser)
            const SizedBox(width: 8),
          if (message.isUser)
            const CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.userPrimary,
              child: Icon(Icons.person, color: Colors.white, size: 16),
            ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: AppColors.success,
            child: Text(
              ref.read(chatProvider).agentName?.substring(0, 1) ?? 'S',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(18),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 600 + (index * 200)),
      curve: Curves.easeInOut,
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: Colors.grey[500],
        shape: BoxShape.circle,
      ),
    );
  }

  void _sendMessage(ChatNotifier chatNotifier) {
    final text = _messageController.text.trim();
    if (text.isNotEmpty) {
      chatNotifier.sendMessage(text);
      _messageController.clear();
      
      // Scroll to bottom
      Future.delayed(const Duration(milliseconds: 100), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}
