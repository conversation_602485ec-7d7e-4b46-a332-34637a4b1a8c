import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';

class QRScannerPage extends ConsumerStatefulWidget {
  const QRScannerPage({super.key});

  @override
  ConsumerState<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends ConsumerState<QRScannerPage>
    with TickerProviderStateMixin {
  MobileScannerController? controller;
  BarcodeCapture? result;
  bool isFlashOn = false;
  bool isProcessing = false;

  late AnimationController _animationController;
  late Animation<double> _scanAnimation;

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();

    controller = MobileScannerController();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat();
  }

  @override
  void dispose() {
    controller?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status.isDenied) {
      if (mounted) {
        _showPermissionDialog();
      }
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Camera Permission Required'),
        content: const Text(
          'This app needs camera permission to scan QR codes. Please grant permission in settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.pop();
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        ),
        title: Text(
          'Scan QR Code',
          style: theme.textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _toggleFlash,
            icon: Icon(
              isFlashOn ? Icons.flash_on : Icons.flash_off,
              color: Colors.white,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 4,
            child: Stack(
              children: [
                // QR Scanner View
                MobileScanner(controller: controller, onDetect: _onQRDetected),

                // Scanner Overlay
                CustomPaint(
                  painter: QrScannerOverlayPainter(
                    borderColor: colorScheme.primary,
                    borderRadius: 16,
                    borderLength: 40,
                    borderWidth: 8,
                    cutOutSize: MediaQuery.of(context).size.width * 0.7,
                  ),
                  child: Container(),
                ),

                // Scanning Animation
                Positioned.fill(
                  child: AnimatedBuilder(
                    animation: _scanAnimation,
                    builder: (context, child) {
                      return CustomPaint(
                        painter: ScanLinePainter(
                          progress: _scanAnimation.value,
                          color: colorScheme.primary,
                        ),
                      );
                    },
                  ),
                ),

                // Instructions Overlay
                Positioned(
                  top: 50,
                  left: 0,
                  right: 0,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 32),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Position the QR code within the frame to scan',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom Section
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: Column(
                children: [
                  // Handle
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: colorScheme.outline,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Quick Actions
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildQuickAction(
                        theme,
                        colorScheme,
                        Icons.photo_library,
                        'Gallery',
                        _pickFromGallery,
                      ),
                      _buildQuickAction(
                        theme,
                        colorScheme,
                        Icons.qr_code,
                        'My QR',
                        _showMyQR,
                      ),
                      _buildQuickAction(
                        theme,
                        colorScheme,
                        Icons.history,
                        'History',
                        _showScanHistory,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction(
    ThemeData theme,
    ColorScheme colorScheme,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: colorScheme.onPrimaryContainer,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onQRDetected(BarcodeCapture capture) {
    if (!isProcessing && mounted && capture.barcodes.isNotEmpty) {
      setState(() {
        result = capture;
        isProcessing = true;
      });
      _processQRCode(capture.barcodes.first.rawValue);
    }
  }

  Future<void> _processQRCode(String? code) async {
    if (code == null || code.isEmpty) {
      _showError('Invalid QR code');
      return;
    }

    try {
      // Stop scanning
      await controller?.stop();

      // Vibrate
      HapticFeedback.mediumImpact();

      // Process different types of QR codes
      if (code.startsWith('myindiafirst://transfer/')) {
        await _processPeerTransfer(code);
      } else if (code.startsWith('upi://')) {
        await _processUPIPayment(code);
      } else if (code.startsWith('http')) {
        await _processWebLink(code);
      } else {
        // Try to parse as JSON for custom format
        await _processCustomQR(code);
      }
    } catch (e) {
      _showError('Failed to process QR code: $e');
    } finally {
      if (mounted) {
        setState(() {
          isProcessing = false;
        });
      }
      // Resume scanning after a delay
      Future.delayed(const Duration(seconds: 2), () {
        controller?.start();
      });
    }
  }

  Future<void> _processPeerTransfer(String code) async {
    try {
      // Extract user ID from custom QR format
      final uri = Uri.parse(code);
      final userId = uri.pathSegments.last;
      final amount = uri.queryParameters['amount'];
      final message = uri.queryParameters['message'];

      if (mounted) {
        final result = await showDialog<bool>(
          context: context,
          builder: (context) => _buildTransferDialog(userId, amount, message),
        );

        if (result == true) {
          // Process the transfer
          await _executeTransfer(userId, amount, message);
        }
      }
    } catch (e) {
      _showError('Invalid transfer QR code');
    }
  }

  Future<void> _processUPIPayment(String code) async {
    // Handle UPI payment QR codes
    _showInfo('UPI payment detected. Redirecting to payment app...');
    // TODO: Implement UPI payment flow
  }

  Future<void> _processWebLink(String code) async {
    // Handle web links
    _showInfo('Web link detected: $code');
    // TODO: Open in browser or in-app browser
  }

  Future<void> _processCustomQR(String code) async {
    // Handle custom JSON format QR codes
    _showInfo('Custom QR code detected');
    // TODO: Parse and handle custom format
  }

  Widget _buildTransferDialog(String userId, String? amount, String? message) {
    return AlertDialog(
      title: const Text('Confirm Transfer'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Transfer to: $userId'),
          if (amount != null) Text('Amount: ₹$amount'),
          if (message != null) Text('Message: $message'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: const Text('Confirm'),
        ),
      ],
    );
  }

  Future<void> _executeTransfer(
    String userId,
    String? amount,
    String? message,
  ) async {
    try {
      // TODO: Implement actual transfer logic using wallet provider
      _showSuccess('Transfer completed successfully!');

      // Navigate back after success
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          context.pop();
        }
      });
    } catch (e) {
      _showError('Transfer failed: $e');
    }
  }

  void _toggleFlash() async {
    if (controller != null) {
      await controller!.toggleTorch();
      setState(() {
        isFlashOn = !isFlashOn;
      });
    }
  }

  void _pickFromGallery() {
    // TODO: Implement gallery picker for QR codes
    _showInfo('Gallery picker coming soon!');
  }

  void _showMyQR() {
    // TODO: Navigate to user's QR code page
    context.push('/wallet/my-qr');
  }

  void _showScanHistory() {
    // TODO: Navigate to scan history page
    context.push('/wallet/scan-history');
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.green),
      );
    }
  }

  void _showInfo(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }
}

class ScanLinePainter extends CustomPainter {
  final double progress;
  final Color color;

  ScanLinePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final scanAreaSize = 280.0;

    final top = centerY - scanAreaSize / 2;
    final bottom = centerY + scanAreaSize / 2;
    final left = centerX - scanAreaSize / 2;
    final right = centerX + scanAreaSize / 2;

    // Draw scanning line
    final lineY = top + (bottom - top) * progress;

    canvas.drawLine(Offset(left + 20, lineY), Offset(right - 20, lineY), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class QrScannerOverlayPainter extends CustomPainter {
  final Color borderColor;
  final double borderRadius;
  final double borderLength;
  final double borderWidth;
  final double cutOutSize;

  QrScannerOverlayPainter({
    required this.borderColor,
    required this.borderRadius,
    required this.borderLength,
    required this.borderWidth,
    required this.cutOutSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final cutOutRect = Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: cutOutSize,
      height: cutOutSize,
    );

    // Draw overlay with cutout
    final overlayPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(
        RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)),
      )
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(overlayPath, paint);

    // Draw corner borders
    final cornerPath = Path();

    // Top-left corner
    cornerPath.moveTo(cutOutRect.left, cutOutRect.top + borderLength);
    cornerPath.lineTo(cutOutRect.left, cutOutRect.top + borderRadius);
    cornerPath.arcToPoint(
      Offset(cutOutRect.left + borderRadius, cutOutRect.top),
      radius: Radius.circular(borderRadius),
    );
    cornerPath.lineTo(cutOutRect.left + borderLength, cutOutRect.top);

    // Top-right corner
    cornerPath.moveTo(cutOutRect.right - borderLength, cutOutRect.top);
    cornerPath.lineTo(cutOutRect.right - borderRadius, cutOutRect.top);
    cornerPath.arcToPoint(
      Offset(cutOutRect.right, cutOutRect.top + borderRadius),
      radius: Radius.circular(borderRadius),
    );
    cornerPath.lineTo(cutOutRect.right, cutOutRect.top + borderLength);

    // Bottom-right corner
    cornerPath.moveTo(cutOutRect.right, cutOutRect.bottom - borderLength);
    cornerPath.lineTo(cutOutRect.right, cutOutRect.bottom - borderRadius);
    cornerPath.arcToPoint(
      Offset(cutOutRect.right - borderRadius, cutOutRect.bottom),
      radius: Radius.circular(borderRadius),
    );
    cornerPath.lineTo(cutOutRect.right - borderLength, cutOutRect.bottom);

    // Bottom-left corner
    cornerPath.moveTo(cutOutRect.left + borderLength, cutOutRect.bottom);
    cornerPath.lineTo(cutOutRect.left + borderRadius, cutOutRect.bottom);
    cornerPath.arcToPoint(
      Offset(cutOutRect.left, cutOutRect.bottom - borderRadius),
      radius: Radius.circular(borderRadius),
    );
    cornerPath.lineTo(cutOutRect.left, cutOutRect.bottom - borderLength);

    canvas.drawPath(cornerPath, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
