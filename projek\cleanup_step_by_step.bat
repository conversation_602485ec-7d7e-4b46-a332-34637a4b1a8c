@echo off
echo ========================================
echo PROJEK PROJECT CLEANUP - STEP BY STEP
echo ========================================

echo.
echo This script will help you clean up duplicate files safely.
echo Each step will ask for confirmation before proceeding.
echo.

:menu
echo ========================================
echo CLEANUP MENU
echo ========================================
echo.
echo 1. Backup important files
echo 2. Remove duplicate main files (7 files)
echo 3. Remove duplicate auth files (5 files)
echo 4. Remove duplicate config files (4 files)
echo 5. Remove duplicate build scripts (12+ files)
echo 6. Remove redundant documentation (40+ files)
echo 7. Test app after cleanup
echo 8. Complete cleanup (all steps)
echo 9. Exit
echo.

set /p choice="Choose an option (1-9): "

if "%choice%"=="1" goto backup
if "%choice%"=="2" goto cleanup_main
if "%choice%"=="3" goto cleanup_auth
if "%choice%"=="4" goto cleanup_config
if "%choice%"=="5" goto cleanup_scripts
if "%choice%"=="6" goto cleanup_docs
if "%choice%"=="7" goto test_app
if "%choice%"=="8" goto complete_cleanup
if "%choice%"=="9" goto exit
goto menu

:backup
echo.
echo ========================================
echo STEP 1: CREATING BACKUPS
echo ========================================

if not exist "backup" mkdir backup
if not exist "backup\main_files" mkdir backup\main_files
if not exist "backup\auth_files" mkdir backup\auth_files
if not exist "backup\config_files" mkdir backup\config_files
if not exist "backup\scripts" mkdir backup\scripts

echo Creating backups of important files...
copy lib\main.dart backup\main_files\ 2>nul
copy lib\main_fixed.dart backup\main_files\ 2>nul
copy lib\main_user.dart backup\main_files\ 2>nul
copy lib\app_fixed.dart backup\main_files\ 2>nul
copy lib\firebase_options*.dart backup\config_files\ 2>nul
copy build_*.bat backup\scripts\ 2>nul

echo ✅ Backup completed in backup\ folder
echo.
pause
goto menu

:cleanup_main
echo.
echo ========================================
echo STEP 2: REMOVING DUPLICATE MAIN FILES
echo ========================================

echo.
echo Files to be removed:
echo - main_debug.dart (debug version)
echo - main_fixed.dart (fixed version)
echo - main_simple.dart (simple test version)
echo - main_marketplace.dart (marketplace only)
echo - main_rider.dart (rider app)
echo - main_seller.dart (seller app)
echo - app_fixed.dart (fixed app wrapper)
echo.
echo ✅ KEEPING: main_user.dart (most complete version)
echo.

set /p confirm="Remove these duplicate main files? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Removing duplicate main files...
del /q lib\main_debug.dart 2>nul && echo ✅ Removed main_debug.dart
del /q lib\main_fixed.dart 2>nul && echo ✅ Removed main_fixed.dart
del /q lib\main_simple.dart 2>nul && echo ✅ Removed main_simple.dart
del /q lib\main_marketplace.dart 2>nul && echo ✅ Removed main_marketplace.dart
del /q lib\main_rider.dart 2>nul && echo ✅ Removed main_rider.dart
del /q lib\main_seller.dart 2>nul && echo ✅ Removed main_seller.dart
del /q lib\app_fixed.dart 2>nul && echo ✅ Removed app_fixed.dart
del /q test_app.dart 2>nul && echo ✅ Removed test_app.dart

echo.
echo ✅ Duplicate main files removed successfully!
pause
goto menu

:cleanup_auth
echo.
echo ========================================
echo STEP 3: REMOVING DUPLICATE AUTH FILES
echo ========================================

echo.
echo Files to be removed:
echo - auth_page_fixed.dart (fixed auth page)
echo - modern_login_page.dart (modern login)
echo - modern_register_page.dart (modern register)
echo - main_app_fixed.dart (fixed main app)
echo.
echo ✅ KEEPING: unified_auth_page.dart (comprehensive auth)
echo.

set /p confirm="Remove these duplicate auth files? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Removing duplicate authentication files...
del /q lib\features\auth\presentation\pages\auth_page_fixed.dart 2>nul && echo ✅ Removed auth_page_fixed.dart
del /q lib\features\auth\presentation\pages\modern_login_page.dart 2>nul && echo ✅ Removed modern_login_page.dart
del /q lib\features\auth\presentation\pages\modern_register_page.dart 2>nul && echo ✅ Removed modern_register_page.dart
del /q lib\features\main\presentation\pages\main_app_fixed.dart 2>nul && echo ✅ Removed main_app_fixed.dart

echo.
echo ✅ Duplicate auth files removed successfully!
pause
goto menu

:cleanup_config
echo.
echo ========================================
echo STEP 4: REMOVING DUPLICATE CONFIG FILES
echo ========================================

echo.
echo Files to be removed:
echo - firebase_options_user.dart (user-specific)
echo - firebase_options_rider.dart (rider-specific)
echo - firebase_options_seller.dart (seller-specific)
echo - firebase_auto.json (auto-generated)
echo.
echo ✅ KEEPING: firebase_options.dart (main config)
echo.

set /p confirm="Remove these duplicate config files? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Removing duplicate config files...
del /q lib\firebase_options_user.dart 2>nul && echo ✅ Removed firebase_options_user.dart
del /q lib\firebase_options_rider.dart 2>nul && echo ✅ Removed firebase_options_rider.dart
del /q lib\firebase_options_seller.dart 2>nul && echo ✅ Removed firebase_options_seller.dart
del /q firebase_auto.json 2>nul && echo ✅ Removed firebase_auto.json
del /q firebase_config_auto.json 2>nul && echo ✅ Removed firebase_config_auto.json

echo.
echo ✅ Duplicate config files removed successfully!
pause
goto menu

:cleanup_scripts
echo.
echo ========================================
echo STEP 5: REMOVING DUPLICATE BUILD SCRIPTS
echo ========================================

echo.
echo ✅ KEEPING: 
echo - build_apps.bat (main build script)
echo - build_user_apk.bat (user app build)
echo - run_tests.bat (testing script)
echo.
echo ❌ REMOVING: 12+ redundant build scripts
echo.

set /p confirm="Remove redundant build scripts? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Removing redundant build scripts...
del /q build_all_apks.bat 2>nul && echo ✅ Removed build_all_apks.bat
del /q build_and_deploy_user.bat 2>nul && echo ✅ Removed build_and_deploy_user.bat
del /q build_apk_simple.bat 2>nul && echo ✅ Removed build_apk_simple.bat
del /q build_dev_apk.bat 2>nul && echo ✅ Removed build_dev_apk.bat
del /q build_low_ram.bat 2>nul && echo ✅ Removed build_low_ram.bat
del /q build_minimal_debug.bat 2>nul && echo ✅ Removed build_minimal_debug.bat
del /q build_simple.bat 2>nul && echo ✅ Removed build_simple.bat
del /q build_user_quick.bat 2>nul && echo ✅ Removed build_user_quick.bat
del /q build_vivo_compatible.bat 2>nul && echo ✅ Removed build_vivo_compatible.bat
del /q quick_build.bat 2>nul && echo ✅ Removed quick_build.bat
del /q quick_deploy_user.bat 2>nul && echo ✅ Removed quick_deploy_user.bat
del /q quick_setup.bat 2>nul && echo ✅ Removed quick_setup.bat

echo.
echo ✅ Redundant build scripts removed successfully!
pause
goto menu

:cleanup_docs
echo.
echo ========================================
echo STEP 6: REMOVING REDUNDANT DOCUMENTATION
echo ========================================

echo.
echo ✅ KEEPING: 
echo - README.md (main documentation)
echo - pubspec.yaml (dependencies)
echo - analysis_options.yaml (code analysis)
echo.
echo ❌ REMOVING: 40+ redundant .md files
echo.

set /p confirm="Remove redundant documentation files? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Removing redundant documentation...
for %%f in (
    ADMIN_DASHBOARD_ENHANCEMENT.md
    APK_BUILD_FIX_SUMMARY.md
    ASSETS_FIX_GUIDE.md
    AUTHENTICATION_FIX_GUIDE.md
    BUILD_SYSTEM_README.md
    COMPILATION_ERRORS_FIXED.md
    DEVELOPMENT_GUIDE.md
    FINAL_CLEANUP_SUMMARY.md
    FIREBASE_CHAT_SETUP.md
    GOOGLE_SIGNIN_FIX.md
    IMPLEMENTATION_GUIDE.md
    MARKETPLACE_README.md
    MODERN_AUTHENTICATION_IMPLEMENTATION.md
    PROJEK_COMPLETE_STATUS_REPORT.md
    RESTRUCTURE_SUMMARY.md
    TEST_SETUP_GUIDE.md
) do (
    del /q "%%f" 2>nul && echo ✅ Removed %%f
)

echo.
echo ✅ Redundant documentation removed successfully!
pause
goto menu

:test_app
echo.
echo ========================================
echo STEP 7: TESTING APP AFTER CLEANUP
echo ========================================

echo Testing if the app builds correctly...
echo.

echo Running flutter clean...
flutter clean

echo Running flutter pub get...
flutter pub get

echo Testing build with main_user.dart...
flutter build apk --debug --flavor user -t lib/main_user.dart

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SUCCESS! App builds correctly after cleanup.
    echo Your project is now clean and functional.
) else (
    echo.
    echo ❌ BUILD FAILED! There might be missing dependencies.
    echo Check the error messages above.
)

echo.
pause
goto menu

:complete_cleanup
echo.
echo ========================================
echo COMPLETE CLEANUP - ALL STEPS
echo ========================================

echo.
echo ⚠️  WARNING: This will run all cleanup steps automatically!
echo.

set /p confirm="Are you sure you want to proceed with complete cleanup? (y/N): "
if /i not "%confirm%"=="y" goto menu

call :backup
call :cleanup_main
call :cleanup_auth
call :cleanup_config
call :cleanup_scripts
call :cleanup_docs
call :test_app

echo.
echo ========================================
echo COMPLETE CLEANUP FINISHED!
echo ========================================
goto menu

:exit
echo.
echo Cleanup script exited.
echo.
pause
exit /b
