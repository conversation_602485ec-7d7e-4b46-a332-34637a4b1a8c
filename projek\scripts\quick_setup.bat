@echo off
echo ========================================
echo Quick Firebase Setup - Projek <PERSON>t App
echo ========================================
echo.

echo This script will automatically:
echo 1. Install Firebase CLI
echo 2. Login to Firebase
echo 3. Initialize your project
echo 4. Deploy security rules
echo 5. Test your app
echo.
echo Press any key to start automatic setup...
pause >nul

echo.
echo 🚀 Starting automatic setup...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  For best results, run as Administrator
    echo Right-click this file and select "Run as administrator"
    echo.
    echo Continue anyway? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" exit /b 1
)

REM Install Node.js check
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Node.js not found!
    echo.
    echo AUTOMATIC SOLUTION:
    echo 1. Downloading Node.js installer...
    echo 2. Please run the installer when it opens
    echo 3. Then restart this script
    echo.
    start https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi
    pause
    exit /b 1
)

echo ✅ Node.js found

REM Install Firebase CLI
echo.
echo Installing Firebase CLI...
call npm install -g firebase-tools
echo ✅ Firebase CLI installed

REM Login to Firebase
echo.
echo Opening Firebase login...
call firebase login --no-localhost
echo ✅ Firebase login complete

REM Initialize Firebase
echo.
echo Initializing Firebase project...
echo.
echo AUTOMATED RESPONSES:
echo - Select: Firestore Database, Authentication
echo - Use an existing project
echo - Select your project from the list
echo - Use default Firestore settings
echo.

REM Create firebase.json if it doesn't exist
if not exist firebase.json (
    echo Creating firebase.json...
    echo {> firebase.json
    echo   "firestore": {>> firebase.json
    echo     "rules": "firestore.rules",>> firebase.json
    echo     "indexes": "firestore.indexes.json">> firebase.json
    echo   }>> firebase.json
    echo }>> firebase.json
)

REM Initialize with predefined settings
call firebase init firestore --project default
echo ✅ Firebase initialized

REM Deploy security rules
echo.
echo Deploying security rules...
call firebase deploy --only firestore:rules
echo ✅ Security rules deployed

REM Test Flutter setup
echo.
echo Testing Flutter setup...
call flutter doctor --android-licenses
call flutter pub get
call flutter analyze

echo.
echo ========================================
echo 🎉 SETUP COMPLETE!
echo ========================================
echo.
echo Your Firebase chat app is ready to run!
echo.
echo To start your app:
echo   flutter run
echo.
echo To test on web:
echo   flutter run -d chrome
echo.
echo Firebase Console: https://console.firebase.google.com
echo.
echo ✅ All done! Your app is ready for testing.
pause
