import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/booking.dart';

// Booking state
class BookingState {
  final List<Booking> bookings;
  final bool isLoading;
  final String? error;
  final Booking? selectedBooking;

  const BookingState({
    this.bookings = const [],
    this.isLoading = false,
    this.error,
    this.selectedBooking,
  });

  BookingState copyWith({
    List<Booking>? bookings,
    bool? isLoading,
    String? error,
    Booking? selectedBooking,
  }) {
    return BookingState(
      bookings: bookings ?? this.bookings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedBooking: selectedBooking ?? this.selectedBooking,
    );
  }
}

// Booking notifier
class BookingNotifier extends StateNotifier<BookingState> {
  BookingNotifier() : super(const BookingState()) {
    _loadBookings();
  }

  final _uuid = const Uuid();

  Future<void> _loadBookings() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Load sample bookings
      final bookings = SampleBookings.bookings;
      
      state = state.copyWith(
        bookings: bookings,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load bookings: $e',
      );
    }
  }

  Future<String> createBooking({
    required String serviceId,
    required String serviceName,
    required String serviceType,
    required String providerId,
    required String providerName,
    required double totalAmount,
    required DateTime serviceDate,
    required String serviceTime,
    required String address,
    required String city,
    required String pincode,
    required double latitude,
    required double longitude,
    required List<BookingItem> items,
    String? specialInstructions,
    String paymentMethod = 'upi',
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 1000));
      
      final bookingId = 'booking_${_uuid.v4().substring(0, 8)}';
      final now = DateTime.now();
      
      final newBooking = Booking(
        id: bookingId,
        customerId: 'user_001', // In real app, get from auth
        customerName: 'John Doe', // In real app, get from user profile
        customerPhone: '+91 **********',
        customerEmail: '<EMAIL>',
        serviceId: serviceId,
        serviceName: serviceName,
        serviceType: serviceType,
        providerId: providerId,
        providerName: providerName,
        totalAmount: totalAmount,
        status: BookingStatus.pending,
        bookingDate: now,
        serviceDate: serviceDate,
        serviceTime: serviceTime,
        address: address,
        city: city,
        pincode: pincode,
        latitude: latitude,
        longitude: longitude,
        specialInstructions: specialInstructions,
        items: items,
        paymentDetails: PaymentDetails(
          paymentId: 'pay_${_uuid.v4().substring(0, 8)}',
          paymentMethod: paymentMethod,
          status: PaymentStatus.pending,
          amount: totalAmount,
        ),
        images: [],
        statusUpdates: [
          BookingStatusUpdate(
            status: BookingStatus.pending,
            message: 'Booking request submitted successfully',
            timestamp: now,
          ),
        ],
        createdAt: now,
        updatedAt: now,
      );
      
      final updatedBookings = [newBooking, ...state.bookings];
      
      state = state.copyWith(
        bookings: updatedBookings,
        isLoading: false,
      );
      
      return bookingId;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create booking: $e',
      );
      rethrow;
    }
  }

  Future<void> updateBookingStatus({
    required String bookingId,
    required BookingStatus newStatus,
    String? message,
    String? updatedBy,
    String? location,
    List<String>? images,
  }) async {
    try {
      final bookingIndex = state.bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex == -1) return;
      
      final booking = state.bookings[bookingIndex];
      final now = DateTime.now();
      
      final statusUpdate = BookingStatusUpdate(
        status: newStatus,
        message: message ?? newStatus.description,
        timestamp: now,
        updatedBy: updatedBy,
        location: location,
        images: images,
      );
      
      final updatedBooking = booking.copyWith(
        status: newStatus,
        statusUpdates: [...booking.statusUpdates, statusUpdate],
        updatedAt: now,
        completedAt: newStatus == BookingStatus.completed ? now : booking.completedAt,
        cancelledAt: newStatus == BookingStatus.cancelled ? now : booking.cancelledAt,
      );
      
      final updatedBookings = List<Booking>.from(state.bookings);
      updatedBookings[bookingIndex] = updatedBooking;
      
      state = state.copyWith(bookings: updatedBookings);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update booking status: $e');
    }
  }

  Future<void> cancelBooking({
    required String bookingId,
    required String reason,
  }) async {
    await updateBookingStatus(
      bookingId: bookingId,
      newStatus: BookingStatus.cancelled,
      message: 'Booking cancelled: $reason',
    );
  }

  Future<void> rescheduleBooking({
    required String bookingId,
    required DateTime newServiceDate,
    required String newServiceTime,
  }) async {
    try {
      final bookingIndex = state.bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex == -1) return;
      
      final booking = state.bookings[bookingIndex];
      final now = DateTime.now();
      
      final statusUpdate = BookingStatusUpdate(
        status: BookingStatus.confirmed,
        message: 'Booking rescheduled to ${newServiceDate.day}/${newServiceDate.month}/${newServiceDate.year} at $newServiceTime',
        timestamp: now,
      );
      
      final updatedBooking = booking.copyWith(
        serviceDate: newServiceDate,
        serviceTime: newServiceTime,
        status: BookingStatus.confirmed,
        statusUpdates: [...booking.statusUpdates, statusUpdate],
        updatedAt: now,
      );
      
      final updatedBookings = List<Booking>.from(state.bookings);
      updatedBookings[bookingIndex] = updatedBooking;
      
      state = state.copyWith(bookings: updatedBookings);
    } catch (e) {
      state = state.copyWith(error: 'Failed to reschedule booking: $e');
    }
  }

  void selectBooking(String bookingId) {
    final booking = state.bookings.firstWhere(
      (b) => b.id == bookingId,
      orElse: () => throw Exception('Booking not found'),
    );
    state = state.copyWith(selectedBooking: booking);
  }

  void clearSelectedBooking() {
    state = state.copyWith(selectedBooking: null);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Get bookings by status
  List<Booking> getBookingsByStatus(BookingStatus status) {
    return state.bookings.where((booking) => booking.status == status).toList();
  }

  // Get active bookings
  List<Booking> getActiveBookings() {
    return state.bookings.where((booking) => booking.status.isActive).toList();
  }

  // Get completed bookings
  List<Booking> getCompletedBookings() {
    return state.bookings.where((booking) => 
      booking.status == BookingStatus.completed).toList();
  }

  // Get cancelled bookings
  List<Booking> getCancelledBookings() {
    return state.bookings.where((booking) => 
      booking.status == BookingStatus.cancelled).toList();
  }
}

// Providers
final bookingProvider = StateNotifierProvider<BookingNotifier, BookingState>((ref) {
  return BookingNotifier();
});

// Computed providers
final activeBookingsProvider = Provider<List<Booking>>((ref) {
  final bookingState = ref.watch(bookingProvider);
  return bookingState.bookings.where((booking) => booking.status.isActive).toList();
});

final completedBookingsProvider = Provider<List<Booking>>((ref) {
  final bookingState = ref.watch(bookingProvider);
  return bookingState.bookings.where((booking) => 
    booking.status == BookingStatus.completed).toList();
});

final bookingCountProvider = Provider<int>((ref) {
  final bookingState = ref.watch(bookingProvider);
  return bookingState.bookings.length;
});

final activeBookingCountProvider = Provider<int>((ref) {
  final activeBookings = ref.watch(activeBookingsProvider);
  return activeBookings.length;
});

// Selected booking provider
final selectedBookingProvider = Provider<Booking?>((ref) {
  final bookingState = ref.watch(bookingProvider);
  return bookingState.selectedBooking;
});
