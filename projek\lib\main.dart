import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:url_strategy/url_strategy.dart';
import 'firebase_options.dart';
import 'help_center.dart';
import 'core/di/injection_simple.dart';
import 'core/utils/app_router.dart';
import 'core/theme/app_theme.dart';

void main() async {
  // Wrap everything in a try-catch for comprehensive error handling
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Configure URL strategy for web
    if (kIsWeb) {
      setPathUrlStrategy();
    }

    // Initialize app with comprehensive error handling
    await _initializeApp();
  } catch (e, stackTrace) {
    // Critical error during app initialization
    debugPrint('CRITICAL ERROR during app initialization: $e');
    debugPrint('Stack trace: $stackTrace');

    // Run minimal error app
    runApp(_buildErrorApp(e, stackTrace));
  }
}

Future<void> _initializeApp() async {
  try {
    debugPrint('🚀 Starting app initialization...');

    // Step 1: Initialize Firebase
    debugPrint('📱 Initializing Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptionsUser.currentPlatform,
    );
    debugPrint('✅ Firebase initialized successfully');

    // Step 2: Initialize dependencies
    debugPrint('🔧 Configuring dependencies...');
    await configureDependencies();
    debugPrint('✅ Dependencies configured successfully');

    // Step 3: Run the app
    debugPrint('🎯 Starting Projek app...');
    runApp(const ProviderScope(child: ProjekApp()));
    debugPrint('✅ App started successfully');
  } catch (e, stackTrace) {
    debugPrint('❌ Error during initialization: $e');
    debugPrint('Stack trace: $stackTrace');

    // Try to run app with error handling
    runApp(ProviderScope(child: ProjekApp(hasInitError: true)));
  }
}

Widget _buildErrorApp(dynamic error, StackTrace stackTrace) {
  return MaterialApp(
    title: 'Projek - Error',
    home: Scaffold(
      backgroundColor: Colors.red.shade50,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 80, color: Colors.red.shade600),
              const SizedBox(height: 24),
              Text(
                'App Initialization Failed',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'We encountered an error while starting the app.',
                style: TextStyle(fontSize: 16, color: Colors.red.shade700),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Error Details:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => main(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouter);
    
    return MaterialApp.router(
      title: 'Projek',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    );
  }
}


class CustomerSupportChatPage extends StatefulWidget {
  const CustomerSupportChatPage({super.key});

  @override
  State<CustomerSupportChatPage> createState() =>
      _CustomerSupportChatPageState();
}

class _CustomerSupportChatPageState extends State<CustomerSupportChatPage> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String? _currentSupportChatId;
  String? _selectedSupportTopic;
  bool _isSendingMessage = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeSupportChat();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeSupportChat() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      // Check for existing active support chat
      final existingChats = await _firestore
          .collection('support_chats')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'active')
          .limit(1)
          .get();

      if (existingChats.docs.isNotEmpty) {
        setState(() {
          _currentSupportChatId = existingChats.docs.first.id;
        });
      } else {
        // Show topic selection first
        _showSupportTopicDialog();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize support chat: ${e.toString()}';
      });
    }
  }

  Future<void> _createSupportChat(String topic) async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final userName = await _getUserName();

      final chatDoc = await _firestore.collection('support_chats').add({
        'userId': user.uid,
        'userEmail': user.email,
        'userName': userName,
        'status': 'active',
        'priority': 'normal',
        'topic': topic,
        'createdAt': FieldValue.serverTimestamp(),
        'lastMessageAt': FieldValue.serverTimestamp(),
        'assignedAgent': null,
        'isReadBySupport': false,
      });

      setState(() {
        _currentSupportChatId = chatDoc.id;
        _selectedSupportTopic = topic;
      });

      // Send initial welcome message
      await _sendInitialMessage(topic);
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to create support chat: ${e.toString()}';
      });
    }
  }

  Future<String> _getUserName() async {
    final user = _auth.currentUser;
    if (user == null) return 'User';

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      return userDoc.data()?['name'] ?? user.displayName ?? 'User';
    } catch (e) {
      return user.displayName ?? 'User';
    }
  }

  Future<void> _sendInitialMessage(String topic) async {
    if (_currentSupportChatId == null) return;

    final user = _auth.currentUser;
    if (user == null) return;

    await _firestore
        .collection('support_chats')
        .doc(_currentSupportChatId)
        .collection('messages')
        .add({
          'text': 'Hello! I need help with $topic.',
          'senderId': user.uid,
          'senderEmail': user.email,
          'senderName': await _getUserName(),
          'senderType': 'user',
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'sent',
          'isReadBySupport': false,
        });
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty ||
        _currentSupportChatId == null) {
      return;
    }

    final user = _auth.currentUser;
    if (user == null) return;

    final messageText = _messageController.text.trim();
    if (messageText.length > 1000) {
      setState(() {
        _errorMessage =
            'Message is too long. Please keep it under 1000 characters.';
      });
      return;
    }

    setState(() {
      _isSendingMessage = true;
      _errorMessage = null;
    });

    try {
      final userName = await _getUserName();

      await _firestore
          .collection('support_chats')
          .doc(_currentSupportChatId)
          .collection('messages')
          .add({
            'text': messageText,
            'senderId': user.uid,
            'senderEmail': user.email,
            'senderName': userName,
            'senderType': 'user',
            'timestamp': FieldValue.serverTimestamp(),
            'status': 'sent',
            'isReadBySupport': false,
          });

      // Update chat last message timestamp
      await _firestore
          .collection('support_chats')
          .doc(_currentSupportChatId)
          .update({
            'lastMessageAt': FieldValue.serverTimestamp(),
            'isReadBySupport': false,
          });

      _messageController.clear();

      // Scroll to bottom
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to send message: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isSendingMessage = false;
      });
    }
  }

  void _showSupportTopicDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('How can we help you?'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Please select the topic that best describes your inquiry:',
              ),
              const SizedBox(height: 16),
              _buildTopicOption('Account & Login Issues', Icons.account_circle),
              _buildTopicOption('Service Problems', Icons.build),
              _buildTopicOption('Billing & Payments', Icons.payment),
              _buildTopicOption('Order Support', Icons.shopping_cart),
              _buildTopicOption('Technical Issues', Icons.computer),
              _buildTopicOption('General Questions', Icons.help),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTopicOption(String topic, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(topic),
      onTap: () {
        Navigator.of(context).pop();
        _createSupportChat(topic);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Support'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const HelpCenterPage()),
              );
            },
            icon: const Icon(Icons.help_center),
            tooltip: 'Help Center',
          ),
        ],
      ),
      body: Column(
        children: [
          // Support Info Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.support_agent,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Live Customer Support',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _selectedSupportTopic != null
                      ? 'Topic: $_selectedSupportTopic'
                      : 'We\'re here to help with your questions and concerns',
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Support team is online',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(
                          context,
                        ).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Messages Area
          Expanded(
            child: _currentSupportChatId != null
                ? _buildMessagesArea()
                : _buildWaitingArea(),
          ),

          // Error Message
          if (_errorMessage != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: Theme.of(context).colorScheme.errorContainer,
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                ),
              ),
            ),

          // Message Input
          if (_currentSupportChatId != null) _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildWaitingArea() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Setting up your support chat...'),
        ],
      ),
    );
  }

  Widget _buildMessagesArea() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore
          .collection('support_chats')
          .doc(_currentSupportChatId)
          .collection('messages')
          .orderBy('timestamp', descending: false)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Text('Error loading messages: ${snapshot.error}'),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final messages = snapshot.data?.docs ?? [];

        if (messages.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Start the conversation!',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Send a message to get help from our support team.',
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index].data() as Map<String, dynamic>;
            final isUser = message['senderType'] == 'user';
            final timestamp = message['timestamp'] as Timestamp?;

            return _buildMessageBubble(
              message['text'] ?? '',
              isUser,
              message['senderName'] ?? 'Unknown',
              timestamp,
            );
          },
        );
      },
    );
  }

  Widget _buildMessageBubble(
    String text,
    bool isUser,
    String senderName,
    Timestamp? timestamp,
  ) {
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isUser
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isUser)
              Text(
                senderName,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            if (!isUser) const SizedBox(height: 4),
            Text(
              text,
              style: TextStyle(
                color: isUser
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            if (timestamp != null) ...[
              const SizedBox(height: 4),
              Text(
                DateFormat('MMM d, h:mm a').format(timestamp.toDate()),
                style: TextStyle(
                  fontSize: 10,
                  color: isUser
                      ? Colors.white70
                      : Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type your message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _isSendingMessage ? null : _sendMessage,
            icon: _isSendingMessage
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.send),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
