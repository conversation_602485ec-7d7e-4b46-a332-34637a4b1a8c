import 'package:hive_flutter/hive_flutter.dart';
import '../config/constants.dart';

class HiveService {
  static late Box _userBox;
  static late Box _settingsBox;
  static late Box _cacheBox;
  
  static Future<void> initialize() async {
    // Open boxes
    _userBox = await Hive.openBox('user_data');
    _settingsBox = await Hive.openBox('app_settings');
    _cacheBox = await Hive.openBox('cache_data');
  }
  
  // User Data Methods
  static Future<void> saveUserToken(String token) async {
    await _userBox.put(AppConstants.userTokenKey, token);
  }
  
  static String? getUserToken() {
    return _userBox.get(AppConstants.userTokenKey);
  }
  
  static Future<void> saveUserId(String userId) async {
    await _userBox.put(AppConstants.userIdKey, userId);
  }
  
  static String? getUserId() {
    return _userBox.get(AppConstants.userIdKey);
  }
  
  static Future<void> clearUserData() async {
    await _userBox.clear();
  }
  
  // Settings Methods
  static Future<void> saveThemeMode(String themeMode) async {
    await _settingsBox.put(AppConstants.themeKey, themeMode);
  }
  
  static String getThemeMode() {
    return _settingsBox.get(AppConstants.themeKey, defaultValue: 'system');
  }
  
  static Future<void> saveLanguage(String languageCode) async {
    await _settingsBox.put(AppConstants.languageKey, languageCode);
  }
  
  static String getLanguage() {
    return _settingsBox.get(AppConstants.languageKey, defaultValue: 'en');
  }
  
  static Future<void> saveOnboardingStatus(bool completed) async {
    await _settingsBox.put(AppConstants.onboardingKey, completed);
  }
  
  static bool getOnboardingStatus() {
    return _settingsBox.get(AppConstants.onboardingKey, defaultValue: false);
  }
  
  static Future<void> saveLocationPermission(bool granted) async {
    await _settingsBox.put(AppConstants.locationPermissionKey, granted);
  }
  
  static bool getLocationPermission() {
    return _settingsBox.get(AppConstants.locationPermissionKey, defaultValue: false);
  }
  
  // Cache Methods
  static Future<void> saveToCache(String key, dynamic value) async {
    await _cacheBox.put(key, {
      'data': value,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  static T? getFromCache<T>(String key, {Duration? maxAge}) {
    final cached = _cacheBox.get(key);
    if (cached == null) return null;
    
    if (maxAge != null) {
      final timestamp = cached['timestamp'] as int;
      final age = DateTime.now().millisecondsSinceEpoch - timestamp;
      if (age > maxAge.inMilliseconds) {
        _cacheBox.delete(key);
        return null;
      }
    }
    
    return cached['data'] as T?;
  }
  
  static Future<void> clearCache() async {
    await _cacheBox.clear();
  }
  
  static Future<void> clearExpiredCache() async {
    final keys = _cacheBox.keys.toList();
    final now = DateTime.now().millisecondsSinceEpoch;
    
    for (final key in keys) {
      final cached = _cacheBox.get(key);
      if (cached != null) {
        final timestamp = cached['timestamp'] as int;
        final age = now - timestamp;
        if (age > AppConstants.cacheExpiry.inMilliseconds) {
          await _cacheBox.delete(key);
        }
      }
    }
  }
  
  // Generic Methods
  static Future<void> saveData(String boxName, String key, dynamic value) async {
    final box = await Hive.openBox(boxName);
    await box.put(key, value);
  }
  
  static T? getData<T>(String boxName, String key, {T? defaultValue}) {
    final box = Hive.box(boxName);
    return box.get(key, defaultValue: defaultValue);
  }
  
  static Future<void> deleteData(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    await box.delete(key);
  }
  
  static Future<void> clearBox(String boxName) async {
    final box = await Hive.openBox(boxName);
    await box.clear();
  }
  
  // Offline Data Methods
  static Future<void> saveOfflineData(String collection, String id, Map<String, dynamic> data) async {
    final box = await Hive.openBox('offline_$collection');
    await box.put(id, {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'synced': false,
    });
  }
  
  static List<Map<String, dynamic>> getUnsyncedData(String collection) {
    final box = Hive.box('offline_$collection');
    final unsyncedData = <Map<String, dynamic>>[];
    
    for (final key in box.keys) {
      final item = box.get(key);
      if (item != null && !(item['synced'] as bool)) {
        unsyncedData.add({
          'id': key,
          'data': item['data'],
          'timestamp': item['timestamp'],
        });
      }
    }
    
    return unsyncedData;
  }
  
  static Future<void> markAsSynced(String collection, String id) async {
    final box = await Hive.openBox('offline_$collection');
    final item = box.get(id);
    if (item != null) {
      item['synced'] = true;
      await box.put(id, item);
    }
  }
  
  static Future<void> clearSyncedData(String collection) async {
    final box = await Hive.openBox('offline_$collection');
    final keysToDelete = <dynamic>[];
    
    for (final key in box.keys) {
      final item = box.get(key);
      if (item != null && (item['synced'] as bool)) {
        keysToDelete.add(key);
      }
    }
    
    for (final key in keysToDelete) {
      await box.delete(key);
    }
  }
}
