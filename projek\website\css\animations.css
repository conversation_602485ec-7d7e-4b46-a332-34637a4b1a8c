/* Fade-in animation styles */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delay for multiple elements */
.fade-in:nth-child(1) {
  transition-delay: 0.1s;
}

.fade-in:nth-child(2) {
  transition-delay: 0.3s;
}

.fade-in:nth-child(3) {
  transition-delay: 0.5s;
}

.fade-in:nth-child(4) {
  transition-delay: 0.7s;
}

.fade-in:nth-child(5) {
  transition-delay: 0.9s;
}

/* Pulse animation for CTA buttons */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(20, 110, 180, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(20, 110, 180, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(20, 110, 180, 0);
  }
}

@keyframes pulseGreen {
  0% {
    box-shadow: 0 0 0 0 rgba(19, 136, 8, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(19, 136, 8, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(19, 136, 8, 0);
  }
}

@keyframes pulseSaffron {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 153, 51, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 153, 51, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 153, 51, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

.pulse-green {
  animation: pulseGreen 2s infinite;
}

.pulse-saffron {
  animation: pulseSaffron 2s infinite;
}

/* Bounce animation for icons */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.bounce {
  animation: bounce 2s infinite;
}

/* Shimmer effect for cards */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  background-size: 1000px 100%;
}