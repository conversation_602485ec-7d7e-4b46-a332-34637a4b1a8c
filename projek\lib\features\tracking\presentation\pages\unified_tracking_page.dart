import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/unified_order.dart';
import '../../data/services/unified_tracking_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class UnifiedTrackingPage extends ConsumerStatefulWidget {
  final String orderId;

  const UnifiedTrackingPage({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<UnifiedTrackingPage> createState() => _UnifiedTrackingPageState();
}

class _UnifiedTrackingPageState extends ConsumerState<UnifiedTrackingPage>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  UnifiedOrder? _order;
  LocationPoint? _currentRiderLocation;
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  StreamSubscription<DocumentSnapshot>? _orderSubscription;
  StreamSubscription<DocumentSnapshot>? _trackingSubscription;
  
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadOrderData();
    _setupRealTimeTracking();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _orderSubscription?.cancel();
    _trackingSubscription?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _pulseAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadOrderData() async {
    try {
      final order = await UnifiedTrackingService.getOrderById(widget.orderId);
      setState(() {
        _order = order;
        _isLoading = false;
      });
      _updateMapMarkers();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _setupRealTimeTracking() {
    // Listen to order updates
    _orderSubscription = UnifiedTrackingService.getOrderStream(widget.orderId)
        .listen(_handleOrderUpdate);
    
    // Listen to tracking updates
    _trackingSubscription = UnifiedTrackingService.getTrackingStream(widget.orderId)
        .listen(_handleTrackingUpdate);
  }

  void _handleOrderUpdate(DocumentSnapshot snapshot) {
    if (snapshot.exists) {
      final order = UnifiedOrder.fromJson(snapshot.data() as Map<String, dynamic>);
      setState(() => _order = order);
      _updateMapMarkers();
    }
  }

  void _handleTrackingUpdate(DocumentSnapshot snapshot) {
    if (snapshot.exists) {
      final data = snapshot.data() as Map<String, dynamic>;
      if (data['currentLocation'] != null) {
        final location = LocationPoint.fromJson(data['currentLocation']);
        setState(() => _currentRiderLocation = location);
        _updateRiderMarker();
      }
    }
  }

  void _updateMapMarkers() {
    if (_order == null) return;

    final markers = <Marker>{};

    // Pickup location marker
    markers.add(Marker(
      markerId: const MarkerId('pickup'),
      position: LatLng(
        _order!.pickupLocation.latitude,
        _order!.pickupLocation.longitude,
      ),
      infoWindow: InfoWindow(
        title: 'Pickup Location',
        snippet: _order!.pickupLocation.address ?? 'Pickup Point',
      ),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
    ));

    // Delivery location marker
    markers.add(Marker(
      markerId: const MarkerId('delivery'),
      position: LatLng(
        _order!.deliveryLocation.latitude,
        _order!.deliveryLocation.longitude,
      ),
      infoWindow: InfoWindow(
        title: 'Delivery Location',
        snippet: _order!.deliveryLocation.address ?? 'Delivery Point',
      ),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
    ));

    setState(() => _markers = markers);
    _updateRiderMarker();
  }

  void _updateRiderMarker() {
    if (_currentRiderLocation == null || _order?.rider == null) return;

    final riderMarker = Marker(
      markerId: const MarkerId('rider'),
      position: LatLng(
        _currentRiderLocation!.latitude,
        _currentRiderLocation!.longitude,
      ),
      infoWindow: InfoWindow(
        title: _order!.rider!.name,
        snippet: 'Delivery Partner',
      ),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
    );

    setState(() {
      _markers.removeWhere((marker) => marker.markerId.value == 'rider');
      _markers.add(riderMarker);
    });

    // Update camera to show rider location
    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(_currentRiderLocation!.latitude, _currentRiderLocation!.longitude),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Tracking'),
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Tracking'),
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: AppColors.error),
              const SizedBox(height: 16),
              Text('Error loading tracking data', style: AppTextStyles.titleLarge),
              const SizedBox(height: 8),
              Text(_error!, style: AppTextStyles.bodyMedium),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadOrderData();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Track Order #${_order!.orderNumber}'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _refreshTracking,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Order status header
          _buildStatusHeader(),
          
          // Map view
          Expanded(
            flex: 3,
            child: _buildMapView(),
          ),
          
          // Order details
          Expanded(
            flex: 2,
            child: _buildOrderDetails(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryBlue, AppColors.secondaryOrange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getStatusIcon(_order!.status),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _order!.statusDisplayText,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStatusDescription(_order!.status),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                if (_order!.tracking?.estimatedDeliveryTime != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.access_time, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'ETA: ${_formatTime(_order!.tracking!.estimatedDeliveryTime!)}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapView() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: GoogleMap(
        onMapCreated: (GoogleMapController controller) {
          _mapController = controller;
          _fitMarkersInView();
        },
        initialCameraPosition: CameraPosition(
          target: LatLng(
            _order!.deliveryLocation.latitude,
            _order!.deliveryLocation.longitude,
          ),
          zoom: 14,
        ),
        markers: _markers,
        polylines: _polylines,
        myLocationEnabled: false,
        myLocationButtonEnabled: false,
        zoomControlsEnabled: true,
        mapToolbarEnabled: false,
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order info
            _buildInfoSection(),
            const SizedBox(height: 16),
            
            // Rider info (if available)
            if (_order!.rider != null) ...[
              _buildRiderSection(),
              const SizedBox(height: 16),
            ],
            
            // Timeline
            _buildTimelineSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Information',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildInfoRow('Order Number', _order!.orderNumber),
        _buildInfoRow('Order Type', _order!.type.toString().split('.').last.toUpperCase()),
        _buildInfoRow('Total Amount', '₹${_order!.totalAmount.toStringAsFixed(2)}'),
        _buildInfoRow('Payment Status', _order!.paymentStatus.toUpperCase()),
      ],
    );
  }

  Widget _buildRiderSection() {
    final rider = _order!.rider!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.delivery_dining, color: AppColors.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Delivery Partner',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.primaryBlue,
                child: Text(
                  rider.name[0].toUpperCase(),
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      rider.name,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (rider.phone != null)
                      Text(
                        rider.phone!,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                  ],
                ),
              ),
              if (rider.phone != null)
                IconButton(
                  onPressed: () => _callRider(rider.phone!),
                  icon: Icon(Icons.phone, color: AppColors.primaryBlue),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.white,
                    shape: const CircleBorder(),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Timeline',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...(_order!.milestones.reversed.take(5).map(_buildTimelineItem)),
      ],
    );
  }

  Widget _buildTimelineItem(OrderMilestone milestone) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.description ?? milestone.status.toString(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _formatDateTime(milestone.timestamp),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _fitMarkersInView() {
    if (_markers.isEmpty || _mapController == null) return;

    final bounds = _calculateBounds(_markers);
    _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 100),
    );
  }

  LatLngBounds _calculateBounds(Set<Marker> markers) {
    double minLat = markers.first.position.latitude;
    double maxLat = markers.first.position.latitude;
    double minLng = markers.first.position.longitude;
    double maxLng = markers.first.position.longitude;

    for (final marker in markers) {
      minLat = minLat < marker.position.latitude ? minLat : marker.position.latitude;
      maxLat = maxLat > marker.position.latitude ? maxLat : marker.position.latitude;
      minLng = minLng < marker.position.longitude ? minLng : marker.position.longitude;
      maxLng = maxLng > marker.position.longitude ? maxLng : marker.position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  void _refreshTracking() {
    _loadOrderData();
  }

  void _callRider(String phoneNumber) {
    // Implement phone call functionality
    debugPrint('Calling rider: $phoneNumber');
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
      case OrderStatus.confirmed:
        return Icons.receipt;
      case OrderStatus.preparing:
        return Icons.kitchen;
      case OrderStatus.ready:
        return Icons.check_circle;
      case OrderStatus.assigned:
      case OrderStatus.pickedUp:
        return Icons.local_shipping;
      case OrderStatus.inTransit:
        return Icons.directions_car;
      case OrderStatus.nearDestination:
        return Icons.location_on;
      case OrderStatus.delivered:
      case OrderStatus.completed:
        return Icons.done_all;
      default:
        return Icons.info;
    }
  }

  String _getStatusDescription(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 'Your order has been placed successfully';
      case OrderStatus.confirmed:
        return 'Order confirmed and being prepared';
      case OrderStatus.preparing:
        return 'Your order is being prepared';
      case OrderStatus.ready:
        return 'Order is ready for pickup';
      case OrderStatus.assigned:
        return 'Delivery partner assigned';
      case OrderStatus.pickedUp:
        return 'Order picked up and on the way';
      case OrderStatus.inTransit:
        return 'Your order is on the way to you';
      case OrderStatus.nearDestination:
        return 'Delivery partner is nearby';
      case OrderStatus.delivered:
        return 'Order delivered successfully';
      case OrderStatus.completed:
        return 'Order completed';
      default:
        return 'Order status updated';
    }
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour;
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '${hour == 0 ? 12 : hour}:${dateTime.minute.toString().padLeft(2, '0')} $period';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${_formatTime(dateTime)}';
  }
}
