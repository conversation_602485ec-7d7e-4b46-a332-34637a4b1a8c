import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment_method.g.dart';

@JsonSerializable()
class PaymentMethod extends Equatable {
  final String id;
  final String type;
  final String name;
  final bool isDefault;
  final Map<String, dynamic> metadata;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.name,
    this.isDefault = false,
    this.metadata = const {},
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodToJson(this);

  @override
  List<Object?> get props => [id, type, name, isDefault, metadata];
}
