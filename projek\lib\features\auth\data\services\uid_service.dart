import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/uid_generator.dart';

/// UID Service for managing user identification in Projek app
class UIDService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Generate and store UID for user
  Future<UserUID> createUserUID({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Generate UID
      final userUID = UserUID.create(
        fullName: fullName,
        dateOfBirth: dateOfBirth,
        phoneNumber: phoneNumber,
        metadata: {
          'sessionId': '9c7d58e8-9e5b-4573-abed-a3d7773c9ec3',
          'appVersion': 'Projek-MyIndiaFirst-v1.0',
          'createdBy': 'ProjekApp',
          ...?additionalData,
        },
      );

      // Check if UID already exists
      final existingUID = await _checkUIDExists(userUID.uid);
      if (existingUID) {
        throw Exception('UID already exists. Please try again.');
      }

      // Store in Firestore
      await _storeUID(userUID);

      // Link to current user if authenticated
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await _linkUIDToUser(currentUser.uid, userUID.uid);
      }

      return userUID;
    } catch (e) {
      throw Exception('Failed to create UID: $e');
    }
  }

  /// Create UID with specific UUID (for special users)
  Future<UserUID> createUserUIDWithCustomUUID({
    required String fullName,
    required DateTime dateOfBirth,
    required String customUUID,
    String? phoneNumber,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Validate UUID format
      if (!_isValidUUID(customUUID)) {
        throw Exception('Invalid UUID format provided');
      }

      // Create UserUID with custom UUID
      final userUID = UserUID(
        uid: customUUID,
        fullName: fullName,
        dateOfBirth: dateOfBirth,
        phoneNumber: phoneNumber,
        createdAt: DateTime.now(),
        metadata: {
          'sessionId': customUUID,
          'appVersion': 'Projek-MyIndiaFirst-v1.0',
          'createdBy': 'ProjekApp-CustomUUID',
          'isCustomUUID': true,
          ...?additionalData,
        },
      );

      // Check if UID already exists
      final existingUID = await _checkUIDExists(userUID.uid);
      if (existingUID) {
        throw Exception('Custom UUID already exists. Please contact support.');
      }

      // Store in Firestore
      await _storeUID(userUID);

      // Link to current user if authenticated
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await _linkUIDToUser(currentUser.uid, userUID.uid);
      }

      return userUID;
    } catch (e) {
      throw Exception('Failed to create UID with custom UUID: $e');
    }
  }

  /// Validate UUID format
  bool _isValidUUID(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
      caseSensitive: false,
    );
    return uuidRegex.hasMatch(uuid);
  }

  /// Check if UID already exists
  Future<bool> _checkUIDExists(String uid) async {
    final doc = await _firestore.collection('user_uids').doc(uid).get();
    return doc.exists;
  }

  /// Store UID in Firestore
  Future<void> _storeUID(UserUID userUID) async {
    await _firestore.collection('user_uids').doc(userUID.uid).set({
      ...userUID.toJson(),
      'status': 'active',
      'verificationLevel': 'basic',
      'lastUsed': FieldValue.serverTimestamp(),
    });
  }

  /// Link UID to Firebase user
  Future<void> _linkUIDToUser(String firebaseUID, String customUID) async {
    await _firestore.collection('users').doc(firebaseUID).update({
      'customUID': customUID,
      'uidLinkedAt': FieldValue.serverTimestamp(),
    });
  }

  /// Get UID by Firebase user ID
  Future<UserUID?> getUserUID(String firebaseUID) async {
    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(firebaseUID)
          .get();

      if (!userDoc.exists) return null;

      final customUID = userDoc.data()?['customUID'] as String?;
      if (customUID == null) return null;

      final uidDoc = await _firestore
          .collection('user_uids')
          .doc(customUID)
          .get();

      if (!uidDoc.exists) return null;

      return UserUID.fromJson(uidDoc.data()!);
    } catch (e) {
      throw Exception('Failed to get UID: $e');
    }
  }

  /// Search UID by partial information
  Future<List<UserUID>> searchUIDs({
    String? nameQuery,
    DateTime? dateOfBirth,
    String? phoneNumber,
  }) async {
    try {
      Query query = _firestore.collection('user_uids');

      if (nameQuery != null && nameQuery.isNotEmpty) {
        query = query
            .where('fullName', isGreaterThanOrEqualTo: nameQuery)
            .where('fullName', isLessThan: '$nameQuery\uf8ff');
      }

      if (phoneNumber != null) {
        query = query.where('phoneNumber', isEqualTo: phoneNumber);
      }

      final snapshot = await query.limit(50).get();

      final uids = snapshot.docs.map((doc) {
        return UserUID.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();

      // Filter by date of birth if provided
      if (dateOfBirth != null) {
        return uids
            .where(
              (uid) =>
                  uid.dateOfBirth.year == dateOfBirth.year &&
                  uid.dateOfBirth.month == dateOfBirth.month &&
                  uid.dateOfBirth.day == dateOfBirth.day,
            )
            .toList();
      }

      return uids;
    } catch (e) {
      throw Exception('Failed to search UIDs: $e');
    }
  }

  /// Validate and verify UID
  Future<Map<String, dynamic>> verifyUID(String uid) async {
    try {
      // Check format validity
      if (!UIDGenerator.isValidUID(uid)) {
        return {'isValid': false, 'error': 'Invalid UID format'};
      }

      // Check if exists in database
      final doc = await _firestore.collection('user_uids').doc(uid).get();

      if (!doc.exists) {
        return {'isValid': false, 'error': 'UID not found in database'};
      }

      final data = doc.data()!;
      final userUID = UserUID.fromJson(data);

      // Update last used timestamp
      await _firestore.collection('user_uids').doc(uid).update({
        'lastUsed': FieldValue.serverTimestamp(),
        'verificationCount': FieldValue.increment(1),
      });

      return {
        'isValid': true,
        'userUID': userUID,
        'uidInfo': UIDGenerator.parseUID(uid),
        'status': data['status'],
        'verificationLevel': data['verificationLevel'],
      };
    } catch (e) {
      return {'isValid': false, 'error': 'Verification failed: $e'};
    }
  }

  /// Generate UID suggestions for user
  Future<List<String>> generateUIDSuggestions({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
  }) async {
    final suggestions = UIDGenerator.generateUIDSuggestions(
      fullName: fullName,
      dateOfBirth: dateOfBirth,
      phoneNumber: phoneNumber,
      count: 5,
    );

    // Filter out existing UIDs
    final availableSuggestions = <String>[];

    for (final suggestion in suggestions) {
      final exists = await _checkUIDExists(suggestion);
      if (!exists) {
        availableSuggestions.add(suggestion);
      }
    }

    return availableSuggestions;
  }

  /// Update UID information
  Future<void> updateUID(String uid, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('user_uids').doc(uid).update({
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update UID: $e');
    }
  }

  /// Deactivate UID
  Future<void> deactivateUID(String uid, String reason) async {
    try {
      await _firestore.collection('user_uids').doc(uid).update({
        'status': 'deactivated',
        'deactivatedAt': FieldValue.serverTimestamp(),
        'deactivationReason': reason,
      });
    } catch (e) {
      throw Exception('Failed to deactivate UID: $e');
    }
  }

  /// Get UID statistics
  Future<Map<String, dynamic>> getUIDStats() async {
    try {
      final snapshot = await _firestore.collection('user_uids').get();

      int totalUIDs = snapshot.docs.length;
      int activeUIDs = 0;
      int verifiedUIDs = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data();
        if (data['status'] == 'active') activeUIDs++;
        if (data['verificationLevel'] == 'verified') verifiedUIDs++;
      }

      return {
        'totalUIDs': totalUIDs,
        'activeUIDs': activeUIDs,
        'verifiedUIDs': verifiedUIDs,
        'deactivatedUIDs': totalUIDs - activeUIDs,
      };
    } catch (e) {
      throw Exception('Failed to get UID stats: $e');
    }
  }
}

/// Riverpod providers for UID service
final uidServiceProvider = Provider<UIDService>((ref) {
  return UIDService();
});

final currentUserUIDProvider = FutureProvider<UserUID?>((ref) async {
  final uidService = ref.read(uidServiceProvider);
  final user = FirebaseAuth.instance.currentUser;

  if (user == null) return null;

  return await uidService.getUserUID(user.uid);
});

final uidVerificationProvider =
    FutureProvider.family<Map<String, dynamic>, String>((ref, uid) async {
      final uidService = ref.read(uidServiceProvider);
      return await uidService.verifyUID(uid);
    });

/// Example Usage:
///
/// // Create UID for new user
/// final uidService = ref.read(uidServiceProvider);
/// final userUID = await uidService.createUserUID(
///   fullName: 'Rahul Kumar',
///   dateOfBirth: DateTime(1995, 8, 15),
///   phoneNumber: '+************',
/// );
///
/// // Verify existing UID
/// final verification = await uidService.verifyUID('RAHKUM150895567A1B2');
///
/// // Get current user's UID
/// final currentUID = ref.watch(currentUserUIDProvider);
///
/// // Search UIDs
/// final searchResults = await uidService.searchUIDs(
///   nameQuery: 'Rahul',
///   dateOfBirth: DateTime(1995, 8, 15),
/// );
