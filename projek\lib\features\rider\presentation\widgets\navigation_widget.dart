import 'package:flutter/material.dart';
import '../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class NavigationWidget extends StatelessWidget {
  final OptimizedRoute route;

  const NavigationWidget({
    super.key,
    required this.route,
  });

  @override
  Widget build(BuildContext context) {
    final nextInstruction = _getNextInstruction();
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Distance and Time
          Row(
            children: [
              Icon(
                Icons.navigation,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                route.formattedDistance,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.access_time,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                route.formattedDuration,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          if (nextInstruction != null) ...[
            const SizedBox(height: 12),
            
            // Next Instruction
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getInstructionIcon(nextInstruction.maneuver),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (nextInstruction.distance != null)
                        Text(
                          'In ${_formatDistance(nextInstruction.distance!)}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      Text(
                        nextInstruction.instruction ?? 'Continue straight',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (nextInstruction.streetName != null)
                        Text(
                          'on ${nextInstruction.streetName}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
          
          // Traffic Warning
          if (route.warnings.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColors.warning.withValues(alpha: 0.5)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: AppColors.warning,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      route.warnings.first,
                      style: TextStyle(
                        color: AppColors.warning,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  RoutePoint? _getNextInstruction() {
    if (route.points.isEmpty) return null;
    
    // Find the next point with an instruction
    for (final point in route.points) {
      if (point.instruction != null && point.instruction!.isNotEmpty) {
        return point;
      }
    }
    
    return route.points.first;
  }

  IconData _getInstructionIcon(String? maneuver) {
    if (maneuver == null) return Icons.straight;
    
    switch (maneuver.toLowerCase()) {
      case 'turn-left':
      case 'left':
        return Icons.turn_left;
      case 'turn-right':
      case 'right':
        return Icons.turn_right;
      case 'turn-slight-left':
        return Icons.turn_slight_left;
      case 'turn-slight-right':
        return Icons.turn_slight_right;
      case 'turn-sharp-left':
        return Icons.turn_sharp_left;
      case 'turn-sharp-right':
        return Icons.turn_sharp_right;
      case 'uturn':
      case 'u-turn':
        return Icons.u_turn_left;
      case 'merge':
        return Icons.merge;
      case 'roundabout':
        return Icons.roundabout_left;
      case 'exit':
        return Icons.exit_to_app;
      case 'continue':
      case 'straight':
        return Icons.straight;
      default:
        return Icons.navigation;
    }
  }

  String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(0)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(1)}km';
    }
  }
}
