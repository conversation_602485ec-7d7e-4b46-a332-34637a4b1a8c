:root {
  /* Official Indian Flag Colors - Improved Color Matching */
  --saffron: #FF9933;           /* Official Indian Flag Saffron */
  --white: #FFFFFF;             /* Pure White */
  --green: #138808;             /* Official Indian Flag Green */
  --navy-blue: #000080;         /* Ashoka Chakra Blue */

  /* Enhanced Color Palette */
  --peacock-blue: #146eb4;      /* Primary Blue - keeping for consistency */
  --deep-saffron: #FF6600;      /* Deeper saffron for accents */
  --forest-green: #0F5132;      /* Deeper green for text/accents */
  --cream: #FFF8DC;             /* Warm cream background */
  --light-gray: #F8F9FA;        /* Softer light gray */
  --medium-gray: #6C757D;       /* Medium gray for text */
  --dark-gray: #212529;         /* Darker gray for better contrast */

  /* Functional Colors */
  --shadow: rgba(0, 0, 0, 0.15);
  --shadow-light: rgba(0, 0, 0, 0.08);
  --glass-bg: rgba(255, 255, 255, 0.9);
  --overlay-dark: rgba(0, 0, 0, 0.4);

  /* Gradient Colors */
  --gradient-saffron: linear-gradient(135deg, #FF9933 0%, #FF6600 100%);
  --gradient-green: linear-gradient(135deg, #138808 0%, #0F5132 100%);
  --gradient-blue: linear-gradient(135deg, #146eb4 0%, #000080 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--medium-gray);
  background-color: var(--white);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--white);
  box-shadow: 0 2px 10px var(--shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  margin-right: 10px;
}

.nav-links {
  display: flex;
  list-style: none;
}

.nav-links li {
  margin-left: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--peacock-blue);
}

.nav-links .login-btn {
  background-color: var(--peacock-blue);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.nav-links .login-btn:hover {
  background-color: #0d5a9f;
  color: var(--white);
}

.menu-toggle {
  display: none;
  cursor: pointer;
  font-size: 1.5rem;
}

/* Hero Section */
.hero {
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  animation: animatedBackground 20s ease-in-out infinite alternate;
  /* Add overlay for better text contrast */
  background-blend-mode: overlay;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  z-index: 1;
}

.hero .container {
  position: relative;
  z-index: 2;
}

@keyframes animatedBackground {
  0% {
    background-size: 100% 100%;
  }
  100% {
    background-size: 110% 110%;
    background-position: center center;
  }
}

.hero h1,
.hero p {
  color: var(--white); /* Explicitly set hero text to white */
}

.rainbow-text span {
  display: inline-block;
  transition: all 0.3s ease;
  animation: colorPulse 3s ease-in-out infinite;
}

.rainbow-text span:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

@keyframes colorPulse {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.1);
  }
}

/* Enhanced Hero Text Colors */
.welcome-text {
  color: var(--saffron) !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
  transition: all 0.3s ease;
  animation: welcomeGlow 2s ease-in-out infinite alternate;
}

.welcome-text:hover {
  transform: scale(1.05);
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
}

@keyframes welcomeGlow {
  0% {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  100% {
    text-shadow: 2px 2px 8px rgba(255, 153, 51, 0.4);
  }
}

.indian-text {
  background: var(--gradient-saffron);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  font-weight: 700;
  transition: all 0.3s ease;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.indian-text:hover {
  transform: scale(1.05);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.super-vision-text {
  background: var(--gradient-blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  font-weight: 700;
  transition: all 0.3s ease;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite reverse;
}

.super-vision-text:hover {
  transform: scale(1.05);
}

/* Enhanced Description Text Colors */
.power-text {
  color: var(--deep-saffron) !important;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.users-text {
  background: var(--gradient-blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.riders-text {
  background: var(--gradient-green);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.sellers-text {
  background: var(--gradient-saffron);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Enhanced Rainbow Colors for 'App Projek' */
.rainbow-text > span:nth-child(1) { color: #FF6B35; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* A */
.rainbow-text > span:nth-child(2) { color: #F7931E; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* p */
.rainbow-text > span:nth-child(3) { color: #FFD700; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* p */
.rainbow-text > span:nth-child(4) { color: #FFFFFF; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); } /*   */
.rainbow-text > span:nth-child(5) { color: #32CD32; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* P */
.rainbow-text > span:nth-child(6) { color: #138808; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* r */
.rainbow-text > span:nth-child(7) { color: #4169E1; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* o */
.rainbow-text > span:nth-child(8) { color: #146eb4; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* j */
.rainbow-text > span:nth-child(9) { color: #FF6B35; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* e */
.rainbow-text > span:nth-child(10) { color: #F7931E; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); } /* k */

.hero::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: 0;
  width: 100%;
  height: 100px;
  background-color: var(--white);
  clip-path: polygon(0 0, 100% 50%, 100% 100%, 0% 100%);
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.hero p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 2rem;
}

.download-btns {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin: 3rem auto 2rem;
  padding: 2.5rem 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.25) 100%);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  position: relative;
  overflow: hidden;
}

.download-btns::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 153, 51, 0.1) 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(19, 136, 8, 0.1) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    rgba(20, 110, 180, 0.1) 100%);
  z-index: -1;
  animation: gradientShift 6s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--gradient-blue);
  color: var(--white);
  border: none;
  box-shadow: 0 4px 15px rgba(20, 110, 180, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0d5a9f 0%, #000070 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(20, 110, 180, 0.4);
}

.btn-secondary {
  background: var(--gradient-green);
  color: var(--white);
  border: none;
  box-shadow: 0 4px 15px rgba(19, 136, 8, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #0c6b06 0%, #0A4A25 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(19, 136, 8, 0.4);
}

.btn-tertiary {
  background: var(--gradient-saffron);
  color: var(--white);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 153, 51, 0.3);
}

.btn-tertiary:hover {
  background: linear-gradient(135deg, #e88a2a 0%, #CC5500 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 153, 51, 0.4);
}

.btn-outline {
  background-color: transparent;
  color: var(--peacock-blue);
  border: 2px solid var(--peacock-blue);
}

.btn-outline:hover {
  background-color: var(--peacock-blue);
  color: var(--white);
  transform: translateY(-2px);
}

.btn .material-icons {
  margin-right: 8px;
}

/* Enhanced Download Buttons */
.download-btns .btn {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 15px;
  min-width: 180px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.download-btns .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
  z-index: -1;
}

.download-btns .btn:hover::before {
  left: 100%;
}

.download-btns .btn .material-icons {
  font-size: 1.4rem;
  margin-right: 10px;
}

.download-btns .btn:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: rgba(255, 255, 255, 0.6);
}

/* App Preview Section */
.app-preview {
  padding: 5rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.section-title {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--dark-gray);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--saffron) 0%, var(--white) 50%, var(--green) 100%);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 3rem;
}

.phone-mockup {
  position: relative;
  width: 250px;
}

.phone-frame {
  width: 100%;
  border-radius: 30px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.phone-screen {
  position: absolute;
  top: 5%;
  left: 5%;
  width: 90%;
  height: 90%;
  border-radius: 25px;
  overflow: hidden;
}

/* Feature Cards */
.features {
  padding: 5rem 2rem;
  background-color: var(--white);
}

.features-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
  border-radius: 12px;
  box-shadow: 0 8px 25px var(--shadow-light);
  padding: 2rem;
  width: 300px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 153, 51, 0.1);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
  border-color: rgba(255, 153, 51, 0.3);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(20, 110, 180, 0.1);
}

.feature-icon.user {
  background-color: rgba(20, 110, 180, 0.1);
}

.feature-icon.rider {
  background-color: rgba(19, 136, 8, 0.1);
}

.feature-icon.seller {
  background-color: rgba(255, 153, 51, 0.1);
}

.feature-icon img {
  width: 50px;
  height: 50px;
}

.feature-card h3 {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

.feature-card p {
  margin-bottom: 1.5rem;
  color: #666;
}

/* Admin Login Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  position: relative;
  overflow: hidden;
}

.modal-header {
  background: var(--gradient-blue);
  color: var(--white);
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(20, 110, 180, 0.2);
}

.modal-body {
  padding: 2rem;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 1.5rem;
  color: var(--white);
  cursor: pointer;
  background: none;
  border: none;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Poppins', sans-serif;
}

.form-control:focus {
  outline: none;
  border-color: var(--peacock-blue);
  box-shadow: 0 0 0 2px rgba(20, 110, 180, 0.2);
}

.form-select {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Poppins', sans-serif;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

/* Admin Dashboard Preview */
.admin-preview {
  padding: 5rem 2rem;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--cream) 100%);
  text-align: center;
}

.dashboard-preview {
  margin-top: 3rem;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 15px var(--shadow);
  overflow: hidden;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.dashboard-header {
  background: var(--gradient-blue);
  color: var(--white);
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 15px rgba(20, 110, 180, 0.2);
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 600;
}

.dashboard-user {
  display: flex;
  align-items: center;
}

.dashboard-user img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.dashboard-body {
  display: flex;
  padding: 2rem;
}

.dashboard-sidebar {
  width: 250px;
  padding-right: 2rem;
}

.sidebar-menu {
  list-style: none;
}

.sidebar-menu li {
  margin-bottom: 1rem;
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--dark-gray);
  padding: 0.8rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.sidebar-menu a:hover {
  background-color: rgba(20, 110, 180, 0.1);
}

.sidebar-menu a.active {
  background-color: var(--peacock-blue);
  color: var(--white);
}

.sidebar-menu .material-icons {
  margin-right: 10px;
}

.dashboard-main {
  flex: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow);
  padding: 1.5rem;
  text-align: center;
}

.stat-card.users {
  border-top: 4px solid var(--peacock-blue);
}

.stat-card.riders {
  border-top: 4px solid var(--green);
}

.stat-card.sellers {
  border-top: 4px solid var(--saffron);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Footer */
.footer {
  background-color: var(--dark-gray);
  color: var(--white);
  padding: 3rem 2rem;
}

.footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-logo {
  margin-bottom: 1rem;
}

.footer-logo img {
  height: 40px;
}

.footer-section {
  flex: 1;
  min-width: 200px;
}

.footer-section h3 {
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  position: relative;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--gradient-saffron);
  border-radius: 2px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.8rem;
}

.footer-links a {
  text-decoration: none;
  color: #ccc;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--saffron);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: var(--saffron);
  transform: translateY(-3px);
}

.copyright {
  text-align: center;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #999;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero h1 {
    font-size: 2.2rem;
  }
  
  .dashboard-body {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    padding-right: 0;
    margin-bottom: 2rem;
  }
  
  .sidebar-menu {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .sidebar-menu li {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }
  
  .nav-links {
    display: none;
    position: absolute;
    top: 70px;
    left: 0;
    right: 0;
    background-color: var(--white);
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 5px 10px var(--shadow);
  }
  
  .nav-links.show {
    display: flex;
  }
  
  .nav-links li {
    margin: 0.5rem 0;
  }
  
  .menu-toggle {
    display: block;
  }
  
  .hero {
    padding: 6rem 1rem 3rem;
  }
  
  .hero h1 {
    font-size: 1.8rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .download-btns {
    flex-direction: column;
    align-items: center;
  }
  
  .features-container,
  .preview-container {
    gap: 1.5rem;
  }
  
  .feature-card {
    width: 100%;
    max-width: 350px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.5rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
  
  .feature-icon img {
    width: 35px;
    height: 35px;
  }
  
  .modal-content {
    width: 95%;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

/* Indian-inspired decorative elements */
.indian-pattern {
  position: absolute;
  width: 100%;
  height: 50px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,0 L40,0 L40,40 L0,40 Z" fill="none" stroke="rgba(255,153,51,0.4)" stroke-width="1"/><path d="M20,0 L20,40 M0,20 L40,20" stroke="rgba(255,153,51,0.4)" stroke-width="1"/><circle cx="20" cy="20" r="5" fill="rgba(255,153,51,0.4)"/><circle cx="20" cy="20" r="2" fill="rgba(19,136,8,0.4)"/></svg>');
  opacity: 0.6;
  animation: patternFloat 8s ease-in-out infinite;
}

@keyframes patternFloat {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(10px);
  }
}

.top-pattern {
  top: 0;
}

.bottom-pattern {
  bottom: 0;
}

/* Animation */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 4s ease-in-out infinite;
}