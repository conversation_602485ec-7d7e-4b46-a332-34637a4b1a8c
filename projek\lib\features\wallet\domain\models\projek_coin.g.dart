// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'projek_coin.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProjekCoinTransactionAdapter extends TypeAdapter<ProjekCoinTransaction> {
  @override
  final int typeId = 12;

  @override
  ProjekCoinTransaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProjekCoinTransaction(
      id: fields[0] as String,
      type: fields[1] as TransactionType,
      amount: fields[2] as double,
      description: fields[3] as String,
      timestamp: fields[4] as DateTime,
      status: fields[5] as TransactionStatus,
      orderId: fields[6] as String?,
      referenceId: fields[7] as String?,
      metadata: (fields[8] as Map?)?.cast<String, dynamic>(),
      category: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProjekCoinTransaction obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.orderId)
      ..writeByte(7)
      ..write(obj.referenceId)
      ..writeByte(8)
      ..write(obj.metadata)
      ..writeByte(9)
      ..write(obj.category);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjekCoinTransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProjekCoinWalletAdapter extends TypeAdapter<ProjekCoinWallet> {
  @override
  final int typeId = 13;

  @override
  ProjekCoinWallet read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProjekCoinWallet(
      balance: fields[0] as double,
      transactions: (fields[1] as List).cast<ProjekCoinTransaction>(),
      lastUpdated: fields[2] as DateTime,
      totalEarned: fields[3] as double,
      totalSpent: fields[4] as double,
      transactionCount: fields[5] as int,
    );
  }

  @override
  void write(BinaryWriter writer, ProjekCoinWallet obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.balance)
      ..writeByte(1)
      ..write(obj.transactions)
      ..writeByte(2)
      ..write(obj.lastUpdated)
      ..writeByte(3)
      ..write(obj.totalEarned)
      ..writeByte(4)
      ..write(obj.totalSpent)
      ..writeByte(5)
      ..write(obj.transactionCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjekCoinWalletAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WalletStatisticsAdapter extends TypeAdapter<WalletStatistics> {
  @override
  final int typeId = 14;

  @override
  WalletStatistics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WalletStatistics(
      monthlyEarnings: fields[0] as double,
      monthlySpending: fields[1] as double,
      monthlyTransactions: fields[2] as int,
      pendingAmount: fields[3] as double,
      calculatedAt: fields[4] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, WalletStatistics obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.monthlyEarnings)
      ..writeByte(1)
      ..write(obj.monthlySpending)
      ..writeByte(2)
      ..write(obj.monthlyTransactions)
      ..writeByte(3)
      ..write(obj.pendingAmount)
      ..writeByte(4)
      ..write(obj.calculatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WalletStatisticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProjekCoinTransactionTypeAdapter
    extends TypeAdapter<ProjekCoinTransactionType> {
  @override
  final int typeId = 15;

  @override
  ProjekCoinTransactionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProjekCoinTransactionType.earn;
      case 1:
        return ProjekCoinTransactionType.spend;
      case 2:
        return ProjekCoinTransactionType.transfer;
      default:
        return ProjekCoinTransactionType.earn;
    }
  }

  @override
  void write(BinaryWriter writer, ProjekCoinTransactionType obj) {
    switch (obj) {
      case ProjekCoinTransactionType.earn:
        writer.writeByte(0);
        break;
      case ProjekCoinTransactionType.spend:
        writer.writeByte(1);
        break;
      case ProjekCoinTransactionType.transfer:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProjekCoinTransactionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionTypeAdapter extends TypeAdapter<TransactionType> {
  @override
  final int typeId = 10;

  @override
  TransactionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionType.purchase;
      case 1:
        return TransactionType.refund;
      case 2:
        return TransactionType.reward;
      case 3:
        return TransactionType.transfer;
      case 4:
        return TransactionType.topup;
      case 5:
        return TransactionType.withdrawal;
      case 6:
        return TransactionType.cashback;
      case 7:
        return TransactionType.referral;
      default:
        return TransactionType.purchase;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionType obj) {
    switch (obj) {
      case TransactionType.purchase:
        writer.writeByte(0);
        break;
      case TransactionType.refund:
        writer.writeByte(1);
        break;
      case TransactionType.reward:
        writer.writeByte(2);
        break;
      case TransactionType.transfer:
        writer.writeByte(3);
        break;
      case TransactionType.topup:
        writer.writeByte(4);
        break;
      case TransactionType.withdrawal:
        writer.writeByte(5);
        break;
      case TransactionType.cashback:
        writer.writeByte(6);
        break;
      case TransactionType.referral:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionStatusAdapter extends TypeAdapter<TransactionStatus> {
  @override
  final int typeId = 11;

  @override
  TransactionStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionStatus.pending;
      case 1:
        return TransactionStatus.completed;
      case 2:
        return TransactionStatus.failed;
      case 3:
        return TransactionStatus.cancelled;
      default:
        return TransactionStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionStatus obj) {
    switch (obj) {
      case TransactionStatus.pending:
        writer.writeByte(0);
        break;
      case TransactionStatus.completed:
        writer.writeByte(1);
        break;
      case TransactionStatus.failed:
        writer.writeByte(2);
        break;
      case TransactionStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjekCoinTransaction _$ProjekCoinTransactionFromJson(
        Map<String, dynamic> json) =>
    ProjekCoinTransaction(
      id: json['id'] as String,
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: $enumDecodeNullable(_$TransactionStatusEnumMap, json['status']) ??
          TransactionStatus.completed,
      orderId: json['orderId'] as String?,
      referenceId: json['referenceId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      category: json['category'] as String?,
    );

Map<String, dynamic> _$ProjekCoinTransactionToJson(
        ProjekCoinTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'description': instance.description,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': _$TransactionStatusEnumMap[instance.status]!,
      'orderId': instance.orderId,
      'referenceId': instance.referenceId,
      'metadata': instance.metadata,
      'category': instance.category,
    };

const _$TransactionTypeEnumMap = {
  TransactionType.purchase: 'purchase',
  TransactionType.refund: 'refund',
  TransactionType.reward: 'reward',
  TransactionType.transfer: 'transfer',
  TransactionType.topup: 'topup',
  TransactionType.withdrawal: 'withdrawal',
  TransactionType.cashback: 'cashback',
  TransactionType.referral: 'referral',
};

const _$TransactionStatusEnumMap = {
  TransactionStatus.pending: 'pending',
  TransactionStatus.completed: 'completed',
  TransactionStatus.failed: 'failed',
  TransactionStatus.cancelled: 'cancelled',
};

ProjekCoinWallet _$ProjekCoinWalletFromJson(Map<String, dynamic> json) =>
    ProjekCoinWallet(
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      transactions: (json['transactions'] as List<dynamic>?)
              ?.map((e) =>
                  ProjekCoinTransaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      totalEarned: (json['totalEarned'] as num?)?.toDouble() ?? 0.0,
      totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
      transactionCount: (json['transactionCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ProjekCoinWalletToJson(ProjekCoinWallet instance) =>
    <String, dynamic>{
      'balance': instance.balance,
      'transactions': instance.transactions,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'totalEarned': instance.totalEarned,
      'totalSpent': instance.totalSpent,
      'transactionCount': instance.transactionCount,
    };

WalletStatistics _$WalletStatisticsFromJson(Map<String, dynamic> json) =>
    WalletStatistics(
      monthlyEarnings: (json['monthlyEarnings'] as num?)?.toDouble() ?? 0.0,
      monthlySpending: (json['monthlySpending'] as num?)?.toDouble() ?? 0.0,
      monthlyTransactions: (json['monthlyTransactions'] as num?)?.toInt() ?? 0,
      pendingAmount: (json['pendingAmount'] as num?)?.toDouble() ?? 0.0,
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$WalletStatisticsToJson(WalletStatistics instance) =>
    <String, dynamic>{
      'monthlyEarnings': instance.monthlyEarnings,
      'monthlySpending': instance.monthlySpending,
      'monthlyTransactions': instance.monthlyTransactions,
      'pendingAmount': instance.pendingAmount,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
    };
