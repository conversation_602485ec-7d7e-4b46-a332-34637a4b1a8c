// Gaming System JavaScript

class GamingSystem {
  constructor() {
    this.wheel = document.getElementById('spinWheel');
    this.gamingBalance = 250; // Starting balance
    this.isSpinning = false;
    this.spinHistory = [];
    
    this.initializeEventListeners();
    this.updateBalance();
  }

  initializeEventListeners() {
    // Spin button event listeners
    const spinButtons = document.querySelectorAll('.spin-btn');
    spinButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const spinType = e.target.getAttribute('data-type');
        this.handleSpin(spinType);
      });
    });

    // Add money button
    const addMoneyBtn = document.querySelector('.add-money-btn');
    if (addMoneyBtn) {
      addMoneyBtn.addEventListener('click', () => this.addMoney());
    }

    // Transfer buttons
    const transferBtns = document.querySelectorAll('.transfer-btn');
    transferBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.target.textContent.toLowerCase();
        this.handleTransfer(action);
      });
    });
  }

  handleSpin(spinType) {
    if (this.isSpinning) {
      this.showMessage('Please wait for the current spin to complete!', 'warning');
      return;
    }

    const entryFee = spinType === 'regular' ? 10 : 50;

    if (this.gamingBalance < entryFee) {
      this.showMessage('Insufficient gaming balance! Please add money or visit wallet.', 'error');
      return;
    }

    // Integrate with wallet system if available
    if (window.wallet && !window.wallet.processGameLoss(entryFee)) {
      this.showMessage('Insufficient gaming balance! Please add money.', 'error');
      return;
    }

    // Deduct entry fee
    this.gamingBalance -= entryFee;
    this.updateBalance();

    // Start spinning
    this.isSpinning = true;
    this.disableSpinButtons();

    // Calculate winning
    const result = this.calculateSpinResult(spinType);

    // Animate wheel spin
    this.animateWheelSpin(result, spinType);
  }

  calculateSpinResult(spinType) {
    const random = Math.random();
    let winAmount = 0;
    let segment = 0;

    // Probability distribution for fair gaming
    if (random < 0.4) {
      // 40% chance of small wins
      const smallWins = [10, 25, 50];
      winAmount = smallWins[Math.floor(Math.random() * smallWins.length)];
      segment = this.getSegmentForAmount(winAmount);
    } else if (random < 0.65) {
      // 25% chance of medium wins
      const mediumWins = [100, 250];
      winAmount = mediumWins[Math.floor(Math.random() * mediumWins.length)];
      segment = this.getSegmentForAmount(winAmount);
    } else if (random < 0.85) {
      // 20% chance of no win
      winAmount = 0;
      segment = 6; // "Try Again" segment
    } else if (random < 0.98) {
      // 13% chance of big win
      winAmount = 500;
      segment = 5;
    } else {
      // 2% chance of jackpot
      winAmount = spinType === 'max' ? 1500 : 500;
      segment = spinType === 'max' ? 7 : 5;
    }

    // Apply multiplier for max spin
    if (spinType === 'max' && winAmount > 0 && winAmount < 1500) {
      winAmount *= 3;
    }

    return { winAmount, segment };
  }

  getSegmentForAmount(amount) {
    const amountToSegment = {
      10: 0,
      25: 1,
      50: 2,
      100: 3,
      250: 4,
      500: 5,
      0: 6,
      1500: 7
    };
    return amountToSegment[amount] || 6;
  }

  animateWheelSpin(result, spinType) {
    const { winAmount, segment } = result;
    
    // Calculate rotation angle
    const segmentAngle = 45; // 360/8 segments
    const targetAngle = (segment * segmentAngle) + (Math.random() * segmentAngle);
    const spins = 5 + Math.random() * 3; // 5-8 full rotations
    const totalRotation = (spins * 360) + targetAngle;

    // Apply rotation
    this.wheel.style.transform = `rotate(${totalRotation}deg)`;

    // Handle spin completion after animation
    setTimeout(() => {
      this.handleSpinComplete(winAmount, spinType);
    }, 3000);
  }

  handleSpinComplete(winAmount, spinType) {
    this.isSpinning = false;
    this.enableSpinButtons();

    if (winAmount > 0) {
      // Add winnings to balance
      this.gamingBalance += winAmount;
      this.updateBalance();

      // Integrate with wallet system if available
      if (window.wallet) {
        window.wallet.processGameWin(winAmount);
      }

      // Show winning message
      this.showWinningAnimation(winAmount);
      this.showMessage(`🎉 Congratulations! You won ₹${winAmount}!`, 'success');
    } else {
      this.showMessage('Better luck next time! Try again.', 'info');
    }

    // Record in history
    this.addToHistory(spinType, winAmount);
    this.updateGameHistory();
  }

  showWinningAnimation(amount) {
    // Create confetti effect
    this.createConfetti();
    
    // Highlight winning amount
    const winDisplay = document.createElement('div');
    winDisplay.className = 'win-display';
    winDisplay.innerHTML = `<h2>🎉 YOU WON! 🎉</h2><p>₹${amount}</p>`;
    winDisplay.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #ff9933, #138808);
      color: white;
      padding: 2rem;
      border-radius: 20px;
      text-align: center;
      z-index: 10000;
      animation: bounceIn 0.5s ease-out;
    `;
    
    document.body.appendChild(winDisplay);
    
    setTimeout(() => {
      winDisplay.remove();
    }, 3000);
  }

  createConfetti() {
    for (let i = 0; i < 50; i++) {
      const confetti = document.createElement('div');
      confetti.style.cssText = `
        position: fixed;
        width: 10px;
        height: 10px;
        background: ${this.getRandomColor()};
        top: -10px;
        left: ${Math.random() * 100}%;
        animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
        z-index: 9999;
      `;
      document.body.appendChild(confetti);
      
      setTimeout(() => confetti.remove(), 5000);
    }
  }

  getRandomColor() {
    const colors = ['#ff9933', '#138808', '#146eb4', '#e74c3c', '#f39c12', '#27ae60'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  addToHistory(spinType, winAmount) {
    const historyItem = {
      timestamp: new Date(),
      type: spinType,
      amount: winAmount,
      entryFee: spinType === 'regular' ? 10 : 50
    };
    
    this.spinHistory.unshift(historyItem);
    
    // Keep only last 10 games
    if (this.spinHistory.length > 10) {
      this.spinHistory = this.spinHistory.slice(0, 10);
    }
  }

  updateGameHistory() {
    const gameList = document.querySelector('.game-list');
    if (!gameList) return;

    gameList.innerHTML = '';
    
    this.spinHistory.slice(0, 3).forEach(game => {
      const gameItem = document.createElement('div');
      gameItem.className = `game-item ${game.amount > 0 ? 'win' : 'loss'}`;
      
      const timeAgo = this.getTimeAgo(game.timestamp);
      const result = game.amount > 0 ? `+₹${game.amount}` : `-₹${game.entryFee}`;
      
      gameItem.innerHTML = `
        <span class="game-time">${timeAgo}</span>
        <span class="game-type">${game.type === 'regular' ? 'Regular Spin' : 'Max Spin'}</span>
        <span class="game-result">${result}</span>
      `;
      
      gameList.appendChild(gameItem);
    });
  }

  getTimeAgo(timestamp) {
    const now = new Date();
    const diff = Math.floor((now - timestamp) / 1000);
    
    if (diff < 60) return `${diff} secs ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)} mins ago`;
    return `${Math.floor(diff / 3600)} hours ago`;
  }

  updateBalance() {
    const balanceElement = document.getElementById('gamingBalance');
    if (balanceElement) {
      balanceElement.textContent = this.gamingBalance;
    }
  }

  disableSpinButtons() {
    const spinButtons = document.querySelectorAll('.spin-btn');
    spinButtons.forEach(btn => {
      btn.disabled = true;
      btn.style.opacity = '0.6';
      btn.textContent = 'Spinning...';
    });
  }

  enableSpinButtons() {
    const spinButtons = document.querySelectorAll('.spin-btn');
    spinButtons.forEach(btn => {
      btn.disabled = false;
      btn.style.opacity = '1';
      
      const spinType = btn.getAttribute('data-type');
      btn.textContent = spinType === 'regular' ? 'Spin Now' : 'Max Spin';
    });
  }

  addMoney() {
    const amount = prompt('Enter amount to add to gaming balance:');
    if (amount && !isNaN(amount) && amount > 0) {
      this.gamingBalance += parseInt(amount);
      this.updateBalance();
      this.showMessage(`₹${amount} added to gaming balance!`, 'success');
    }
  }

  handleTransfer(action) {
    if (action.includes('gaming')) {
      this.addMoney();
    } else if (action.includes('withdraw')) {
      this.withdrawWinnings();
    } else if (action.includes('add')) {
      this.addMoney();
    }
  }

  withdrawWinnings() {
    if (this.gamingBalance <= 0) {
      this.showMessage('No balance to withdraw!', 'error');
      return;
    }
    
    const amount = prompt(`Enter amount to withdraw (Available: ₹${this.gamingBalance}):`);
    if (amount && !isNaN(amount) && amount > 0 && amount <= this.gamingBalance) {
      this.gamingBalance -= parseInt(amount);
      this.updateBalance();
      this.showMessage(`₹${amount} withdrawn successfully!`, 'success');
    }
  }

  showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 1rem 2rem;
      border-radius: 10px;
      color: white;
      font-weight: 600;
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
    `;
    
    // Set background color based on type
    const colors = {
      success: '#28a745',
      error: '#dc3545',
      warning: '#ffc107',
      info: '#17a2b8'
    };
    messageDiv.style.background = colors[type] || colors.info;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
      messageDiv.style.animation = 'slideOut 0.3s ease-out forwards';
      setTimeout(() => messageDiv.remove(), 300);
    }, 3000);
  }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes bounceIn {
    0% { transform: translate(-50%, -50%) scale(0.3); opacity: 0; }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
  }
  
  @keyframes confettiFall {
    to { transform: translateY(100vh) rotate(360deg); }
  }
  
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(style);

// Initialize gaming system when page loads
document.addEventListener('DOMContentLoaded', () => {
  new GamingSystem();
});
