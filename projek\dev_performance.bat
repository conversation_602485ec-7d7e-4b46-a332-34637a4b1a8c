@echo off
title Projek User App - Performance Mode
color 0D

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              PROJEK USER APP - PERFORMANCE MODE             ║
echo ║            Development with Performance Monitoring          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Setting up performance environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
set FLUTTER_WEB_USE_SKIA=true
set FLUTTER_WEB_AUTO_DETECT=true
echo ✅ Environment configured

echo.
echo 📱 Checking device connection...
flutter devices
echo.

echo 📦 Getting dependencies...
flutter pub get
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   PERFORMANCE MONITORING                    ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  🎯 Running in PROFILE mode for performance analysis       ║
echo ║  📊 Performance metrics will be available                  ║
echo ║  🔍 Use Flutter DevTools for detailed analysis             ║
echo ║                                                              ║
echo ║  DEVELOPMENT COMMANDS:                                       ║
echo ║    r  │ Hot Reload    │ Apply changes (limited in profile) ║
echo ║    R  │ Hot Restart   │ Restart app completely             ║
echo ║    h  │ Help          │ Show all commands                  ║
echo ║    q  │ Quit          │ Stop performance monitoring        ║
echo ║                                                              ║
echo ║  📊 PERFORMANCE FEATURES:                                   ║
echo ║    • Frame rendering analysis                               ║
echo ║    • Memory usage monitoring                                ║
echo ║    • CPU performance tracking                               ║
echo ║    • Network request monitoring                             ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting Projek User App in Performance Mode...
echo 📱 Device: V2130 (1397182984001HG)
echo 📊 Profile mode enabled for performance analysis
echo.

flutter run --target lib/main_user.dart -d 1397182984001HG --profile --verbose

echo.
echo 🏁 Performance monitoring session ended.
echo Press any key to exit...
pause >nul
