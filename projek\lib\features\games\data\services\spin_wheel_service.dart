import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../../wallet/domain/models/projek_coin.dart';
import '../../../wallet/data/repositories/wallet_repository.dart';
import '../../domain/models/game_transaction.dart';

class SpinWheelService {
  final WalletRepository _walletRepository;
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  late Box<GameTransaction> _gameTransactionBox;
  bool _isInitialized = false;

  SpinWheelService({
    required WalletRepository walletRepository,
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  }) : _walletRepository = walletRepository,
       _firestore = firestore ?? FirebaseFirestore.instance,
       _auth = auth ?? FirebaseAuth.instance;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _gameTransactionBox = await Hive.openBox<GameTransaction>(
        'game_transactions',
      );
      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing SpinWheelService: $e');
      rethrow;
    }
  }

  /// Main method to play the spin wheel game
  Future<GameTransactionResult> playSpin({required SpinType spinType}) async {
    await initialize();

    try {
      // Step 1: Validate user authentication
      final user = _auth.currentUser;
      if (user == null) {
        return GameTransactionResult.failure('User not authenticated');
      }

      // Step 2: Get current wallet balance
      final wallet = await _walletRepository.getWallet();
      final currentBalance = wallet.balance;

      // Step 3: Validate sufficient balance
      final entryAmount = SpinWheelConfig.getEntryAmount(spinType);
      if (!SpinWheelConfig.canAffordSpin(currentBalance, spinType)) {
        return GameTransactionResult.failure(
          'Insufficient balance. Need ₹${entryAmount.toStringAsFixed(0)}, have ₹${currentBalance.toStringAsFixed(0)}',
        );
      }

      // Step 4: Determine winning segment
      final winningSegmentIndex = SpinWheelConfig.determineWinningSegment();
      final winningSegment = SpinWheelConfig.getSegmentByIndex(
        winningSegmentIndex,
      );

      // Step 5: Calculate win amount with multiplier
      final multiplier = SpinWheelConfig.getMultiplier(spinType);
      final baseWinAmount = winningSegment.amount.toDouble();
      final finalWinAmount = baseWinAmount * multiplier;

      // Step 6: Create game transaction record
      final gameTransaction = GameTransaction.create(
        userId: user.uid,
        gameType: GameType.spinWheel,
        spinType: spinType,
        entryAmount: entryAmount,
        winAmount: finalWinAmount,
        result: _generateResultMessage(finalWinAmount, entryAmount, spinType),
        segmentIndex: winningSegmentIndex,
        balanceBeforeGame: currentBalance,
        metadata: {
          'multiplier': multiplier,
          'baseWinAmount': baseWinAmount,
          'segmentColor': winningSegment.color,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Step 7: Process the transaction
      final result = await _processGameTransaction(gameTransaction);

      return result;
    } catch (e) {
      debugPrint('Error in playSpin: $e');
      return GameTransactionResult.failure('Game error: ${e.toString()}');
    }
  }

  /// Process the game transaction and update wallet
  Future<GameTransactionResult> _processGameTransaction(
    GameTransaction transaction,
  ) async {
    try {
      // Step 1: Mark transaction as processing
      final processingTransaction = transaction.copyWith(
        status: GameTransactionStatus.processing,
      );
      await _saveGameTransaction(processingTransaction);

      // Step 2: Calculate net amount (can be negative for losses)
      // final netAmount = transaction.winAmount - transaction.entryAmount;

      // Step 3: Create wallet transaction for entry fee (always negative)
      final entryTransaction = ProjekCoinTransaction.create(
        amount: -transaction.entryAmount,
        type: ProjekCoinTransactionType.spend,
        description:
            'Spin Wheel Entry Fee - ${transaction.spinTypeDisplayName}',
        category: 'gaming',
        metadata: {
          'gameTransactionId': transaction.id,
          'gameType': 'spin_wheel',
          'spinType': transaction.spinType?.name,
        },
      );

      // Step 4: Create wallet transaction for winnings (if any)
      ProjekCoinTransaction? winningsTransaction;
      if (transaction.winAmount > 0) {
        winningsTransaction = ProjekCoinTransaction.create(
          amount: transaction.winAmount,
          type: ProjekCoinTransactionType.earn,
          description: 'Spin Wheel Winnings - ${transaction.result}',
          category: 'gaming',
          metadata: {
            'gameTransactionId': transaction.id,
            'gameType': 'spin_wheel',
            'spinType': transaction.spinType?.name,
            'segmentIndex': transaction.segmentIndex,
          },
        );
      }

      // Step 5: Update wallet with both transactions
      await _walletRepository.processGameTransactions(
        entryTransaction: entryTransaction,
        winningsTransaction: winningsTransaction,
      );

      // Step 6: Save to Firebase (optional, for analytics)
      await _saveToFirestore(transaction);

      // Step 7: Mark transaction as completed
      final completedTransaction = processingTransaction.copyWith(
        status: GameTransactionStatus.completed,
        completedAt: DateTime.now(),
      );
      await _saveGameTransaction(completedTransaction);

      return GameTransactionResult.success(
        transaction: completedTransaction,
        message: transaction.result,
      );
    } catch (e) {
      // Mark transaction as failed
      final failedTransaction = transaction.copyWith(
        status: GameTransactionStatus.failed,
        errorMessage: e.toString(),
        completedAt: DateTime.now(),
      );
      await _saveGameTransaction(failedTransaction);

      debugPrint('Error processing game transaction: $e');
      return GameTransactionResult.failure(
        'Transaction failed: ${e.toString()}',
      );
    }
  }

  /// Save game transaction to local storage
  Future<void> _saveGameTransaction(GameTransaction transaction) async {
    try {
      await _gameTransactionBox.put(transaction.id, transaction);
    } catch (e) {
      debugPrint('Error saving game transaction: $e');
      rethrow;
    }
  }

  /// Save game transaction to Firestore for analytics
  Future<void> _saveToFirestore(GameTransaction transaction) async {
    try {
      await _firestore
          .collection('game_transactions')
          .doc(transaction.id)
          .set(transaction.toJson());
    } catch (e) {
      // Don't fail the transaction if Firestore save fails
      debugPrint('Warning: Failed to save to Firestore: $e');
    }
  }

  /// Generate result message based on win/loss
  String _generateResultMessage(
    double winAmount,
    double entryAmount,
    SpinType spinType,
  ) {
    final netAmount = winAmount - entryAmount;

    if (netAmount > 0) {
      return 'Won ₹${winAmount.toStringAsFixed(0)}! Profit: +₹${netAmount.toStringAsFixed(0)}';
    } else if (netAmount == 0) {
      return 'Break Even! Got your entry fee back';
    } else {
      return 'Better luck next time! Try again';
    }
  }

  /// Get user's game history
  Future<List<GameTransaction>> getGameHistory({int limit = 50}) async {
    await initialize();

    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final allTransactions = _gameTransactionBox.values.toList();
      final userTransactions = allTransactions
          .where((transaction) => transaction.userId == user.uid)
          .toList();

      // Sort by creation date (newest first)
      userTransactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return userTransactions.take(limit).toList();
    } catch (e) {
      debugPrint('Error getting game history: $e');
      return [];
    }
  }

  /// Get game statistics
  Future<GameStatistics> getGameStatistics() async {
    await initialize();

    try {
      final user = _auth.currentUser;
      if (user == null) {
        return GameStatistics.empty();
      }

      final userTransactions = await getGameHistory(limit: 1000);
      final completedTransactions = userTransactions
          .where((t) => t.status == GameTransactionStatus.completed)
          .toList();

      if (completedTransactions.isEmpty) {
        return GameStatistics.empty();
      }

      final totalGames = completedTransactions.length;
      final totalSpent = completedTransactions
          .map((t) => t.entryAmount)
          .reduce((a, b) => a + b);
      final totalWon = completedTransactions
          .map((t) => t.winAmount)
          .reduce((a, b) => a + b);
      final netProfit = totalWon - totalSpent;

      final wins = completedTransactions.where((t) => t.isWin).length;
      final losses = completedTransactions.where((t) => t.isLoss).length;
      final winRate = wins / totalGames;

      final biggestWin = completedTransactions
          .map((t) => t.netAmount)
          .reduce((a, b) => a > b ? a : b);

      return GameStatistics(
        totalGames: totalGames,
        totalWins: wins,
        totalLosses: losses,
        winRate: winRate,
        totalSpent: totalSpent,
        totalWon: totalWon,
        netProfit: netProfit,
        biggestWin: biggestWin,
        lastPlayedAt: completedTransactions.first.createdAt,
      );
    } catch (e) {
      debugPrint('Error getting game statistics: $e');
      return GameStatistics.empty();
    }
  }

  /// Check if user can afford to play
  Future<bool> canAffordSpin(SpinType spinType) async {
    try {
      final wallet = await _walletRepository.getWallet();
      return SpinWheelConfig.canAffordSpin(wallet.balance, spinType);
    } catch (e) {
      debugPrint('Error checking affordability: $e');
      return false;
    }
  }

  /// Get current wallet balance
  Future<double> getCurrentBalance() async {
    try {
      final wallet = await _walletRepository.getWallet();
      return wallet.balance;
    } catch (e) {
      debugPrint('Error getting current balance: $e');
      return 0.0;
    }
  }

  /// Clear all game data (for testing/reset)
  Future<void> clearGameData() async {
    await initialize();
    try {
      await _gameTransactionBox.clear();
    } catch (e) {
      debugPrint('Error clearing game data: $e');
    }
  }
}

/// Result wrapper for game transactions
class GameTransactionResult {
  final bool isSuccess;
  final GameTransaction? transaction;
  final String message;
  final String? errorCode;

  const GameTransactionResult._({
    required this.isSuccess,
    this.transaction,
    required this.message,
    this.errorCode,
  });

  factory GameTransactionResult.success({
    required GameTransaction transaction,
    required String message,
  }) {
    return GameTransactionResult._(
      isSuccess: true,
      transaction: transaction,
      message: message,
    );
  }

  factory GameTransactionResult.failure(String message, [String? errorCode]) {
    return GameTransactionResult._(
      isSuccess: false,
      message: message,
      errorCode: errorCode,
    );
  }
}

/// Game statistics model
class GameStatistics {
  final int totalGames;
  final int totalWins;
  final int totalLosses;
  final double winRate;
  final double totalSpent;
  final double totalWon;
  final double netProfit;
  final double biggestWin;
  final DateTime? lastPlayedAt;

  const GameStatistics({
    required this.totalGames,
    required this.totalWins,
    required this.totalLosses,
    required this.winRate,
    required this.totalSpent,
    required this.totalWon,
    required this.netProfit,
    required this.biggestWin,
    this.lastPlayedAt,
  });

  factory GameStatistics.empty() {
    return const GameStatistics(
      totalGames: 0,
      totalWins: 0,
      totalLosses: 0,
      winRate: 0.0,
      totalSpent: 0.0,
      totalWon: 0.0,
      netProfit: 0.0,
      biggestWin: 0.0,
    );
  }

  String get formattedWinRate => '${(winRate * 100).toStringAsFixed(1)}%';
  String get formattedNetProfit {
    final prefix = netProfit >= 0 ? '+' : '';
    return '$prefix₹${netProfit.toStringAsFixed(0)}';
  }

  String get formattedBiggestWin => '₹${biggestWin.toStringAsFixed(0)}';
}
