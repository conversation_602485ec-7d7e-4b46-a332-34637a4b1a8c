<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- Background -->
  <circle cx="100" cy="100" r="95" fill="#ffffff" stroke="#138808" stroke-width="3"/>
  <circle cx="100" cy="100" r="85" fill="url(#bgGradient)"/>
  
  <!-- Main Illustration -->
  <g transform="translate(30, 50)">
    <!-- Scooter Body -->
    <path d="M20 70 Q20 60 30 60 L110 60 Q120 60 120 70 L120 80 Q120 90 110 90 L30 90 Q20 90 20 80 Z" fill="#ff4757"/>
    
    <!-- Scooter Details -->
    <rect x="90" y="55" width="4" height="15" fill="#2c2c54"/>
    <rect x="85" y="55" width="14" height="4" rx="2" fill="#2c2c54"/>
    
    <!-- Front Wheel -->
    <circle cx="110" cy="100" r="15" fill="#2c2c54"/>
    <circle cx="110" cy="100" r="10" fill="#ffffff"/>
    <circle cx="110" cy="100" r="6" fill="#2c2c54"/>
    
    <!-- Rear Wheel -->
    <circle cx="40" cy="100" r="15" fill="#2c2c54"/>
    <circle cx="40" cy="100" r="10" fill="#ffffff"/>
    <circle cx="40" cy="100" r="6" fill="#2c2c54"/>
    
    <!-- Delivery Box -->
    <rect x="10" y="65" width="20" height="15" rx="3" fill="#ffffff" stroke="#ddd" stroke-width="1"/>
    <rect x="12" y="68" width="16" height="2" fill="#ff4757"/>
    <rect x="12" y="72" width="16" height="2" fill="#ff4757"/>
    <rect x="12" y="76" width="16" height="2" fill="#ff4757"/>
    
    <!-- Rider -->
    <g transform="translate(60, 20)">
      <!-- Body -->
      <ellipse cx="10" cy="35" rx="12" ry="20" fill="#ffa502"/>
      
      <!-- Legs -->
      <rect x="2" y="50" width="6" height="18" rx="3" fill="#3742fa"/>
      <rect x="12" y="50" width="6" height="18" rx="3" fill="#3742fa"/>
      
      <!-- Arms -->
      <ellipse cx="25" cy="30" rx="4" ry="12" fill="#ffa502" transform="rotate(20 25 30)"/>
      <ellipse cx="-5" cy="30" rx="4" ry="12" fill="#ffa502" transform="rotate(-20 -5 30)"/>
      
      <!-- Head -->
      <circle cx="10" cy="15" r="9" fill="#ffeaa7"/>
      
      <!-- Helmet -->
      <path d="M1 12 Q1 6 10 6 Q19 6 19 12 Q19 18 10 18 Q1 18 1 12 Z" fill="#ff4757"/>
      
      <!-- Face Mask -->
      <rect x="6" y="16" width="8" height="4" rx="2" fill="#74b9ff"/>
      
      <!-- Eyes -->
      <circle cx="7" cy="13" r="1" fill="#2c2c54"/>
      <circle cx="13" cy="13" r="1" fill="#2c2c54"/>
      
      <!-- Hands -->
      <circle cx="30" cy="40" r="3" fill="#ffeaa7"/>
      <circle cx="-8" cy="40" r="3" fill="#ffeaa7"/>
    </g>
    
    <!-- Speed Lines -->
    <g opacity="0.6">
      <path d="M130 65 L145 65" stroke="#ff4757" stroke-width="3" stroke-linecap="round"/>
      <path d="M132 75 L142 75" stroke="#ff4757" stroke-width="2" stroke-linecap="round"/>
      <path d="M134 85 L140 85" stroke="#ff4757" stroke-width="2" stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- Brand Elements -->
  <text x="100" y="170" text-anchor="middle" font-family="Poppins, sans-serif" font-size="16" font-weight="700" fill="#138808">
    RIDER
  </text>
  
  <!-- Decorative Stars -->
  <g fill="#ffa502" opacity="0.7">
    <polygon points="50,30 52,36 58,36 53,40 55,46 50,42 45,46 47,40 42,36 48,36" transform="scale(0.6)"/>
    <polygon points="150,40 152,46 158,46 153,50 155,56 150,52 145,56 147,50 142,46 148,46" transform="scale(0.5)"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f5e8;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
