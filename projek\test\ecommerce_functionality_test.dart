import 'package:flutter_test/flutter_test.dart';

import 'package:projek/features/marketplace/domain/models/product.dart';
import 'package:projek/features/cart/domain/models/cart_item.dart';
import 'package:projek/features/wishlist/domain/models/wishlist_item.dart';

void main() {
  group('E-commerce Model Tests', () {
    late Product testProduct;

    setUp(() {
      // Create test product
      testProduct = Product(
        id: 'test_product_1',
        name: 'Test Product',
        description: 'A test product for unit testing',
        price: 299.0,
        originalPrice: 399.0,
        images: ['test_image.jpg'],
        category: 'Test Category',
        subcategory: 'Test Subcategory',
        brand: 'Test Brand',
        rating: 4.5,
        reviewCount: 100,
        stockQuantity: 50,
        vendorId: 'test_vendor',
        vendorName: 'Test Vendor',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: true,
      );
    });

    group('Product Model Tests', () {
      test('Product should have correct formatted price', () {
        expect(testProduct.formattedPrice, '₹299.00');
        expect(testProduct.formattedOriginalPrice, '₹399.00');
      });

      test('Product should calculate discount correctly', () {
        expect(testProduct.hasDiscount, true);
        expect(testProduct.calculatedDiscountPercentage, closeTo(25.06, 0.1));
      });

      test('Product should have primary image', () {
        expect(testProduct.primaryImage, 'test_image.jpg');
      });

      test('Product should be available', () {
        expect(testProduct.isAvailable, true);
      });
    });

    group('Cart Item Model Tests', () {
      test('Should create cart item from product', () {
        final cartItem = CartItem(
          id: 'cart_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          quantity: 2,
          addedAt: DateTime.now(),
        );

        expect(cartItem.productId, testProduct.id);
        expect(cartItem.name, testProduct.name);
        expect(cartItem.price, testProduct.price);
        expect(cartItem.quantity, 2);
      });

      test('Should calculate cart item total correctly', () {
        final cartItem = CartItem(
          id: 'cart_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          quantity: 3,
          addedAt: DateTime.now(),
        );

        expect(cartItem.quantity * cartItem.price, 897.0); // 299 * 3
      });

      test('Should format cart item price correctly', () {
        final cartItem = CartItem(
          id: 'cart_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          quantity: 1,
          addedAt: DateTime.now(),
        );

        expect(cartItem.formattedPrice, '₹299.00');
      });
    });

    group('Wishlist Item Model Tests', () {
      test('Should create wishlist item from product', () {
        final wishlistItem = WishlistItem(
          id: 'wishlist_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          addedAt: DateTime.now(),
        );

        expect(wishlistItem.productId, testProduct.id);
        expect(wishlistItem.name, testProduct.name);
        expect(wishlistItem.price, testProduct.price);
      });

      test('Should format wishlist item price correctly', () {
        final wishlistItem = WishlistItem(
          id: 'wishlist_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          addedAt: DateTime.now(),
        );

        expect(wishlistItem.formattedPrice, '₹299.00');
        expect(wishlistItem.formattedOriginalPrice, '₹399.00');
      });

      test('Should detect discount in wishlist item', () {
        final wishlistItem = WishlistItem(
          id: 'wishlist_item_1',
          productId: testProduct.id,
          name: testProduct.name,
          price: testProduct.price,
          originalPrice: testProduct.originalPrice,
          imageUrl: testProduct.primaryImage,
          category: testProduct.category,
          brand: testProduct.brand,
          vendorId: testProduct.vendorId,
          vendorName: testProduct.vendorName,
          addedAt: DateTime.now(),
        );

        expect(wishlistItem.hasDiscount, true);
      });
    });
  });
}
