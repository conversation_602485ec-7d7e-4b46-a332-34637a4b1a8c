import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../main.dart';

class EnhancedHelpCenterPage extends StatefulWidget {
  const EnhancedHelpCenterPage({super.key});

  @override
  State<EnhancedHelpCenterPage> createState() => _EnhancedHelpCenterPageState();
}

class _EnhancedHelpCenterPageState extends State<EnhancedHelpCenterPage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';

  final List<HelpCategory> _categories = [
    HelpCategory(
      title: 'Getting Started',
      icon: Icons.rocket_launch,
      color: Colors.blue,
      topics: [
        'How to create your UID',
        'Setting up your profile',
        'First time wallet setup',
        'Understanding the super app',
      ],
    ),
    HelpCategory(
      title: 'Wallet & Payments',
      icon: Icons.account_balance_wallet,
      color: Colors.green,
      topics: [
        'Adding money to wallet',
        'UPI payments',
        'QR code scanning',
        'Transaction history',
        'Refunds and disputes',
      ],
    ),
    HelpCategory(
      title: 'Games & Rewards',
      icon: Icons.casino,
      color: Colors.purple,
      topics: [
        'Spin-to-Earn game',
        'Daily rewards',
        'Referral bonuses',
        'Withdrawal limits',
      ],
    ),
    HelpCategory(
      title: 'Marketplace',
      icon: Icons.shopping_cart,
      color: Colors.orange,
      topics: [
        'How to buy products',
        'Order tracking',
        'Returns and exchanges',
        'Seller verification',
      ],
    ),
    HelpCategory(
      title: 'Account & Security',
      icon: Icons.security,
      color: Colors.red,
      topics: [
        'Password reset',
        'Two-factor authentication',
        'Account verification',
        'Privacy settings',
      ],
    ),
    HelpCategory(
      title: 'Technical Support',
      icon: Icons.build,
      color: Colors.teal,
      topics: [
        'App not working',
        'Login issues',
        'Payment failures',
        'Bug reports',
      ],
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: const Text('Help Center'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _openLiveChat(),
            icon: const Icon(Icons.chat_bubble_outline),
            tooltip: 'Live Chat',
          ),
          IconButton(
            onPressed: () => _callSupport(),
            icon: const Icon(Icons.phone),
            tooltip: 'Call Support',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Header
            _buildWelcomeHeader(theme, colorScheme),
            const SizedBox(height: 24),

            // Search Bar
            _buildSearchBar(theme, colorScheme),
            const SizedBox(height: 24),

            // Quick Actions
            _buildQuickActions(theme, colorScheme),
            const SizedBox(height: 24),

            // Help Categories
            _buildHelpCategories(theme, colorScheme),
            const SizedBox(height: 24),

            // Contact Support
            _buildContactSupport(theme, colorScheme),
            const SizedBox(height: 24),

            // App Info
            _buildAppInfo(theme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [colorScheme.primary, colorScheme.secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_center,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'My India First Help',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Super App Support Center',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Get instant help for wallet, games, marketplace, and more. Our support team is here 24/7 to assist you.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search for help topics...',
          prefixIcon: Icon(Icons.search, color: colorScheme.onSurfaceVariant),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildQuickActions(ThemeData theme, ColorScheme colorScheme) {
    final quickActions = [
      QuickAction(
        title: 'Live Chat',
        subtitle: 'Chat with support',
        icon: Icons.chat_bubble,
        color: Colors.green,
        onTap: () => _openLiveChat(),
      ),
      QuickAction(
        title: 'Call Support',
        subtitle: '+91 1800-123-4567',
        icon: Icons.phone,
        color: Colors.blue,
        onTap: () => _callSupport(),
      ),
      QuickAction(
        title: 'Email Us',
        subtitle: '<EMAIL>',
        icon: Icons.email,
        color: Colors.orange,
        onTap: () => _emailSupport(),
      ),
      QuickAction(
        title: 'Report Bug',
        subtitle: 'Report an issue',
        icon: Icons.bug_report,
        color: Colors.red,
        onTap: () => _reportBug(),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
          ),
          itemCount: quickActions.length,
          itemBuilder: (context, index) {
            final action = quickActions[index];
            return _buildQuickActionCard(action, theme, colorScheme);
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    QuickAction action,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: action.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                action.color.withValues(alpha: 0.1),
                action.color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: action.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(action.icon, color: action.color, size: 24),
              ),
              const SizedBox(height: 12),
              Text(
                action.title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                action.subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHelpCategories(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Help Categories',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return _buildCategoryCard(category, theme, colorScheme);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    HelpCategory category,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: category.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(category.icon, color: category.color, size: 24),
        ),
        title: Text(
          category.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          '${category.topics.length} topics',
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        children: category.topics.map((topic) {
          return ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 4,
            ),
            leading: Icon(
              Icons.help_outline,
              size: 20,
              color: colorScheme.onSurfaceVariant,
            ),
            title: Text(topic, style: theme.textTheme.bodyMedium),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
            onTap: () => _openTopicDetail(topic, category),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildContactSupport(ThemeData theme, ColorScheme colorScheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.support_agent, color: colorScheme.primary, size: 28),
                const SizedBox(width: 12),
                Text(
                  'Still Need Help?',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Our support team is available 24/7 to help you with any questions or issues.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openLiveChat(),
                    icon: const Icon(Icons.chat),
                    label: const Text('Live Chat'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _callSupport(),
                    icon: const Icon(Icons.phone),
                    label: const Text('Call Us'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfo(ThemeData theme, ColorScheme colorScheme) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'Version',
              '1.0.0',
              Icons.info_outline,
              theme,
              colorScheme,
            ),
            _buildInfoRow(
              'Session ID',
              '9c7d58e8-9e5b-4573-abed-a3d7773c9ec3',
              Icons.fingerprint,
              theme,
              colorScheme,
              copyable: true,
            ),
            _buildInfoRow(
              'Support Hours',
              '24/7 Available',
              Icons.schedule,
              theme,
              colorScheme,
            ),
            _buildInfoRow(
              'Response Time',
              'Usually within 5 minutes',
              Icons.timer,
              theme,
              colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme, {
    bool copyable = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: colorScheme.onSurfaceVariant),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (copyable)
            IconButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: value));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Copied to clipboard')),
                );
              },
              icon: const Icon(Icons.copy, size: 18),
              tooltip: 'Copy',
            ),
        ],
      ),
    );
  }

  // Action Methods
  void _openLiveChat() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomerSupportChatPage()),
    );
  }

  void _callSupport() {
    _launchUrl('tel:+911800123456');
  }

  void _emailSupport() {
    _launchUrl('mailto:<EMAIL>?subject=Support Request');
  }

  void _reportBug() {
    _showBugReportDialog();
  }

  void _openTopicDetail(String topic, HelpCategory category) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildTopicDetailSheet(topic, category),
    );
  }

  Widget _buildTopicDetailSheet(String topic, HelpCategory category) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: category.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(category.icon, color: category.color, size: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      topic,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: _getTopicContent(topic),
                ),
              ),

              // Actions
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      label: const Text('Close'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _openLiveChat();
                      },
                      icon: const Icon(Icons.chat),
                      label: const Text('Get Help'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _getTopicContent(String topic) {
    // Return detailed content based on topic
    switch (topic.toLowerCase()) {
      case 'how to create your uid':
        return const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Creating Your Unique ID (UID)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('1. Go to Profile → Create UID'),
            Text('2. Enter your full name'),
            Text('3. Select your date of birth'),
            Text('4. Add phone number (optional)'),
            Text('5. Generate and select your preferred UID'),
            Text('6. Save your UID safely'),
            SizedBox(height: 16),
            Text(
              'Your UID format: Name + DOB + 567 + Hash\nExample: RAHKUM150895567A1B2',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        );
      case 'spin-to-earn game':
        return const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How to Play Spin-to-Earn',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('1. Ensure you have sufficient wallet balance'),
            Text('2. Choose Regular Spin (₹10) or Max Spin (₹50)'),
            Text('3. Tap the spin button'),
            Text('4. Watch the wheel spin and stop'),
            Text('5. Collect your winnings instantly'),
            SizedBox(height: 16),
            Text(
              'Max Spin offers 3x multiplier on all prizes!',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        );
      default:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              topic,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text(
              'We\'re working on detailed content for this topic. In the meantime, please contact our support team for immediate assistance.',
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _openLiveChat(),
              icon: const Icon(Icons.chat),
              label: const Text('Contact Support'),
            ),
          ],
        );
    }
  }

  void _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Could not launch $url')));
      }
    }
  }

  void _showBugReportDialog() {
    final TextEditingController bugController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report a Bug'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please describe the bug you encountered:'),
            const SizedBox(height: 16),
            TextField(
              controller: bugController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'Describe the issue...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (bugController.text.trim().isNotEmpty) {
                _submitBugReport(bugController.text.trim());
                Navigator.pop(context);
              }
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _submitBugReport(String bugDescription) async {
    try {
      await _firestore.collection('bug_reports').add({
        'description': bugDescription,
        'userId': _auth.currentUser?.uid ?? 'anonymous',
        'userEmail': _auth.currentUser?.email ?? '<EMAIL>',
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'open',
        'sessionId': '9c7d58e8-9e5b-4573-abed-a3d7773c9ec3',
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bug report submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting bug report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Data Models
class HelpCategory {
  final String title;
  final IconData icon;
  final Color color;
  final List<String> topics;

  HelpCategory({
    required this.title,
    required this.icon,
    required this.color,
    required this.topics,
  });
}

class QuickAction {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  QuickAction({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
