// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'real_time_tracking_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RealTimeLocationAdapter extends TypeAdapter<RealTimeLocation> {
  @override
  final int typeId = 122;

  @override
  RealTimeLocation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RealTimeLocation(
      latitude: fields[0] as double,
      longitude: fields[1] as double,
      accuracy: fields[2] as double,
      altitude: fields[3] as double?,
      speed: fields[4] as double?,
      heading: fields[5] as double?,
      timestamp: fields[6] as DateTime,
      address: fields[7] as String?,
      metadata: (fields[8] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, RealTimeLocation obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude)
      ..writeByte(2)
      ..write(obj.accuracy)
      ..writeByte(3)
      ..write(obj.altitude)
      ..writeByte(4)
      ..write(obj.speed)
      ..writeByte(5)
      ..write(obj.heading)
      ..writeByte(6)
      ..write(obj.timestamp)
      ..writeByte(7)
      ..write(obj.address)
      ..writeByte(8)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RealTimeLocationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RoutePointAdapter extends TypeAdapter<RoutePoint> {
  @override
  final int typeId = 123;

  @override
  RoutePoint read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RoutePoint(
      latitude: fields[0] as double,
      longitude: fields[1] as double,
      instruction: fields[2] as String?,
      distance: fields[3] as double?,
      duration: fields[4] as int?,
      streetName: fields[5] as String?,
      maneuver: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, RoutePoint obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude)
      ..writeByte(2)
      ..write(obj.instruction)
      ..writeByte(3)
      ..write(obj.distance)
      ..writeByte(4)
      ..write(obj.duration)
      ..writeByte(5)
      ..write(obj.streetName)
      ..writeByte(6)
      ..write(obj.maneuver);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RoutePointAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OptimizedRouteAdapter extends TypeAdapter<OptimizedRoute> {
  @override
  final int typeId = 124;

  @override
  OptimizedRoute read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OptimizedRoute(
      id: fields[0] as String,
      points: (fields[1] as List).cast<RoutePoint>(),
      totalDistance: fields[2] as double,
      totalDuration: fields[3] as int,
      optimizationType: fields[4] as RouteOptimizationType,
      calculatedAt: fields[5] as DateTime,
      expiresAt: fields[6] as DateTime?,
      trafficData: (fields[7] as Map).cast<String, dynamic>(),
      warnings: (fields[8] as List).cast<String>(),
      metadata: (fields[9] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, OptimizedRoute obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.points)
      ..writeByte(2)
      ..write(obj.totalDistance)
      ..writeByte(3)
      ..write(obj.totalDuration)
      ..writeByte(4)
      ..write(obj.optimizationType)
      ..writeByte(5)
      ..write(obj.calculatedAt)
      ..writeByte(6)
      ..write(obj.expiresAt)
      ..writeByte(7)
      ..write(obj.trafficData)
      ..writeByte(8)
      ..write(obj.warnings)
      ..writeByte(9)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OptimizedRouteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ETACalculationAdapter extends TypeAdapter<ETACalculation> {
  @override
  final int typeId = 125;

  @override
  ETACalculation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ETACalculation(
      estimatedArrival: fields[0] as DateTime,
      confidenceLevel: fields[1] as int,
      remainingDistance: fields[2] as double,
      remainingTime: fields[3] as int,
      averageSpeed: fields[4] as double,
      calculatedAt: fields[5] as DateTime,
      factors: (fields[6] as List).cast<String>(),
      trafficImpact: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, ETACalculation obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.estimatedArrival)
      ..writeByte(1)
      ..write(obj.confidenceLevel)
      ..writeByte(2)
      ..write(obj.remainingDistance)
      ..writeByte(3)
      ..write(obj.remainingTime)
      ..writeByte(4)
      ..write(obj.averageSpeed)
      ..writeByte(5)
      ..write(obj.calculatedAt)
      ..writeByte(6)
      ..write(obj.factors)
      ..writeByte(7)
      ..write(obj.trafficImpact);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ETACalculationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RealTimeTrackingAdapter extends TypeAdapter<RealTimeTracking> {
  @override
  final int typeId = 126;

  @override
  RealTimeTracking read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RealTimeTracking(
      orderId: fields[0] as String,
      riderId: fields[1] as String,
      status: fields[2] as TrackingStatus,
      currentLocation: fields[3] as RealTimeLocation?,
      activeRoute: fields[4] as OptimizedRoute?,
      currentETA: fields[5] as ETACalculation?,
      locationHistory: (fields[6] as List).cast<RealTimeLocation>(),
      startedAt: fields[7] as DateTime,
      completedAt: fields[8] as DateTime?,
      totalDistanceTraveled: fields[9] as double,
      totalTimeElapsed: fields[10] as int,
      performanceMetrics: (fields[11] as Map).cast<String, dynamic>(),
      alerts: (fields[12] as List).cast<String>(),
      metadata: (fields[13] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, RealTimeTracking obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.orderId)
      ..writeByte(1)
      ..write(obj.riderId)
      ..writeByte(2)
      ..write(obj.status)
      ..writeByte(3)
      ..write(obj.currentLocation)
      ..writeByte(4)
      ..write(obj.activeRoute)
      ..writeByte(5)
      ..write(obj.currentETA)
      ..writeByte(6)
      ..write(obj.locationHistory)
      ..writeByte(7)
      ..write(obj.startedAt)
      ..writeByte(8)
      ..write(obj.completedAt)
      ..writeByte(9)
      ..write(obj.totalDistanceTraveled)
      ..writeByte(10)
      ..write(obj.totalTimeElapsed)
      ..writeByte(11)
      ..write(obj.performanceMetrics)
      ..writeByte(12)
      ..write(obj.alerts)
      ..writeByte(13)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RealTimeTrackingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TrackingStatusAdapter extends TypeAdapter<TrackingStatus> {
  @override
  final int typeId = 120;

  @override
  TrackingStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TrackingStatus.inactive;
      case 1:
        return TrackingStatus.active;
      case 2:
        return TrackingStatus.paused;
      case 3:
        return TrackingStatus.completed;
      case 4:
        return TrackingStatus.failed;
      default:
        return TrackingStatus.inactive;
    }
  }

  @override
  void write(BinaryWriter writer, TrackingStatus obj) {
    switch (obj) {
      case TrackingStatus.inactive:
        writer.writeByte(0);
        break;
      case TrackingStatus.active:
        writer.writeByte(1);
        break;
      case TrackingStatus.paused:
        writer.writeByte(2);
        break;
      case TrackingStatus.completed:
        writer.writeByte(3);
        break;
      case TrackingStatus.failed:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrackingStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RouteOptimizationTypeAdapter extends TypeAdapter<RouteOptimizationType> {
  @override
  final int typeId = 121;

  @override
  RouteOptimizationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RouteOptimizationType.fastest;
      case 1:
        return RouteOptimizationType.shortest;
      case 2:
        return RouteOptimizationType.balanced;
      case 3:
        return RouteOptimizationType.fuelEfficient;
      default:
        return RouteOptimizationType.fastest;
    }
  }

  @override
  void write(BinaryWriter writer, RouteOptimizationType obj) {
    switch (obj) {
      case RouteOptimizationType.fastest:
        writer.writeByte(0);
        break;
      case RouteOptimizationType.shortest:
        writer.writeByte(1);
        break;
      case RouteOptimizationType.balanced:
        writer.writeByte(2);
        break;
      case RouteOptimizationType.fuelEfficient:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RouteOptimizationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RealTimeLocation _$RealTimeLocationFromJson(Map<String, dynamic> json) =>
    RealTimeLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num).toDouble(),
      altitude: (json['altitude'] as num?)?.toDouble(),
      speed: (json['speed'] as num?)?.toDouble(),
      heading: (json['heading'] as num?)?.toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      address: json['address'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$RealTimeLocationToJson(RealTimeLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'accuracy': instance.accuracy,
      'altitude': instance.altitude,
      'speed': instance.speed,
      'heading': instance.heading,
      'timestamp': instance.timestamp.toIso8601String(),
      'address': instance.address,
      'metadata': instance.metadata,
    };

RoutePoint _$RoutePointFromJson(Map<String, dynamic> json) => RoutePoint(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      instruction: json['instruction'] as String?,
      distance: (json['distance'] as num?)?.toDouble(),
      duration: (json['duration'] as num?)?.toInt(),
      streetName: json['streetName'] as String?,
      maneuver: json['maneuver'] as String?,
    );

Map<String, dynamic> _$RoutePointToJson(RoutePoint instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'instruction': instance.instruction,
      'distance': instance.distance,
      'duration': instance.duration,
      'streetName': instance.streetName,
      'maneuver': instance.maneuver,
    };

OptimizedRoute _$OptimizedRouteFromJson(Map<String, dynamic> json) =>
    OptimizedRoute(
      id: json['id'] as String,
      points: (json['points'] as List<dynamic>)
          .map((e) => RoutePoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalDistance: (json['totalDistance'] as num).toDouble(),
      totalDuration: (json['totalDuration'] as num).toInt(),
      optimizationType: $enumDecodeNullable(
              _$RouteOptimizationTypeEnumMap, json['optimizationType']) ??
          RouteOptimizationType.balanced,
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      trafficData: json['trafficData'] as Map<String, dynamic>? ?? const {},
      warnings: (json['warnings'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$OptimizedRouteToJson(OptimizedRoute instance) =>
    <String, dynamic>{
      'id': instance.id,
      'points': instance.points,
      'totalDistance': instance.totalDistance,
      'totalDuration': instance.totalDuration,
      'optimizationType':
          _$RouteOptimizationTypeEnumMap[instance.optimizationType]!,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'trafficData': instance.trafficData,
      'warnings': instance.warnings,
      'metadata': instance.metadata,
    };

const _$RouteOptimizationTypeEnumMap = {
  RouteOptimizationType.fastest: 'fastest',
  RouteOptimizationType.shortest: 'shortest',
  RouteOptimizationType.balanced: 'balanced',
  RouteOptimizationType.fuelEfficient: 'fuelEfficient',
};

ETACalculation _$ETACalculationFromJson(Map<String, dynamic> json) =>
    ETACalculation(
      estimatedArrival: DateTime.parse(json['estimatedArrival'] as String),
      confidenceLevel: (json['confidenceLevel'] as num).toInt(),
      remainingDistance: (json['remainingDistance'] as num).toDouble(),
      remainingTime: (json['remainingTime'] as num).toInt(),
      averageSpeed: (json['averageSpeed'] as num).toDouble(),
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
      factors: (json['factors'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      trafficImpact: json['trafficImpact'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$ETACalculationToJson(ETACalculation instance) =>
    <String, dynamic>{
      'estimatedArrival': instance.estimatedArrival.toIso8601String(),
      'confidenceLevel': instance.confidenceLevel,
      'remainingDistance': instance.remainingDistance,
      'remainingTime': instance.remainingTime,
      'averageSpeed': instance.averageSpeed,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
      'factors': instance.factors,
      'trafficImpact': instance.trafficImpact,
    };

RealTimeTracking _$RealTimeTrackingFromJson(Map<String, dynamic> json) =>
    RealTimeTracking(
      orderId: json['orderId'] as String,
      riderId: json['riderId'] as String,
      status: $enumDecodeNullable(_$TrackingStatusEnumMap, json['status']) ??
          TrackingStatus.inactive,
      currentLocation: json['currentLocation'] == null
          ? null
          : RealTimeLocation.fromJson(
              json['currentLocation'] as Map<String, dynamic>),
      activeRoute: json['activeRoute'] == null
          ? null
          : OptimizedRoute.fromJson(
              json['activeRoute'] as Map<String, dynamic>),
      currentETA: json['currentETA'] == null
          ? null
          : ETACalculation.fromJson(json['currentETA'] as Map<String, dynamic>),
      locationHistory: (json['locationHistory'] as List<dynamic>?)
              ?.map((e) => RealTimeLocation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      totalDistanceTraveled:
          (json['totalDistanceTraveled'] as num?)?.toDouble() ?? 0.0,
      totalTimeElapsed: (json['totalTimeElapsed'] as num?)?.toInt() ?? 0,
      performanceMetrics:
          json['performanceMetrics'] as Map<String, dynamic>? ?? const {},
      alerts: (json['alerts'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$RealTimeTrackingToJson(RealTimeTracking instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'riderId': instance.riderId,
      'status': _$TrackingStatusEnumMap[instance.status]!,
      'currentLocation': instance.currentLocation,
      'activeRoute': instance.activeRoute,
      'currentETA': instance.currentETA,
      'locationHistory': instance.locationHistory,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'totalDistanceTraveled': instance.totalDistanceTraveled,
      'totalTimeElapsed': instance.totalTimeElapsed,
      'performanceMetrics': instance.performanceMetrics,
      'alerts': instance.alerts,
      'metadata': instance.metadata,
    };

const _$TrackingStatusEnumMap = {
  TrackingStatus.inactive: 'inactive',
  TrackingStatus.active: 'active',
  TrackingStatus.paused: 'paused',
  TrackingStatus.completed: 'completed',
  TrackingStatus.failed: 'failed',
};
