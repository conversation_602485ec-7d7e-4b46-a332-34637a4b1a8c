import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../domain/models/gamification_models.dart';
import '../../data/services/gamification_service.dart';
import '../../data/services/enhanced_spin_wheel_service.dart';
import '../widgets/user_profile_card.dart';
// Create these widget files or use inline widgets instead
// import '../widgets/achievements_section.dart';
// import '../widgets/games_grid.dart';
// import '../widgets/leaderboard_preview.dart';
// import '../widgets/referral_card.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';

final userProfileProvider = FutureProvider<UserProfile>((ref) async {
  final userId = AuthService.currentUserId;
  if (userId == null) throw Exception('User not authenticated');

  return await GamificationService.initializeUserProfile(
    userId: userId,
    username: AuthService.currentUserEmail ?? 'User',
  );
});

final achievementsProvider = FutureProvider<List<Achievement>>((ref) async {
  // This would fetch user's achievements from Firestore
  return [];
});

final leaderboardProvider = FutureProvider<List<LeaderboardEntry>>((ref) async {
  return await GamificationService.getLeaderboard();
});

final spinStatsProvider = FutureProvider<SpinStatistics>((ref) async {
  final userId = AuthService.currentUserId;
  if (userId == null) throw Exception('User not authenticated');

  return await EnhancedSpinWheelService.getSpinStatistics(userId);
});

class GamificationDashboardPage extends ConsumerStatefulWidget {
  const GamificationDashboardPage({super.key});

  @override
  ConsumerState<GamificationDashboardPage> createState() =>
      _GamificationDashboardPageState();
}

class _GamificationDashboardPageState
    extends ConsumerState<GamificationDashboardPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProfileAsync = ref.watch(userProfileProvider);
    final achievementsAsync = ref.watch(achievementsProvider);
    final leaderboardAsync = ref.watch(leaderboardProvider);
    final spinStatsAsync = ref.watch(spinStatsProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: userProfileAsync.when(
        data: (profile) => _buildDashboardContent(
          profile,
          achievementsAsync,
          leaderboardAsync,
          spinStatsAsync,
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error.toString()),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryBlue,
      foregroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        'Rewards & Games',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
      ),
      actions: [
        IconButton(
          onPressed: () => context.push('/gamification/achievements'),
          icon: const Icon(Icons.emoji_events),
        ),
        IconButton(
          onPressed: () => context.push('/gamification/leaderboard'),
          icon: const Icon(Icons.leaderboard),
        ),
      ],
    );
  }

  Widget _buildDashboardContent(
    UserProfile profile,
    AsyncValue<List<Achievement>> achievementsAsync,
    AsyncValue<List<LeaderboardEntry>> leaderboardAsync,
    AsyncValue<SpinStatistics> spinStatsAsync,
  ) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: CustomScrollView(
        slivers: [
          // User Profile Header
          SliverToBoxAdapter(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primaryBlue, AppColors.secondaryOrange],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  UserProfileCard(profile: profile),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // Quick Stats
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              child: _buildQuickStats(profile, spinStatsAsync),
            ),
          ),

          // Tab Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  TabBar(
                    controller: _tabController,
                    labelColor: AppColors.primaryBlue,
                    unselectedLabelColor: AppColors.textSecondary,
                    indicatorColor: AppColors.primaryBlue,
                    tabs: const [
                      Tab(text: 'Games'),
                      Tab(text: 'Achievements'),
                      Tab(text: 'Leaderboard'),
                      Tab(text: 'Referrals'),
                    ],
                  ),
                  SizedBox(
                    height: 400,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Games Tab
                        GamesGrid(
                          onGameTap: _navigateToGame,
                          spinStats: spinStatsAsync.value,
                        ),

                        // Achievements Tab
                        achievementsAsync.when(
                          data: (achievements) => _buildAchievementsSection(
                            achievements: achievements,
                            onAchievementTap: _showAchievementDetails,
                          ),
                          loading: () =>
                              const Center(child: CircularProgressIndicator()),
                          error: (error, stack) => Center(
                            child: Text('Error loading achievements: $error'),
                          ),
                        ),

                        // Leaderboard Tab
                        leaderboardAsync.when(
                          data: (leaderboard) => _buildLeaderboardPreview(
                            entries: leaderboard,
                            currentUserId: profile.userId,
                            onViewFullLeaderboard: () =>
                                context.push('/gamification/leaderboard'),
                          ),
                          loading: () =>
                              const Center(child: CircularProgressIndicator()),
                          error: (error, stack) => Center(
                            child: Text('Error loading leaderboard: $error'),
                          ),
                        ),

                        // Referrals Tab
                        _buildReferralCard(
                          userId: profile.userId,
                          onShareReferral: _shareReferralCode,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom spacing
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildQuickStats(
    UserProfile profile,
    AsyncValue<SpinStatistics> spinStatsAsync,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Level',
            value: '${profile.level}',
            subtitle:
                '${profile.experience}/${profile.experienceToNextLevel} XP',
            icon: Icons.trending_up,
            color: AppColors.success,
            progress:
                profile.experience /
                profile.experienceToNextLevel, // Calculate progress
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'Tier',
            value: _getTierName(profile.loyaltyTier).split(' ').first,
            subtitle: _getTierName(profile.loyaltyTier).split(' ').last,
            icon: Icons.military_tech,
            color: _getTierColor(profile.loyaltyTier),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: spinStatsAsync.when(
            data: (stats) => _buildStatCard(
              title: 'Win Rate',
              value: stats.formattedWinRate,
              subtitle: '${stats.totalWins} wins',
              icon: Icons.casino,
              color: AppColors.warning,
            ),
            loading: () => _buildStatCard(
              title: 'Win Rate',
              value: '...',
              subtitle: 'Loading',
              icon: Icons.casino,
              color: AppColors.warning,
            ),
            error: (_, __) => _buildStatCard(
              title: 'Win Rate',
              value: '0%',
              subtitle: 'Error',
              icon: Icons.casino,
              color: AppColors.error,
            ),
          ),
        ),
      ],
    );
  }

  String _getTierName(LoyaltyTier tier) {
    switch (tier) {
      case LoyaltyTier.bronze:
        return 'Bronze Tier';
      case LoyaltyTier.silver:
        return 'Silver Tier';
      case LoyaltyTier.gold:
        return 'Gold Tier';
      case LoyaltyTier.platinum:
        return 'Platinum Tier';
      case LoyaltyTier.diamond:
        return 'Diamond Tier';
    }
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    double? progress,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          if (progress != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            'Error loading dashboard',
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.refresh(userProfileProvider),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Event handlers
  Future<void> _refreshData() async {
    await Future.wait([
      ref.refresh(userProfileProvider.future),
      ref.refresh(achievementsProvider.future),
      ref.refresh(leaderboardProvider.future),
      ref.refresh(spinStatsProvider.future),
    ]);
  }

  void _navigateToGame(GameType gameType) {
    switch (gameType) {
      case GameType.spinWheel:
        context.push('/games/spin-wheel');
        break;
      case GameType.dailyCheckin:
        context.push('/games/daily-checkin');
        break;
      case GameType.scratchCard:
        context.push('/games/scratch-card');
        break;
      case GameType.quiz:
        context.push('/games/quiz');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${gameType.toString()} coming soon!'),
            backgroundColor: AppColors.info,
          ),
        );
    }
  }

  // Replacement for AchievementsSection widget
  Widget _buildAchievementsSection({
    required List<Achievement> achievements,
    required Function(Achievement) onAchievementTap,
  }) {
    return ListView.builder(
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return ListTile(
          leading: Text(achievement.icon),
          title: Text(achievement.title),
          subtitle: Text(achievement.description),
          trailing: achievement.isUnlocked
              ? const Icon(Icons.check_circle, color: Colors.green)
              : null,
          onTap: () => onAchievementTap(achievement),
        );
      },
    );
  }

  // Replacement for LeaderboardPreview widget
  Widget _buildLeaderboardPreview({
    required List<LeaderboardEntry> entries,
    required String currentUserId,
    required VoidCallback onViewFullLeaderboard,
  }) {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: entries.length > 5 ? 5 : entries.length,
            itemBuilder: (context, index) {
              final entry = entries[index];
              final isCurrentUser = entry.userId == currentUserId;

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: isCurrentUser ? AppColors.primaryBlue : null,
                  child: Text('${index + 1}'),
                ),
                title: Text(
                  entry.username,
                  style: TextStyle(
                    fontWeight: isCurrentUser
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                trailing: Text(
                  '${entry.score} pts',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isCurrentUser ? AppColors.primaryBlue : null,
                  ),
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton(
            onPressed: onViewFullLeaderboard,
            child: const Text('View Full Leaderboard'),
          ),
        ),
      ],
    );
  }

  // Replacement for ReferralCard widget
  Widget _buildReferralCard({
    required String userId,
    required Function(String) onShareReferral,
  }) {
    final referralCode = 'REF-${userId.substring(0, 6).toUpperCase()}';

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Icon(Icons.people, size: 48),
          const SizedBox(height: 16),
          const Text(
            'Invite Friends & Earn Rewards',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Share your referral code with friends. When they sign up, you both get rewards!',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  referralCode,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.5,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => onShareReferral(referralCode),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => onShareReferral(referralCode),
            icon: const Icon(Icons.share),
            label: const Text('Share Referral Code'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showAchievementDetails(Achievement achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Text(achievement.icon, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Expanded(child: Text(achievement.title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(achievement.description),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: achievement.progressPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
            ),
            const SizedBox(height: 8),
            Text(
              '${achievement.currentProgress}/${achievement.targetValue}',
              style: AppTextStyles.bodySmall,
            ),
            if (achievement.isUnlocked && !achievement.isClaimed) ...[
              const SizedBox(height: 16),
              Text(
                'Reward: ${achievement.formattedReward}',
                style: AppTextStyles.titleSmall.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (achievement.isUnlocked && !achievement.isClaimed)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _claimAchievement(achievement);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
              ),
              child: const Text('Claim Reward'),
            ),
        ],
      ),
    );
  }

  void _shareReferralCode(String referralCode) {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Referral code: $referralCode'),
        action: SnackBarAction(
          label: 'Share',
          onPressed: () {
            // Implement actual sharing
          },
        ),
      ),
    );
  }

  void _claimAchievement(Achievement achievement) {
    // Implement achievement claiming
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Claimed ${achievement.formattedReward}!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Color _getTierColor(LoyaltyTier tier) {
    switch (tier) {
      case LoyaltyTier.bronze:
        return const Color(0xFFCD7F32);
      case LoyaltyTier.silver:
        return const Color(0xFFC0C0C0);
      case LoyaltyTier.gold:
        return const Color(0xFFFFD700);
      case LoyaltyTier.platinum:
        return const Color(0xFFE5E4E2);
      case LoyaltyTier.diamond:
        return const Color(0xFFB9F2FF);
    }
  }
}

class GamesGrid extends StatelessWidget {
  final void Function(GameType) onGameTap;
  final dynamic spinStats;

  const GamesGrid({super.key, required this.onGameTap, this.spinStats});

  @override
  Widget build(BuildContext context) {
    // Example grid with placeholder games
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildGameTile(context, GameType.spinWheel, 'Spin Wheel', Icons.casino),
        _buildGameTile(
          context,
          GameType.dailyCheckin,
          'Daily Check-in',
          Icons.calendar_today,
        ),
        _buildGameTile(
          context,
          GameType.scratchCard,
          'Scratch Card',
          Icons.credit_card,
        ),
        _buildGameTile(context, GameType.quiz, 'Quiz', Icons.quiz),
      ],
    );
  }

  Widget _buildGameTile(
    BuildContext context,
    GameType type,
    String title,
    IconData icon,
  ) {
    return GestureDetector(
      onTap: () => onGameTap(type),
      child: Card(
        margin: const EdgeInsets.all(8),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 40),
              const SizedBox(height: 8),
              Text(title),
            ],
          ),
        ),
      ),
    );
  }
}
