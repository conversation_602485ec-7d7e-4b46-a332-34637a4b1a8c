import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:confetti/confetti.dart';

import '../../../wallet/presentation/providers/wallet_provider.dart';
import '../../domain/models/game_transaction.dart';
import '../providers/spin_wheel_provider.dart';

class SpinWheelPage extends ConsumerStatefulWidget {
  const SpinWheelPage({super.key});

  @override
  ConsumerState<SpinWheelPage> createState() => _SpinWheelPageState();
}

class _SpinWheelPageState extends ConsumerState<SpinWheelPage>
    with TickerProviderStateMixin {
  late AnimationController _wheelController;
  late AnimationController _pulseController;
  late AnimationController _winningSegmentController;
  late Animation<double> _wheelAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _winningSegmentAnimation;
  late ConfettiController _confettiController;

  bool _isSpinning = false;
  // int? _winningAmount; // TODO: Use for displaying win amount
  // SpinType? _lastSpinType; // TODO: Use for tracking spin history
  int? _targetSegmentIndex;
  double _currentRotation = 0.0;

  // Wheel segments with prizes
  final List<WheelSegment> _segments = [
    WheelSegment(amount: 10, color: Colors.red, probability: 0.3),
    WheelSegment(amount: 25, color: Colors.orange, probability: 0.25),
    WheelSegment(amount: 50, color: Colors.yellow, probability: 0.2),
    WheelSegment(amount: 100, color: Colors.green, probability: 0.15),
    WheelSegment(amount: 250, color: Colors.blue, probability: 0.08),
    WheelSegment(amount: 500, color: Colors.purple, probability: 0.02),
  ];

  @override
  void initState() {
    super.initState();

    _wheelController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _wheelAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _wheelController, curve: Curves.easeOutCubic),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Initialize winning segment highlight animation
    _winningSegmentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _winningSegmentAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _winningSegmentController,
        curve: Curves.elasticOut,
      ),
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _wheelController.dispose();
    _pulseController.dispose();
    _winningSegmentController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final walletAsync = ref.watch(walletProvider);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.arrow_back_ios, color: colorScheme.onSurface),
        ),
        title: Text(
          'Spin & Earn',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showGameRules(),
            icon: Icon(Icons.help_outline, color: colorScheme.onSurface),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colorScheme.primaryContainer.withValues(alpha: 0.3),
                  colorScheme.surface,
                ],
              ),
            ),
          ),

          // Confetti
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: pi / 2,
              maxBlastForce: 5,
              minBlastForce: 2,
              emissionFrequency: 0.05,
              numberOfParticles: 50,
              gravity: 0.05,
            ),
          ),

          // Main content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Wallet balance
                  _buildWalletBalance(theme, colorScheme, walletAsync),
                  const SizedBox(height: 32),

                  // Spin wheel
                  _buildSpinWheel(theme, colorScheme),
                  const SizedBox(height: 32),

                  // Spin options
                  _buildSpinOptions(theme, colorScheme, walletAsync),
                  const SizedBox(height: 24),

                  // Prize breakdown
                  _buildPrizeBreakdown(theme, colorScheme),
                  const SizedBox(height: 24),

                  // Recent wins
                  _buildRecentWins(theme, colorScheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletBalance(
    ThemeData theme,
    ColorScheme colorScheme,
    AsyncValue walletAsync,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [colorScheme.primary, colorScheme.secondary],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: walletAsync.when(
        data: (wallet) => Column(
          children: [
            Text(
              'Your Balance',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onPrimary.withValues(alpha: 0.8),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '₹${wallet.balance.toStringAsFixed(2)}',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        loading: () => const CircularProgressIndicator(),
        error: (error, stack) => Text(
          'Error loading balance',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildSpinWheel(ThemeData theme, ColorScheme colorScheme) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Wheel
          AnimatedBuilder(
            animation: _wheelAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _calculateWheelRotation(),
                child: SizedBox(
                  width: 280,
                  height: 280,
                  child: AnimatedBuilder(
                    animation: _winningSegmentAnimation,
                    builder: (context, child) {
                      return CustomPaint(
                        painter: WheelPainter(
                          _segments,
                          winningSegmentIndex: _targetSegmentIndex,
                          highlightAnimation: _winningSegmentAnimation.value,
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),

          // Center button
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isSpinning ? 1.0 : _pulseAnimation.value,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.play_arrow,
                    color: colorScheme.onPrimary,
                    size: 40,
                  ),
                ),
              );
            },
          ),

          // Pointer
          Positioned(
            top: 20,
            child: Container(
              width: 0,
              height: 0,
              decoration: const BoxDecoration(
                border: Border(
                  left: BorderSide(width: 15, color: Colors.transparent),
                  right: BorderSide(width: 15, color: Colors.transparent),
                  bottom: BorderSide(width: 30, color: Colors.red),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpinOptions(
    ThemeData theme,
    ColorScheme colorScheme,
    AsyncValue walletAsync,
  ) {
    return walletAsync.when(
      data: (wallet) => Row(
        children: [
          Expanded(
            child: _buildSpinOptionCard(
              theme,
              colorScheme,
              'Regular Spin',
              '₹10',
              'Win up to ₹500',
              SpinType.regular,
              wallet.balance >= 10,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSpinOptionCard(
              theme,
              colorScheme,
              'Max Spin',
              '₹50',
              'Win up to ₹1500\n(3x multiplier)',
              SpinType.max,
              wallet.balance >= 50,
            ),
          ),
        ],
      ),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => const SizedBox(),
    );
  }

  Widget _buildSpinOptionCard(
    ThemeData theme,
    ColorScheme colorScheme,
    String title,
    String cost,
    String description,
    SpinType spinType,
    bool canAfford,
  ) {
    final isMaxSpin = spinType == SpinType.max;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isMaxSpin
            ? BorderSide(color: Colors.amber, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: canAfford && !_isSpinning ? () => _startSpin(spinType) : null,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: isMaxSpin
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      Colors.amber.withValues(alpha: 0.1),
                      Colors.orange.withValues(alpha: 0.1),
                    ],
                  ),
                )
              : null,
          child: Column(
            children: [
              if (isMaxSpin)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'PREMIUM',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (isMaxSpin) const SizedBox(height: 8),

              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                cost,
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: canAfford && !_isSpinning
                      ? () => _startSpin(spinType)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isMaxSpin
                        ? Colors.amber
                        : colorScheme.primary,
                    foregroundColor: isMaxSpin
                        ? Colors.black
                        : colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _isSpinning ? 'Spinning...' : 'SPIN NOW',
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrizeBreakdown(ThemeData theme, ColorScheme colorScheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prize Breakdown',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            ...(_segments.map(
              (segment) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: segment.color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text('₹${segment.amount}'),
                    const Spacer(),
                    Text(
                      '${(segment.probability * 100).toStringAsFixed(0)}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentWins(ThemeData theme, ColorScheme colorScheme) {
    // Sample recent wins data
    final recentWins = [
      {'user': 'Rahul K.', 'amount': 250, 'time': '2 min ago'},
      {'user': 'Priya S.', 'amount': 100, 'time': '5 min ago'},
      {'user': 'Amit P.', 'amount': 500, 'time': '8 min ago'},
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Winners',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            ...recentWins.map(
              (win) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: colorScheme.primaryContainer,
                      child: Text(
                        win['user'].toString().substring(0, 1),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            win['user'].toString(),
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            win['time'].toString(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '₹${win['amount']}',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate the wheel rotation angle for realistic physics-based animation
  double _calculateWheelRotation() {
    if (_targetSegmentIndex == null) {
      // Default spinning animation when no target is set
      return _wheelAnimation.value * 8 * pi;
    }

    // Calculate the target angle for the winning segment
    final segmentAngle = 2 * pi / _segments.length;
    final targetSegmentAngle = _targetSegmentIndex! * segmentAngle;

    // Add multiple full rotations (8+ spins) for realistic effect
    final baseRotations = 8 * 2 * pi;

    // Calculate the final stopping position
    // The pointer is at the top, so we need to adjust for that
    final pointerOffset = pi / 2; // 90 degrees
    final finalAngle =
        baseRotations + (2 * pi - targetSegmentAngle) + pointerOffset;

    // Apply easing animation
    final progress = _wheelAnimation.value;
    return _currentRotation + (finalAngle - _currentRotation) * progress;
  }

  /// Determine the winning segment based on game logic
  int _determineWinningSegmentIndex(double winAmount) {
    // Find the segment that matches the win amount
    for (int i = 0; i < _segments.length; i++) {
      if (_segments[i].amount == winAmount.toInt()) {
        return i;
      }
    }
    // Fallback to first segment if no match found
    return 0;
  }

  Future<void> _startSpin(SpinType spinType) async {
    if (_isSpinning) return;

    setState(() {
      _isSpinning = true;
      // _lastSpinType = spinType; // TODO: Use for tracking spin history
    });

    // Haptic feedback
    HapticFeedback.mediumImpact();

    try {
      // Use the spin wheel service to play the game
      final spinWheelNotifier = ref.read(spinWheelStateProvider.notifier);
      await spinWheelNotifier.playSpin(spinType);

      final gameState = ref.read(spinWheelStateProvider);

      if (gameState.lastTransaction != null) {
        final transaction = gameState.lastTransaction!;

        // Determine the winning segment and set up animation target
        final winningSegmentIndex = _determineWinningSegmentIndex(
          transaction.winAmount,
        );

        setState(() {
          _targetSegmentIndex = winningSegmentIndex;
          _currentRotation = 0.0; // Reset rotation for new spin
        });

        // Start realistic wheel animation with physics-based deceleration
        await _wheelController.forward();

        // Start winning segment highlight animation
        _winningSegmentController.forward();

        // Show result
        setState(() {
          // _winningAmount = transaction.winAmount.toInt(); // TODO: Use for displaying win amount
          _isSpinning = false;
        });

        // Trigger confetti for big wins
        if (transaction.winAmount >= 100) {
          _confettiController.play();
        }

        // Show result dialog with actual transaction data after a brief delay
        await Future.delayed(const Duration(milliseconds: 500));
        _showResultDialog(transaction);

        // Reset animations after showing result
        await Future.delayed(const Duration(seconds: 2));
        _wheelController.reset();
        _winningSegmentController.reset();

        setState(() {
          _targetSegmentIndex = null;
          _currentRotation = 0.0;
        });

        // Refresh wallet balance
        ref.invalidate(currentBalanceProvider);
        ref.invalidate(walletProvider);
      } else if (gameState.errorMessage != null) {
        // Handle error
        setState(() {
          _isSpinning = false;
        });

        _showErrorDialog(gameState.errorMessage!);
      }
    } catch (e) {
      setState(() {
        _isSpinning = false;
      });

      _showErrorDialog('Failed to play spin: $e');
    }
  }

  void _showResultDialog(GameTransaction transaction) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          transaction.isWin
              ? '🎉 Congratulations!'
              : '😔 Better luck next time!',
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'You won ${transaction.formattedWinAmount}',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: transaction.isWin ? Colors.green : Colors.orange,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Entry Fee: ${transaction.formattedEntryAmount}',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Net Result: ${transaction.formattedNetAmount}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: transaction.isWin ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                transaction.result,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
          if (transaction.isWin)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to wallet to see updated balance
                context.push('/wallet');
              },
              child: const Text('View Wallet'),
            ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Game Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showGameRules() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Game Rules'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('• Regular Spin: ₹10 entry, win up to ₹500'),
              Text('• Max Spin: ₹50 entry, win up to ₹1500 (3x multiplier)'),
              Text('• Winnings are added to your wallet instantly'),
              Text('• Fair play guaranteed with transparent odds'),
              Text('• Minimum age: 18 years'),
              Text('• Play responsibly'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

class WheelSegment {
  final int amount;
  final Color color;
  final double probability;

  WheelSegment({
    required this.amount,
    required this.color,
    required this.probability,
  });
}

class WheelPainter extends CustomPainter {
  final List<WheelSegment> segments;
  final int? winningSegmentIndex;
  final double highlightAnimation;

  WheelPainter(
    this.segments, {
    this.winningSegmentIndex,
    this.highlightAnimation = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final segmentAngle = 2 * pi / segments.length;

    for (int i = 0; i < segments.length; i++) {
      final startAngle = i * segmentAngle;
      final isWinningSegment = winningSegmentIndex == i;

      // Calculate highlight effect
      double segmentRadius = radius;
      Color segmentColor = segments[i].color;

      if (isWinningSegment && highlightAnimation > 0) {
        // Pulsing effect for winning segment
        final pulseScale = 1.0 + (highlightAnimation * 0.05);
        segmentRadius = radius * pulseScale;

        // Brighten the winning segment
        final hsl = HSLColor.fromColor(segments[i].color);
        segmentColor = hsl
            .withLightness(
              (hsl.lightness + highlightAnimation * 0.3).clamp(0.0, 1.0),
            )
            .toColor();
      }

      // Draw segment
      final paint = Paint()
        ..color = segmentColor
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: segmentRadius),
        startAngle,
        segmentAngle,
        true,
        paint,
      );

      // Draw winning segment glow effect
      if (isWinningSegment && highlightAnimation > 0) {
        final glowPaint = Paint()
          ..color = Colors.white.withValues(alpha: highlightAnimation * 0.6)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 6 * highlightAnimation;
        canvas.drawArc(
          Rect.fromCircle(center: center, radius: segmentRadius),
          startAngle,
          segmentAngle,
          true,
          glowPaint,
        );
      }

      // Draw text
      final textAngle = startAngle + segmentAngle / 2;
      final textRadius = segmentRadius * 0.7;
      final textX = center.dx + textRadius * cos(textAngle);
      final textY = center.dy + textRadius * sin(textAngle);

      // Enhanced text for winning segment
      final textStyle = TextStyle(
        color: Colors.white,
        fontSize: isWinningSegment && highlightAnimation > 0
            ? 16 + (4 * highlightAnimation)
            : 16,
        fontWeight: FontWeight.bold,
        shadows: isWinningSegment && highlightAnimation > 0
            ? [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.7),
                  blurRadius: 3 * highlightAnimation,
                  offset: const Offset(1, 1),
                ),
              ]
            : null,
      );

      final textPainter = TextPainter(
        text: TextSpan(text: '₹${segments[i].amount}', style: textStyle),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(textX - textPainter.width / 2, textY - textPainter.height / 2),
      );
    }

    // Draw border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    canvas.drawCircle(center, radius, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is WheelPainter) {
      return oldDelegate.winningSegmentIndex != winningSegmentIndex ||
          oldDelegate.highlightAnimation != highlightAnimation;
    }
    return true;
  }
}
