// Booking Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
  
  // Navigation tabs functionality
  const navTabs = document.querySelectorAll('.nav-tab-small');
  const sections = {
    'products': document.getElementById('products'),
    'services': document.getElementById('services'),
    'popular': document.getElementById('popular')
  };

  navTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetSection = this.getAttribute('data-section');
      
      // Remove active class from all tabs
      navTabs.forEach(t => t.classList.remove('active'));
      
      // Add active class to clicked tab
      this.classList.add('active');
      
      // Scroll to target section
      if (sections[targetSection]) {
        sections[targetSection].scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Search functionality
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.querySelector('.search-btn');

  function performSearch() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    
    if (searchTerm === '') {
      showAllItems();
      return;
    }

    // Hide all cards initially
    const allCards = document.querySelectorAll('.product-card-small, .service-card-small, .popular-item-small');
    allCards.forEach(card => {
      card.style.display = 'none';
    });

    // Show matching cards
    let hasResults = false;

    // Search in products
    const productCards = document.querySelectorAll('.product-card-small');
    productCards.forEach(card => {
      const title = card.querySelector('h4').textContent.toLowerCase();
      const description = card.querySelector('p').textContent.toLowerCase();

      if (title.includes(searchTerm) || description.includes(searchTerm)) {
        card.style.display = 'block';
        hasResults = true;
      }
    });

    // Search in services
    const serviceCards = document.querySelectorAll('.service-card-small');
    serviceCards.forEach(card => {
      const title = card.querySelector('h4').textContent.toLowerCase();
      const description = card.querySelector('p').textContent.toLowerCase();

      if (title.includes(searchTerm) || description.includes(searchTerm)) {
        card.style.display = 'block';
        hasResults = true;
      }
    });

    // Search in popular items
    const popularCards = document.querySelectorAll('.popular-item-small');
    popularCards.forEach(card => {
      const title = card.querySelector('h5').textContent.toLowerCase();
      const description = card.querySelector('p').textContent.toLowerCase();

      if (title.includes(searchTerm) || description.includes(searchTerm)) {
        card.style.display = 'block';
        hasResults = true;
      }
    });

    // Show no results message if needed
    if (!hasResults) {
      showNoResults();
    }
  }

  function showAllItems() {
    const allCards = document.querySelectorAll('.product-card-small, .service-card-small, .popular-item-small');
    allCards.forEach(card => {
      card.style.display = 'block';
    });
    hideNoResults();
  }

  function showNoResults() {
    // Remove existing no results message
    hideNoResults();
    
    // Create and show no results message
    const noResultsDiv = document.createElement('div');
    noResultsDiv.className = 'no-results';
    noResultsDiv.innerHTML = `
      <div style="text-align: center; padding: 3rem; color: var(--medium-gray);">
        <span class="material-icons" style="font-size: 4rem; margin-bottom: 1rem; display: block;">search_off</span>
        <h3>No results found</h3>
        <p>Try searching with different keywords</p>
      </div>
    `;
    
    document.querySelector('.services-section .container').appendChild(noResultsDiv);
  }

  function hideNoResults() {
    const noResults = document.querySelector('.no-results');
    if (noResults) {
      noResults.remove();
    }
  }

  // Search event listeners
  searchBtn.addEventListener('click', performSearch);
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      performSearch();
    }
  });

  // Real-time search as user types
  searchInput.addEventListener('input', function() {
    if (this.value.trim() === '') {
      showAllItems();
    }
  });

  // Product card click handlers
  const productCards = document.querySelectorAll('.product-card-small');
  productCards.forEach(card => {
    const buyBtn = card.querySelector('.btn-small');
    buyBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      const product = card.getAttribute('data-product');
      handleProductPurchase(product);
    });
  });

  // Service card click handlers
  const serviceCards = document.querySelectorAll('.service-card-small');
  serviceCards.forEach(card => {
    const bookBtn = card.querySelector('.btn-small');
    bookBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      const service = card.getAttribute('data-service');
      handleServiceBooking(service);
    });
  });

  // Popular item click handlers
  const popularItems = document.querySelectorAll('.popular-item-small');
  popularItems.forEach(item => {
    const bookBtn = item.querySelector('.btn-small');
    bookBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      const service = item.getAttribute('data-service') || 'popular-item';
      handleServiceBooking(service);
    });
  });

  function handleProductPurchase(product) {
    // Add purchase animation
    const clickedCard = document.querySelector(`[data-product="${product}"]`);
    if (!clickedCard) return;

    const buyBtn = clickedCard.querySelector('.btn-small');

    // Change button text temporarily
    const originalText = buyBtn.innerHTML;
    buyBtn.innerHTML = 'Adding...';
    buyBtn.style.background = 'var(--green)';

    setTimeout(() => {
      buyBtn.innerHTML = originalText;
      buyBtn.style.background = '';
      showPurchaseModal(product);
    }, 1000);
  }

  function handleServiceBooking(service) {
    // Add booking animation
    const clickedCard = document.querySelector(`[data-service="${service}"]`);
    if (!clickedCard) return;

    const bookBtn = clickedCard.querySelector('.btn-small');

    // Change button text temporarily
    const originalText = bookBtn.innerHTML;
    bookBtn.innerHTML = 'Booking...';
    bookBtn.style.background = 'var(--green)';

    setTimeout(() => {
      bookBtn.innerHTML = originalText;
      bookBtn.style.background = '';
      showBookingModal(service);
    }, 1000);
  }

  function showPurchaseModal(product) {
    const productNames = {
      'food': 'Food & Beverages',
      'clothes': 'Fashion & Clothing',
      'electronics': 'Electronics & Gadgets',
      'phones': 'Mobile Phones',
      'groceries': 'Groceries & Essentials',
      'books': 'Books & Stationery',
      'beauty-products': 'Beauty & Cosmetics',
      'home': 'Home & Kitchen',
      'sports': 'Sports & Fitness'
    };

    alert(`Adding ${productNames[product] || product} to cart!\n\nThis will open the shopping cart with:\n- Product details\n- Quantity selection\n- Size/Color options\n- Delivery address\n- Payment options`);
  }

  function showBookingModal(service) {
    const serviceNames = {
      'beauty': 'Beauty & Wellness',
      'cleaning': 'Home Cleaning',
      'plumber': 'Plumbing Services',
      'medicine': 'Healthcare & Medicine',
      'laundry': 'Laundry & Dry Cleaning',
      'repairs': 'Home Repairs',
      'teaching': 'Education & Tutoring',
      'food': 'Food Delivery',
      'groceries': 'Groceries & Essentials',
      'electronics': 'Electronics & Gadgets'
    };

    alert(`Booking ${serviceNames[service] || service}!\n\nThis will open the booking form with:\n- Service selection\n- Date & time picker\n- Address details\n- Payment options`);
  }

  // Smooth scroll for navigation links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Add scroll spy for navigation tabs
  window.addEventListener('scroll', function() {
    const scrollPosition = window.scrollY + 200;
    
    // Check which section is currently in view
    Object.keys(sections).forEach(sectionKey => {
      const section = sections[sectionKey];
      if (section) {
        const sectionTop = section.offsetTop;
        const sectionBottom = sectionTop + section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
          // Update active tab
          navTabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.getAttribute('data-section') === sectionKey) {
              tab.classList.add('active');
            }
          });
        }
      }
    });
  });

  // Add loading animation for images
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    img.addEventListener('load', function() {
      this.style.opacity = '1';
    });
    
    // Set initial opacity
    img.style.opacity = '0';
    img.style.transition = 'opacity 0.3s ease';
  });

  // Add hover effects for cards
  const allCards = document.querySelectorAll('.product-card-small, .service-card-small, .popular-item-small');
  allCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-8px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = '';
    });
  });

  // Initialize fade-in animations
  const fadeElements = document.querySelectorAll('.fade-in');
  const fadeObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        fadeObserver.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });

  fadeElements.forEach(element => {
    fadeObserver.observe(element);
  });

  console.log('Booking page initialized successfully!');
});
