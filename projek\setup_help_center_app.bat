@echo off
echo ========================================
echo Projek Help Center Customer Support App
echo ========================================
echo.

echo Welcome to Projek - Help Center Customer Support Application!
echo This app provides comprehensive business services with integrated customer support.
echo.

echo Features:
echo - Business Services Dashboard (Food, Grocery, Rides, Marketplace, Pharmacy)
echo - Comprehensive Help Center with Live Chat Support
echo - Professional Customer Support Interface
echo - Multi-channel Support (Chat, Email, Phone, FAQs)
echo - Topic-based Support Categories
echo.

echo ========================================
echo Setup Instructions
echo ========================================
echo.

echo 1. Install Dependencies
echo.
flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

echo 2. Fix Google Sign-In (Important!)
echo.
echo Running SHA-1 certificate fix...
call fix_google_signin.bat
echo.
echo ⚠️  IMPORTANT: Follow the instructions above to update Firebase Console
echo    with the SHA-1 certificate for Google Sign-In to work properly.
echo.

echo 3. Check Flutter Setup
echo.
flutter doctor
echo.

echo ========================================
echo Application Structure
echo ========================================
echo.
echo Main Navigation:
echo ├── Services Dashboard (Primary)
echo │   ├── Food Delivery
echo │   ├── Grocery Shopping  
echo │   ├── Ride Booking
echo │   ├── Marketplace
echo │   ├── Pharmacy
echo │   └── Quick Support Access
echo ├── Help Center (Support Hub)
echo │   ├── Live Chat Support ← Customer Support Chat
echo │   ├── Browse FAQs
echo │   ├── Email Support
echo │   ├── Phone Support
echo │   └── Report Issues
echo └── Profile
echo     └── Help & Support Access
echo.

echo ========================================
echo Customer Support Features
echo ========================================
echo.
echo Live Chat Support Topics:
echo - Account & Login Issues
echo - Service Problems  
echo - Billing & Payments
echo - Order Support
echo - Technical Issues
echo - General Questions
echo.

echo Support Channels:
echo - Live Chat (Real-time messaging)
echo - Email Support (<EMAIL>)
echo - Phone Support (******-123-4567)
echo - FAQ Section
echo - Bug Reporting
echo.

echo ========================================
echo Ready to Launch!
echo ========================================
echo.

set /p launch="Would you like to launch the app now? (y/n): "
if /i "%launch%"=="y" (
    echo.
    echo Launching Projek Help Center Customer Support App...
    echo.
    flutter run
) else (
    echo.
    echo To launch the app later, run: flutter run
    echo.
    echo For help and documentation:
    echo - README.md - Complete application guide
    echo - HELP_CENTER_RESTRUCTURE_SUMMARY.md - Restructure details
    echo - AUTHENTICATION_FIX_GUIDE.md - Google Sign-In fix guide
    echo.
)

echo.
echo ========================================
echo Support & Documentation
echo ========================================
echo.
echo 📚 Documentation:
echo - README.md
echo - HELP_CENTER_RESTRUCTURE_SUMMARY.md
echo - AUTHENTICATION_FIX_GUIDE.md
echo.
echo 🆘 Get Support:
echo - In-App: Help Center → Live Chat
echo - Email: <EMAIL>
echo - Phone: +****************
echo.
echo 🐛 Report Issues:
echo - GitHub Issues
echo - In-App Bug Report
echo.

echo Thank you for using Projek Help Center Customer Support Application!
echo.
pause
