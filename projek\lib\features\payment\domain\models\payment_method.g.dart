// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_method.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 6;

  @override
  PaymentMethod read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentMethod(
      id: fields[0] as String,
      name: fields[1] as String,
      displayName: fields[2] as String,
      type: fields[3] as PaymentType,
      iconPath: fields[4] as String,
      isEnabled: fields[5] as bool,
      isPopular: fields[6] as bool,
      description: fields[7] as String?,
      processingFee: fields[8] as double?,
      minAmount: fields[9] as double?,
      maxAmount: fields[10] as double?,
      supportedCurrencies: (fields[11] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.iconPath)
      ..writeByte(5)
      ..write(obj.isEnabled)
      ..writeByte(6)
      ..write(obj.isPopular)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.processingFee)
      ..writeByte(9)
      ..write(obj.minAmount)
      ..writeByte(10)
      ..write(obj.maxAmount)
      ..writeByte(11)
      ..write(obj.supportedCurrencies);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentTransactionAdapter extends TypeAdapter<PaymentTransaction> {
  @override
  final int typeId = 7;

  @override
  PaymentTransaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentTransaction(
      id: fields[0] as String,
      orderId: fields[1] as String,
      paymentMethodId: fields[2] as String,
      amount: fields[3] as double,
      currency: fields[4] as String,
      status: fields[5] as PaymentStatus,
      createdAt: fields[6] as DateTime,
      completedAt: fields[7] as DateTime?,
      transactionId: fields[8] as String?,
      gatewayResponse: fields[9] as String?,
      failureReason: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentTransaction obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.orderId)
      ..writeByte(2)
      ..write(obj.paymentMethodId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.currency)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.completedAt)
      ..writeByte(8)
      ..write(obj.transactionId)
      ..writeByte(9)
      ..write(obj.gatewayResponse)
      ..writeByte(10)
      ..write(obj.failureReason);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentTypeAdapter extends TypeAdapter<PaymentType> {
  @override
  final int typeId = 5;

  @override
  PaymentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentType.upi;
      case 1:
        return PaymentType.wallet;
      case 2:
        return PaymentType.card;
      case 3:
        return PaymentType.netBanking;
      case 4:
        return PaymentType.cod;
      case 5:
        return PaymentType.emi;
      default:
        return PaymentType.upi;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentType obj) {
    switch (obj) {
      case PaymentType.upi:
        writer.writeByte(0);
        break;
      case PaymentType.wallet:
        writer.writeByte(1);
        break;
      case PaymentType.card:
        writer.writeByte(2);
        break;
      case PaymentType.netBanking:
        writer.writeByte(3);
        break;
      case PaymentType.cod:
        writer.writeByte(4);
        break;
      case PaymentType.emi:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 8;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.pending;
      case 1:
        return PaymentStatus.processing;
      case 2:
        return PaymentStatus.success;
      case 3:
        return PaymentStatus.failed;
      case 4:
        return PaymentStatus.cancelled;
      case 5:
        return PaymentStatus.refunded;
      default:
        return PaymentStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.pending:
        writer.writeByte(0);
        break;
      case PaymentStatus.processing:
        writer.writeByte(1);
        break;
      case PaymentStatus.success:
        writer.writeByte(2);
        break;
      case PaymentStatus.failed:
        writer.writeByte(3);
        break;
      case PaymentStatus.cancelled:
        writer.writeByte(4);
        break;
      case PaymentStatus.refunded:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) =>
    PaymentMethod(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      iconPath: json['iconPath'] as String,
      isEnabled: json['isEnabled'] as bool? ?? true,
      isPopular: json['isPopular'] as bool? ?? false,
      description: json['description'] as String?,
      processingFee: (json['processingFee'] as num?)?.toDouble(),
      minAmount: (json['minAmount'] as num?)?.toDouble(),
      maxAmount: (json['maxAmount'] as num?)?.toDouble(),
      supportedCurrencies: (json['supportedCurrencies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['INR'],
    );

Map<String, dynamic> _$PaymentMethodToJson(PaymentMethod instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'iconPath': instance.iconPath,
      'isEnabled': instance.isEnabled,
      'isPopular': instance.isPopular,
      'description': instance.description,
      'processingFee': instance.processingFee,
      'minAmount': instance.minAmount,
      'maxAmount': instance.maxAmount,
      'supportedCurrencies': instance.supportedCurrencies,
    };

const _$PaymentTypeEnumMap = {
  PaymentType.upi: 'upi',
  PaymentType.wallet: 'wallet',
  PaymentType.card: 'card',
  PaymentType.netBanking: 'netBanking',
  PaymentType.cod: 'cod',
  PaymentType.emi: 'emi',
};

PaymentTransaction _$PaymentTransactionFromJson(Map<String, dynamic> json) =>
    PaymentTransaction(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      paymentMethodId: json['paymentMethodId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      transactionId: json['transactionId'] as String?,
      gatewayResponse: json['gatewayResponse'] as String?,
      failureReason: json['failureReason'] as String?,
    );

Map<String, dynamic> _$PaymentTransactionToJson(PaymentTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'paymentMethodId': instance.paymentMethodId,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'transactionId': instance.transactionId,
      'gatewayResponse': instance.gatewayResponse,
      'failureReason': instance.failureReason,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.success: 'success',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.refunded: 'refunded',
};
