@echo off
echo ========================================
echo PROJEK APP - COMPREHENSIVE TEST SUITE
echo ========================================

echo.
echo 1. Testing Simple App (No Firebase)...
echo ----------------------------------------
flutter run test_app.dart --debug

echo.
echo 2. Testing Fixed App (With Firebase)...
echo ----------------------------------------
flutter run lib/main_fixed.dart --debug

echo.
echo 3. Testing Original App...
echo ----------------------------------------
flutter run lib/main.dart --debug

echo.
echo 4. Building Debug APK...
echo ----------------------------------------
flutter build apk --debug --target lib/main_fixed.dart

echo.
echo 5. Installing APK to Device...
echo ----------------------------------------
adb install build/app/outputs/flutter-apk/app-debug.apk

echo.
echo ========================================
echo TESTS COMPLETED!
echo ========================================
pause
