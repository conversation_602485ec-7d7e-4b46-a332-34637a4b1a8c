import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../marketplace/domain/models/product.dart';
import '../../domain/models/cart_item.dart';

// Cart repository provider
final cartRepositoryProvider = Provider<CartRepository>((ref) {
  return CartRepository();
});

// Cart state provider
final cartProvider = StateNotifierProvider<CartNotifier, Cart>((ref) {
  final repository = ref.watch(cartRepositoryProvider);
  return CartNotifier(repository);
});

// Cart item count provider
final cartItemCountProvider = Provider<int>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.itemCount;
});

// Cart total provider
final cartTotalProvider = Provider<double>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.total;
});

// Cart subtotal provider
final cartSubtotalProvider = Provider<double>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.subtotal;
});

// Check if product is in cart provider
final isProductInCartProvider = Provider.family<bool, String>((ref, productId) {
  final cart = ref.watch(cartProvider);
  return cart.items.any((item) => item.productId == productId);
});

// Get cart item by product ID provider
final cartItemByProductProvider = Provider.family<CartItem?, String>((
  ref,
  productId,
) {
  final cart = ref.watch(cartProvider);
  try {
    return cart.items.firstWhere((item) => item.productId == productId);
  } catch (e) {
    return null;
  }
});

class CartNotifier extends StateNotifier<Cart> {
  final CartRepository _repository;

  CartNotifier(this._repository) : super(Cart(updatedAt: DateTime.now())) {
    _loadCart();
  }

  Future<void> _loadCart() async {
    try {
      final cart = await _repository.getCart();
      state = cart;
    } catch (e) {
      // Handle error - keep empty cart
      state = Cart(updatedAt: DateTime.now());
    }
  }

  Future<void> addProduct(Product product, {int quantity = 1}) async {
    try {
      // Check if product already exists in cart
      final existingItemIndex = state.items.indexWhere(
        (item) => item.productId == product.id,
      );

      List<CartItem> updatedItems;

      if (existingItemIndex != -1) {
        // Update existing item quantity
        updatedItems = List.from(state.items);
        final existingItem = updatedItems[existingItemIndex];
        final newQuantity = existingItem.quantity + quantity;

        // Check max quantity limit
        if (newQuantity <= existingItem.maxQuantity) {
          updatedItems[existingItemIndex] = existingItem.copyWith(
            quantity: newQuantity,
          );
        } else {
          throw Exception('Cannot add more items. Maximum quantity reached.');
        }
      } else {
        // Add new item
        final cartItem = CartItem.fromProduct(product, quantity: quantity);
        updatedItems = [...state.items, cartItem];
      }

      final updatedCart = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> removeProduct(String productId) async {
    try {
      final updatedItems = state.items
          .where((item) => item.productId != productId)
          .toList();

      final updatedCart = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateQuantity(String productId, int quantity) async {
    try {
      if (quantity <= 0) {
        await removeProduct(productId);
        return;
      }

      final updatedItems = state.items.map((item) {
        if (item.productId == productId) {
          if (quantity <= item.maxQuantity) {
            return item.copyWith(quantity: quantity);
          } else {
            throw Exception('Cannot add more items. Maximum quantity reached.');
          }
        }
        return item;
      }).toList();

      final updatedCart = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> incrementQuantity(String productId) async {
    final item = state.items.firstWhere((item) => item.productId == productId);
    await updateQuantity(productId, item.quantity + 1);
  }

  Future<void> decrementQuantity(String productId) async {
    final item = state.items.firstWhere((item) => item.productId == productId);
    await updateQuantity(productId, item.quantity - 1);
  }

  Future<void> clearCart() async {
    try {
      final updatedCart = Cart(updatedAt: DateTime.now());
      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> applyPromoCode(String promoCode) async {
    try {
      // Simulate promo code validation
      double discount = 0.0;

      switch (promoCode.toUpperCase()) {
        case 'SAVE10':
          discount = state.subtotal * 0.10; // 10% discount
          break;
        case 'SAVE50':
          discount = 50.0; // ₹50 flat discount
          break;
        case 'FIRSTORDER':
          discount = state.subtotal * 0.15; // 15% discount for first order
          break;
        default:
          throw Exception('Invalid promo code');
      }

      final updatedCart = state.copyWith(
        promoCode: promoCode,
        promoDiscount: discount,
        updatedAt: DateTime.now(),
      );

      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> removePromoCode() async {
    try {
      final updatedCart = state.copyWith(
        promoCode: null,
        promoDiscount: 0.0,
        updatedAt: DateTime.now(),
      );

      state = updatedCart;
      await _repository.saveCart(updatedCart);
    } catch (e) {
      rethrow;
    }
  }
}

class CartRepository {
  static const String _boxName = AppConstants.cartBox;

  Future<Box<Cart>> get _box async {
    if (Hive.isBoxOpen(_boxName)) {
      return Hive.box<Cart>(_boxName);
    }
    return await Hive.openBox<Cart>(_boxName);
  }

  Future<Cart> getCart() async {
    try {
      final box = await _box;
      final cart = box.get('cart');
      return cart ?? Cart(updatedAt: DateTime.now());
    } catch (e) {
      return Cart(updatedAt: DateTime.now());
    }
  }

  Future<void> saveCart(Cart cart) async {
    try {
      final box = await _box;
      await box.put('cart', cart);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> clearCart() async {
    try {
      final box = await _box;
      await box.delete('cart');
    } catch (e) {
      rethrow;
    }
  }
}
