class AppConstants {
  // App Info
  static const String appName = 'Projek';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Multi-vendor marketplace platform';

  // API
  static const String baseUrl = 'https://api.projek.com/v1/';
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';
  static const String cartKey = 'cart_items';
  static const String wishlistKey = 'wishlist_items';

  // Hive Boxes
  static const String userBox = 'user_box';
  static const String cartBox = 'cart_box';
  static const String wishlistBox = 'wishlist_box';
  static const String settingsBox = 'settings_box';
  static const String walletBox = 'projek_coin_wallet_box';
  static const String walletStatsBox = 'wallet_statistics_box';

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Categories
  static const List<String> productCategories = [
    'Food & Beverages',
    'Clothing & Fashion',
    'Electronics',
    'Home & Garden',
    'Sports & Outdoors',
    'Books & Media',
    'Health & Beauty',
    'Automotive',
  ];

  static const List<String> serviceCategories = [
    'Vehicle Rentals',
    'Home Services',
    'Professional Services',
    'Education & Tutoring',
    'Health & Wellness',
    'Event Services',
    'Delivery Services',
    'Maintenance & Repair',
  ];

  // Payment Methods
  static const List<String> paymentMethods = [
    'Google Pay',
    'PhonePe',
    'Paytm',
    'UPI',
    'Credit Card',
    'Debit Card',
    'Net Banking',
    'Cash on Delivery',
    'EMI',
  ];

  // Indian Payment Methods
  static const List<String> upiMethods = [
    'Google Pay',
    'PhonePe',
    'Paytm',
    'BHIM UPI',
    'Amazon Pay',
  ];

  static const List<String> walletMethods = [
    'Paytm Wallet',
    'MobiKwik',
    'FreeCharge',
    'Amazon Pay',
    'Ola Money',
  ];

  // Currency
  static const String defaultCurrency = 'INR';
  static const String currencySymbol = '₹';

  // Projek Coin
  static const String projekCoinSymbol = 'PC';
  static const String projekCoinName = 'Projek Coin';
  static const double projekCoinToINRRate = 1.0; // 1 PC = ₹1
  static const double minWalletBalance = 0.0;
  static const double maxWalletBalance = 100000.0;
  static const double minTransactionAmount = 1.0;
  static const double maxTransactionAmount = 10000.0;

  // Payment Limits
  static const double maxCodAmount = 5000.0;
  static const double minEmiAmount = 3000.0;
  static const double freeDeliveryThreshold = 500.0;
  static const double deliveryFee = 40.0;
  static const double gstRate = 0.18; // 18% GST

  // Promo Codes
  static const List<String> validPromoCodes = [
    'SAVE10',
    'SAVE50',
    'FIRSTORDER',
    'WELCOME20',
    'FESTIVAL25',
  ];

  // Order Status
  static const String orderPending = 'pending';
  static const String orderConfirmed = 'confirmed';
  static const String orderProcessing = 'processing';
  static const String orderShipped = 'shipped';
  static const String orderDelivered = 'delivered';
  static const String orderCancelled = 'cancelled';
  static const String orderRefunded = 'refunded';

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  static const String emailPattern =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';

  // Map Settings
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 5.0;
  static const Duration locationUpdateInterval = Duration(seconds: 5);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const int maxImagesPerProduct = 10;
}
