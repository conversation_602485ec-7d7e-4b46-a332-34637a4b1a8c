import 'package:flutter/material.dart';

enum AppType { user, rider, seller }

class AppConfig {
  static AppType _currentAppType = AppType.user;
  
  static AppType get currentAppType => _currentAppType;
  
  static void setAppType(AppType type) {
    _currentAppType = type;
  }
  
  static bool get isUserApp => _currentAppType == AppType.user;
  static bool get isRiderApp => _currentAppType == AppType.rider;
  static bool get isSellerApp => _currentAppType == AppType.seller;
  
  // App-specific configurations
  static String get appName {
    switch (_currentAppType) {
      case AppType.user:
        return 'Projek';
      case AppType.rider:
        return 'Projek Rider';
      case AppType.seller:
        return 'Projek Seller';
    }
  }
  
  static String get packageName {
    switch (_currentAppType) {
      case AppType.user:
        return 'com.projek.user';
      case AppType.rider:
        return 'com.projek.rider';
      case AppType.seller:
        return 'com.projek.seller';
    }
  }
  
  static Color get primaryColor {
    switch (_currentAppType) {
      case AppType.user:
        return const Color(0xFF2196F3); // Blue
      case AppType.rider:
        return const Color(0xFF4CAF50); // Green
      case AppType.seller:
        return const Color(0xFFFF9800); // Orange
    }
  }
  
  static Color get accentColor {
    switch (_currentAppType) {
      case AppType.user:
        return const Color(0xFF03DAC6); // Teal
      case AppType.rider:
        return const Color(0xFF8BC34A); // Light Green
      case AppType.seller:
        return const Color(0xFFFFC107); // Amber
    }
  }
  
  // App-specific features
  static List<String> get enabledFeatures {
    switch (_currentAppType) {
      case AppType.user:
        return [
          'wallet',
          'marketplace',
          'bookings',
          'education',
          'bills',
          'travel',
          'insurance'
        ];
      case AppType.rider:
        return [
          'orders',
          'navigation',
          'earnings',
          'vehicle_management',
          'insurance'
        ];
      case AppType.seller:
        return [
          'products',
          'orders',
          'analytics',
          'inventory',
          'courses',
          'services'
        ];
    }
  }
  
  // Firebase project configurations
  static String get firebaseProjectId {
    switch (_currentAppType) {
      case AppType.user:
        return 'projek-user-app';
      case AppType.rider:
        return 'projek-rider-app';
      case AppType.seller:
        return 'projek-seller-app';
    }
  }
  
  // API endpoints (if different per app)
  static String get baseApiUrl {
    switch (_currentAppType) {
      case AppType.user:
        return 'https://api.projek.com/user';
      case AppType.rider:
        return 'https://api.projek.com/rider';
      case AppType.seller:
        return 'https://api.projek.com/seller';
    }
  }
  
  // App-specific constants
  static Map<String, dynamic> get appConstants {
    switch (_currentAppType) {
      case AppType.user:
        return {
          'max_cart_items': 50,
          'wallet_limit': 50000.0,
          'referral_bonus': 100.0,
        };
      case AppType.rider:
        return {
          'max_daily_orders': 30,
          'commission_rate': 0.15,
          'fuel_allowance': 50.0,
        };
      case AppType.seller:
        return {
          'max_products': 1000,
          'commission_rate': 0.10,
          'subscription_fee': 299.0,
        };
    }
  }
}
