// Super Vision Homepage JavaScript
document.addEventListener('DOMContentLoaded', function() {
    
    // Mobile Navigation Toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Smooth Scrolling for Navigation Links
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Close mobile menu if open
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            }
        });
    });
    
    // Navbar Background on Scroll
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 30px rgba(0, 0, 0, 0.15)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    });
    
    // Intersection Observer for Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.about-card, .service-category, .app-card, .contact-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Counter Animation for Statistics
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        const timer = setInterval(() => {
            start += increment;
            element.textContent = Math.floor(start) + '+';
            
            if (start >= target) {
                element.textContent = target + '+';
                clearInterval(timer);
            }
        }, 16);
    }
    
    // Animate hero stats when they come into view
    const heroStats = document.querySelectorAll('.stat-number');
    const heroStatsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseFloat(entry.target.textContent.replace(/[^\d.]/g, ''));
                const unit = entry.target.textContent.includes('K') ? 'K' : '';
                
                let start = 0;
                const timer = setInterval(() => {
                    start += target / 100;
                    entry.target.textContent = Math.floor(start * 10) / 10 + unit + '+';
                    
                    if (start >= target) {
                        entry.target.textContent = target + unit + '+';
                        clearInterval(timer);
                    }
                }, 20);
                
                heroStatsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    heroStats.forEach(stat => {
        heroStatsObserver.observe(stat);
    });
    
    // Parallax Effect for Hero Shapes
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const shapes = document.querySelectorAll('.shape');
        
        shapes.forEach((shape, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            shape.style.transform = `translateY(${yPos}px) rotate(${scrolled * 0.1}deg)`;
        });
    });
    
    // Contact Form Handling
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="material-icons">sync</span> Sending...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                showNotification('Thank you! Your message has been sent successfully. We\'ll get back to you soon!', 'success');
                contactForm.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }
    
    // App Card Hover Effects
    const appCards = document.querySelectorAll('.app-card');
    appCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Service Category Hover Effects
    const serviceCategories = document.querySelectorAll('.service-category');
    serviceCategories.forEach(category => {
        category.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.category-icon');
            icon.style.transform = 'scale(1.2) rotate(10deg)';
        });
        
        category.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.category-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });
    
    // Floating Action Button Interactions
    const fabs = document.querySelectorAll('.fab');
    fabs.forEach(fab => {
        fab.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.15)';
            this.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
        });
        
        fab.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
        });
    });
    
    // Notification System
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            color: white;
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        `;
        
        if (type === 'success') {
            notification.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        } else if (type === 'error') {
            notification.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
        } else {
            notification.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
    
    // Phone Mockup Animation
    const phoneMockup = document.querySelector('.phone-mockup');
    if (phoneMockup) {
        phoneMockup.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotateY(5deg)';
        });
        
        phoneMockup.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotateY(0deg)';
        });
    }
    
    // Dynamic App Interface Animation
    const serviceCards = document.querySelectorAll('.service-card');
    let currentCard = 0;
    
    function animateServiceCards() {
        serviceCards.forEach((card, index) => {
            card.style.opacity = index === currentCard ? '1' : '0.5';
            card.style.transform = index === currentCard ? 'scale(1.05)' : 'scale(1)';
        });
        
        currentCard = (currentCard + 1) % serviceCards.length;
    }
    
    // Start service card animation
    if (serviceCards.length > 0) {
        setInterval(animateServiceCards, 2000);
    }
    
    // Scroll to Top Functionality
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '↑';
    scrollToTopBtn.className = 'scroll-to-top';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 100px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        font-size: 20px;
        cursor: pointer;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    `;
    
    document.body.appendChild(scrollToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 500) {
            scrollToTopBtn.style.opacity = '1';
        } else {
            scrollToTopBtn.style.opacity = '0';
        }
    });
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .nav-menu.active {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            border-radius: 0 0 15px 15px;
        }
        
        .nav-toggle.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }
        
        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }
        
        .nav-toggle.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }
        
        .scroll-to-top:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
    `;
    document.head.appendChild(style);
    
    // Initialize welcome message
    setTimeout(() => {
        showNotification('🙏 Welcome to Super Vision! Connecting all of India on one platform. 🇮🇳', 'info');
    }, 1000);
    
    console.log('Super Vision Homepage initialized successfully!');
    console.log('🇮🇳 Jai Hind! Welcome to the future of Indian services! 🚀');

    // Initialize Gaming and Wallet
    window.homeGamingWallet = new HomeGamingWallet();
});

// Gaming and Wallet Integration
class HomeGamingWallet {
  constructor() {
    this.balances = {
      pc: 1250,
      gaming: 450,
      staking: 2100
    };
    this.balanceVisible = true;
    this.isSpinning = false;

    this.initializeGamingWallet();
  }

  initializeGamingWallet() {
    this.updateBalances();
  }

  updateBalances() {
    const pcElement = document.getElementById('pcBalance');
    const gamingElement = document.getElementById('gamingBalance');

    if (pcElement) {
      pcElement.textContent = this.balanceVisible ? `${this.balances.pc.toLocaleString()} PC` : '••••••';
    }

    if (gamingElement) {
      gamingElement.textContent = this.balanceVisible ? `${this.balances.gaming.toLocaleString()} PC` : '••••••';
    }
  }

  toggleWalletBalance() {
    this.balanceVisible = !this.balanceVisible;
    const visibilityIcon = document.getElementById('walletVisibility');
    if (visibilityIcon) {
      visibilityIcon.textContent = this.balanceVisible ? 'visibility' : 'visibility_off';
    }
    this.updateBalances();
  }

  handleSpin(spinType) {
    if (this.isSpinning) {
      this.showMessage('Please wait for the current spin to complete!', 'warning');
      return;
    }

    const entryFee = spinType === 'regular' ? 10 : 50;

    if (this.balances.gaming < entryFee) {
      this.showMessage('Insufficient gaming balance! Please add money.', 'error');
      return;
    }

    // Deduct entry fee
    this.balances.gaming -= entryFee;
    this.updateBalances();

    // Start spinning
    this.isSpinning = true;
    this.disableSpinButtons();

    // Calculate winning
    const result = this.calculateSpinResult(spinType);

    // Animate wheel spin
    this.animateWheelSpin(result, spinType);
  }

  calculateSpinResult(spinType) {
    const random = Math.random();
    let winAmount = 0;
    let segment = 0;

    // Probability distribution for fair gaming
    if (random < 0.4) {
      // 40% chance of small wins
      const smallWins = [10, 25, 50];
      winAmount = smallWins[Math.floor(Math.random() * smallWins.length)];
      segment = this.getSegmentForAmount(winAmount);
    } else if (random < 0.65) {
      // 25% chance of medium wins
      const mediumWins = [100, 250];
      winAmount = mediumWins[Math.floor(Math.random() * mediumWins.length)];
      segment = this.getSegmentForAmount(winAmount);
    } else if (random < 0.85) {
      // 20% chance of no win
      winAmount = 0;
      segment = 6; // "Try Again" segment
    } else if (random < 0.98) {
      // 13% chance of big win
      winAmount = 500;
      segment = 5;
    } else {
      // 2% chance of jackpot
      winAmount = spinType === 'max' ? 1500 : 500;
      segment = spinType === 'max' ? 7 : 5;
    }

    // Apply multiplier for max spin
    if (spinType === 'max' && winAmount > 0 && winAmount < 1500) {
      winAmount *= 3;
    }

    return { winAmount, segment };
  }

  getSegmentForAmount(amount) {
    const amountToSegment = {
      10: 0, 25: 1, 50: 2, 100: 3, 250: 4, 500: 5, 0: 6, 1500: 7
    };
    return amountToSegment[amount] || 6;
  }

  animateWheelSpin(result, spinType) {
    const { winAmount, segment } = result;
    const wheel = document.getElementById('miniWheel');

    if (!wheel) return;

    // Calculate rotation angle
    const segmentAngle = 45; // 360/8 segments
    const targetAngle = (segment * segmentAngle) + (Math.random() * segmentAngle);
    const spins = 5 + Math.random() * 3; // 5-8 full rotations
    const totalRotation = (spins * 360) + targetAngle;

    // Apply rotation
    wheel.style.transform = `rotate(${totalRotation}deg)`;

    // Handle spin completion after animation
    setTimeout(() => {
      this.handleSpinComplete(winAmount, spinType);
    }, 3000);
  }

  handleSpinComplete(winAmount, spinType) {
    this.isSpinning = false;
    this.enableSpinButtons();

    if (winAmount > 0) {
      // Add winnings to balance
      this.balances.gaming += winAmount;
      this.balances.pc += winAmount;
      this.updateBalances();

      // Show winning message
      this.showWinningAnimation(winAmount);
      this.showMessage(`🎉 Congratulations! You won ${winAmount} PC!`, 'success');
    } else {
      this.showMessage('Better luck next time! Try again.', 'info');
    }
  }

  showWinningAnimation(amount) {
    // Create confetti effect
    this.createConfetti();

    // Show winning popup
    const winDisplay = document.createElement('div');
    winDisplay.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff9933, #138808);
        color: white;
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        z-index: 10000;
        animation: bounceIn 0.5s ease-out;
      ">
        <h2>🎉 YOU WON! 🎉</h2>
        <p style="font-size: 2rem; margin: 1rem 0;">${amount} PC</p>
        <p>Added to your gaming balance!</p>
      </div>
    `;

    document.body.appendChild(winDisplay);

    setTimeout(() => {
      winDisplay.remove();
    }, 3000);
  }

  createConfetti() {
    for (let i = 0; i < 30; i++) {
      const confetti = document.createElement('div');
      confetti.style.cssText = `
        position: fixed;
        width: 8px;
        height: 8px;
        background: ${this.getRandomColor()};
        top: -10px;
        left: ${Math.random() * 100}%;
        animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
        z-index: 9999;
      `;
      document.body.appendChild(confetti);

      setTimeout(() => confetti.remove(), 5000);
    }
  }

  getRandomColor() {
    const colors = ['#ff9933', '#138808', '#146eb4', '#e74c3c', '#f39c12', '#27ae60'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  disableSpinButtons() {
    const spinButtons = document.querySelectorAll('.game-btn');
    spinButtons.forEach(btn => {
      btn.disabled = true;
      btn.style.opacity = '0.6';
      btn.textContent = 'Spinning...';
    });
  }

  enableSpinButtons() {
    const spinButtons = document.querySelectorAll('.game-btn');
    spinButtons.forEach(btn => {
      btn.disabled = false;
      btn.style.opacity = '1';

      if (btn.classList.contains('max')) {
        btn.textContent = 'Max Spin';
      } else {
        btn.textContent = 'Spin Now';
      }
    });
  }

  openDepositModal() {
    this.showMessage('Deposit feature: Add INR and convert to PC at 1:1 ratio!', 'info');
  }

  openWithdrawModal() {
    this.showMessage('Withdraw feature: Convert PC to INR and send to bank!', 'info');
  }

  openStakeModal() {
    this.showMessage('Staking feature: Earn 8-18% APY on your ProjekCoin!', 'info');
  }

  showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 1rem 2rem;
      border-radius: 10px;
      color: white;
      font-weight: 600;
      z-index: 10001;
      animation: slideIn 0.3s ease-out;
      max-width: 350px;
    `;

    const colors = {
      success: '#28a745',
      error: '#dc3545',
      warning: '#ffc107',
      info: '#17a2b8'
    };
    messageDiv.style.background = colors[type] || colors.info;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
      messageDiv.style.animation = 'slideOut 0.3s ease-out forwards';
      setTimeout(() => messageDiv.remove(), 300);
    }, 4000);
  }
}

// Global functions for HTML onclick events
function toggleWalletBalance() {
  if (window.homeGamingWallet) {
    window.homeGamingWallet.toggleWalletBalance();
  }
}

function handleSpin(spinType) {
  if (window.homeGamingWallet) {
    window.homeGamingWallet.handleSpin(spinType);
  }
}

function openDepositModal() {
  if (window.homeGamingWallet) {
    window.homeGamingWallet.openDepositModal();
  }
}

function openWithdrawModal() {
  if (window.homeGamingWallet) {
    window.homeGamingWallet.openWithdrawModal();
  }
}

function openStakeModal() {
  if (window.homeGamingWallet) {
    window.homeGamingWallet.openStakeModal();
  }
}
