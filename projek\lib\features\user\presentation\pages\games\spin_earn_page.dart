import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

final spinWheelProvider = StateNotifierProvider<SpinWheelNotifier, SpinWheelState>((ref) {
  return SpinWheelNotifier();
});

class SpinWheelState {
  final bool isSpinning;
  final double rotation;
  final int coins;
  final String lastReward;
  final int spinsLeft;
  final DateTime? lastSpinTime;

  SpinWheelState({
    this.isSpinning = false,
    this.rotation = 0.0,
    this.coins = 100, // Starting coins
    this.lastReward = '',
    this.spinsLeft = 5, // Daily spins
    this.lastSpinTime,
  });

  SpinWheelState copyWith({
    bool? isSpinning,
    double? rotation,
    int? coins,
    String? lastReward,
    int? spinsLeft,
    DateTime? lastSpinTime,
  }) {
    return SpinWheelState(
      isSpinning: isSpinning ?? this.isSpinning,
      rotation: rotation ?? this.rotation,
      coins: coins ?? this.coins,
      lastReward: lastReward ?? this.lastReward,
      spinsLeft: spinsLeft ?? this.spinsLeft,
      lastSpinTime: lastSpinTime ?? this.lastSpinTime,
    );
  }
}

class SpinWheelNotifier extends StateNotifier<SpinWheelState> {
  SpinWheelNotifier() : super(SpinWheelState());

  final List<SpinReward> rewards = [
    SpinReward('10 Coins', 10, AppColors.success),
    SpinReward('5 Coins', 5, AppColors.info),
    SpinReward('20 Coins', 20, AppColors.warning),
    SpinReward('Try Again', 0, AppColors.error),
    SpinReward('50 Coins', 50, AppColors.userPrimary),
    SpinReward('15 Coins', 15, AppColors.accentGreen),
    SpinReward('25 Coins', 25, AppColors.secondaryOrange),
    SpinReward('100 Coins', 100, AppColors.ratingGold),
  ];

  Future<void> spin() async {
    if (state.isSpinning || state.spinsLeft <= 0) return;

    state = state.copyWith(isSpinning: true);

    // Random rotation between 5-10 full rotations plus random angle
    final random = math.Random();
    final extraRotations = 5 + random.nextInt(6); // 5-10 rotations
    final finalAngle = random.nextDouble() * 360; // 0-360 degrees
    final totalRotation = (extraRotations * 360) + finalAngle;

    // Animate rotation
    state = state.copyWith(rotation: state.rotation + totalRotation);

    // Wait for animation
    await Future.delayed(const Duration(seconds: 3));

    // Calculate reward based on final angle
    final rewardIndex = ((finalAngle / 45).floor()) % rewards.length;
    final reward = rewards[rewardIndex];

    // Update state with reward
    state = state.copyWith(
      isSpinning: false,
      coins: state.coins + reward.coins,
      lastReward: reward.name,
      spinsLeft: state.spinsLeft - 1,
      lastSpinTime: DateTime.now(),
    );

    // Log analytics
    await AnalyticsService.logEvent('spin_wheel_used', {
      'reward': reward.name,
      'coins_earned': reward.coins,
      'total_coins': state.coins,
      'spins_left': state.spinsLeft,
    });
  }

  void resetDailySpins() {
    final now = DateTime.now();
    final lastSpin = state.lastSpinTime;
    
    if (lastSpin == null || 
        now.difference(lastSpin).inHours >= 24) {
      state = state.copyWith(spinsLeft: 5);
    }
  }
}

class SpinReward {
  final String name;
  final int coins;
  final Color color;

  SpinReward(this.name, this.coins, this.color);
}

class SpinEarnPage extends ConsumerWidget {
  const SpinEarnPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spinState = ref.watch(spinWheelProvider);
    final spinNotifier = ref.read(spinWheelProvider.notifier);

    // Reset daily spins on page load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      spinNotifier.resetDailySpins();
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('🎰 Spin & Earn'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
        actions: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Icon(Icons.monetization_on, color: AppColors.ratingGold),
                const SizedBox(width: 4),
                Text(
                  '${spinState.coins}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.userPrimary,
              AppColors.backgroundLight,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Info
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildInfoCard(
                      '🎯 Spins Left',
                      '${spinState.spinsLeft}/5',
                      AppColors.info,
                    ),
                    _buildInfoCard(
                      '💰 Total Coins',
                      '${spinState.coins}',
                      AppColors.success,
                    ),
                    _buildInfoCard(
                      '🏆 Last Reward',
                      spinState.lastReward.isEmpty ? 'None' : spinState.lastReward,
                      AppColors.warning,
                    ),
                  ],
                ),
              ),

              // Spin Wheel
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Wheel Container
                      Container(
                        width: 300,
                        height: 300,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Spin Wheel
                            AnimatedRotation(
                              turns: spinState.rotation / 360,
                              duration: Duration(
                                milliseconds: spinState.isSpinning ? 3000 : 0,
                              ),
                              curve: Curves.easeOutCubic,
                              child: CustomPaint(
                                size: const Size(300, 300),
                                painter: SpinWheelPainter(spinNotifier.rewards),
                              ),
                            ),
                            // Center Button
                            Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.play_arrow,
                                size: 40,
                                color: AppColors.userPrimary,
                              ),
                            ),
                            // Pointer
                            Positioned(
                              top: 10,
                              child: Container(
                                width: 0,
                                height: 0,
                                decoration: const BoxDecoration(
                                  border: Border(
                                    left: BorderSide(width: 15, color: Colors.transparent),
                                    right: BorderSide(width: 15, color: Colors.transparent),
                                    bottom: BorderSide(width: 30, color: Colors.red),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 40),

                      // Spin Button
                      ElevatedButton(
                        onPressed: spinState.isSpinning || spinState.spinsLeft <= 0
                            ? null
                            : () => spinNotifier.spin(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.userPrimary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 40,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          elevation: 8,
                        ),
                        child: Text(
                          spinState.isSpinning
                              ? '🎰 Spinning...'
                              : spinState.spinsLeft <= 0
                                  ? '⏰ Come back tomorrow'
                                  : '🎯 SPIN NOW!',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      if (spinState.spinsLeft <= 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Text(
                            'Daily spins reset every 24 hours',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}

class SpinWheelPainter extends CustomPainter {
  final List<SpinReward> rewards;

  SpinWheelPainter(this.rewards);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final sectionAngle = 2 * math.pi / rewards.length;

    for (int i = 0; i < rewards.length; i++) {
      final startAngle = i * sectionAngle - math.pi / 2;
      final paint = Paint()
        ..color = rewards[i].color
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sectionAngle,
        true,
        paint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sectionAngle,
        true,
        borderPaint,
      );

      // Draw text
      final textAngle = startAngle + sectionAngle / 2;
      final textRadius = radius * 0.7;
      final textX = center.dx + textRadius * math.cos(textAngle);
      final textY = center.dy + textRadius * math.sin(textAngle);

      final textPainter = TextPainter(
        text: TextSpan(
          text: rewards[i].name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          textX - textPainter.width / 2,
          textY - textPainter.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
