rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.admin == true;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection - users can only read/write their own profile
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Messages collection - authenticated users can read and create their own messages
    match /messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated()
        && request.auth.uid == request.resource.data.userId
        && request.auth.token.email == request.resource.data.userEmail;
      allow update, delete: if false;
    }

    // Support chats collection
    match /support_chats/{chatId} {
      allow read, write: if isAuthenticated() &&
        (isOwner(resource.data.userId) || isAdmin());

      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;

      // Messages subcollection inside support chats
      match /messages/{messageId} {
        function getChatOwner() {
          return get(/databases/$(database)/documents/support_chats/$(chatId)).data.userId;
        }

        function isChatOwner() {
          return isAuthenticated() && request.auth.uid == getChatOwner();
        }

        allow read: if isAuthenticated() && (isChatOwner() || isAdmin());

        allow create: if isAuthenticated() &&
          ((isChatOwner() && request.auth.uid == request.resource.data.senderId) ||
           (isAdmin() && request.resource.data.senderType == 'agent'));

        allow update: if isAuthenticated() && (isChatOwner() || isAdmin());
        allow delete: if false;
      }
    }

    // Support agents collection - admin only
    match /support_agents/{agentId} {
      allow read, write: if isAdmin();
    }
  }
}