@echo off
title Projek User App - Development Mode
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 PROJEK USER APP - DEV MODE                  ║
echo ║              Real-time Development with Hot Reload          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Setting up development environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
set FLUTTER_WEB_USE_SKIA=true
set FLUTTER_WEB_AUTO_DETECT=true
echo ✅ Environment configured

echo.
echo 📱 Checking connected devices...
flutter devices
echo.

echo 📦 Ensuring dependencies are up to date...
flutter pub get
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    DEVELOPMENT COMMANDS                     ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  r  │ Hot Reload    │ Apply changes instantly (keeps state) ║
echo ║  R  │ Hot Restart   │ Restart app completely (resets state) ║
echo ║  h  │ Help          │ Show all available commands           ║
echo ║  d  │ Detach        │ Detach debugger (app keeps running)   ║
echo ║  c  │ Clear         │ Clear console output                  ║
echo ║  q  │ Quit          │ Stop app and exit development         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting Projek User App on your Android device...
echo 📱 Device: V2130 (1397182984001HG)
echo ⚡ Hot reload enabled for real-time development
echo.

flutter run --target lib/main_user.dart -d 1397182984001HG --hot --verbose

echo.
echo 🏁 Development session ended.
echo Press any key to exit...
pause >nul
