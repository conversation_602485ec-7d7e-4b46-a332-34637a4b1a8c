import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../marketplace/domain/models/product.dart';
import '../../domain/models/wishlist_item.dart';

// Wishlist repository provider
final wishlistRepositoryProvider = Provider<WishlistRepository>((ref) {
  return WishlistRepository();
});

// Wishlist state provider
final wishlistProvider = StateNotifierProvider<WishlistNotifier, Wishlist>((
  ref,
) {
  final repository = ref.watch(wishlistRepositoryProvider);
  return WishlistNotifier(repository);
});

// Wishlist item count provider
final wishlistItemCountProvider = Provider<int>((ref) {
  final wishlist = ref.watch(wishlistProvider);
  return wishlist.itemCount;
});

// Check if product is in wishlist provider
final isProductInWishlistProvider = Provider.family<bool, String>((
  ref,
  productId,
) {
  final wishlist = ref.watch(wishlistProvider);
  return wishlist.containsProduct(productId);
});

// Get wishlist item by product ID provider
final wishlistItemByProductProvider = Provider.family<WishlistItem?, String>((
  ref,
  productId,
) {
  final wishlist = ref.watch(wishlistProvider);
  return wishlist.getItemByProductId(productId);
});

// In-stock wishlist items provider
final inStockWishlistItemsProvider = Provider<List<WishlistItem>>((ref) {
  final wishlist = ref.watch(wishlistProvider);
  return wishlist.inStockItems;
});

// Out-of-stock wishlist items provider
final outOfStockWishlistItemsProvider = Provider<List<WishlistItem>>((ref) {
  final wishlist = ref.watch(wishlistProvider);
  return wishlist.outOfStockItems;
});

class WishlistNotifier extends StateNotifier<Wishlist> {
  final WishlistRepository _repository;

  WishlistNotifier(this._repository)
    : super(Wishlist(updatedAt: DateTime.now())) {
    _loadWishlist();
  }

  Future<void> _loadWishlist() async {
    try {
      final wishlist = await _repository.getWishlist();
      state = wishlist;
    } catch (e) {
      // Handle error - keep empty wishlist
      state = Wishlist(updatedAt: DateTime.now());
    }
  }

  Future<void> addProduct(Product product) async {
    try {
      // Check if product already exists in wishlist
      if (state.containsProduct(product.id)) {
        throw Exception('Product already in wishlist');
      }

      final wishlistItem = WishlistItem.fromProduct(product);
      final updatedItems = [...state.items, wishlistItem];

      final updatedWishlist = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedWishlist;
      await _repository.saveWishlist(updatedWishlist);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> removeProduct(String productId) async {
    try {
      final updatedItems = state.items
          .where((item) => item.productId != productId)
          .toList();

      final updatedWishlist = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedWishlist;
      await _repository.saveWishlist(updatedWishlist);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> toggleProduct(Product product) async {
    try {
      if (state.containsProduct(product.id)) {
        await removeProduct(product.id);
      } else {
        await addProduct(product);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> clearWishlist() async {
    try {
      final updatedWishlist = Wishlist(updatedAt: DateTime.now());
      state = updatedWishlist;
      await _repository.saveWishlist(updatedWishlist);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> moveToCart(String productId) async {
    try {
      // This would typically involve calling the cart provider
      // For now, we'll just remove from wishlist
      await removeProduct(productId);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> moveAllToCart() async {
    try {
      // This would typically involve calling the cart provider for each item
      // For now, we'll just clear the wishlist
      await clearWishlist();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateItemStock(String productId, bool inStock) async {
    try {
      final updatedItems = state.items.map((item) {
        if (item.productId == productId) {
          return item.copyWith(inStock: inStock);
        }
        return item;
      }).toList();

      final updatedWishlist = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedWishlist;
      await _repository.saveWishlist(updatedWishlist);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateItemPrice(
    String productId,
    double newPrice,
    double newOriginalPrice,
  ) async {
    try {
      final updatedItems = state.items.map((item) {
        if (item.productId == productId) {
          return item.copyWith(
            price: newPrice,
            originalPrice: newOriginalPrice,
          );
        }
        return item;
      }).toList();

      final updatedWishlist = state.copyWith(
        items: updatedItems,
        updatedAt: DateTime.now(),
      );

      state = updatedWishlist;
      await _repository.saveWishlist(updatedWishlist);
    } catch (e) {
      rethrow;
    }
  }
}

class WishlistRepository {
  static const String _boxName = AppConstants.wishlistBox;

  Future<Box<Wishlist>> get _box async {
    if (Hive.isBoxOpen(_boxName)) {
      return Hive.box<Wishlist>(_boxName);
    }
    return await Hive.openBox<Wishlist>(_boxName);
  }

  Future<Wishlist> getWishlist() async {
    try {
      final box = await _box;
      final wishlist = box.get('wishlist');
      return wishlist ?? Wishlist(updatedAt: DateTime.now());
    } catch (e) {
      return Wishlist(updatedAt: DateTime.now());
    }
  }

  Future<void> saveWishlist(Wishlist wishlist) async {
    try {
      final box = await _box;
      await box.put('wishlist', wishlist);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> clearWishlist() async {
    try {
      final box = await _box;
      await box.delete('wishlist');
    } catch (e) {
      rethrow;
    }
  }
}
