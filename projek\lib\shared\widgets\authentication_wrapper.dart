import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:go_router/go_router.dart';

class AuthenticationWrapper extends ConsumerWidget {
  final AsyncValue<User?> authState;
  final Widget child;

  const AuthenticationWrapper({
    super.key,
    required this.authState,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return authState.when(
      data: (user) {
        // Check if we're on an auth page
        final currentLocation = GoRouterState.of(context).uri.path;
        final isAuthPage = _isAuthenticationPage(currentLocation);
        
        // If user is logged in but on auth page, redirect to home
        if (user != null && isAuthPage) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (context.mounted) {
              context.go('/home');
            }
          });
        }
        
        // If user is not logged in and not on auth page, redirect to login
        if (user == null && !isAuthPage && !_isPublicPage(currentLocation)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (context.mounted) {
              context.go('/login');
            }
          });
        }
        
        return child;
      },
      loading: () => const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading...'),
            ],
          ),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Authentication Error',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Go to Login'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _isAuthenticationPage(String path) {
    const authPaths = [
      '/login',
      '/register',
      '/forgot-password',
      '/phone-auth',
      '/uid-registration',
      '/otp-verification',
    ];
    return authPaths.any((authPath) => path.startsWith(authPath));
  }

  bool _isPublicPage(String path) {
    const publicPaths = [
      '/',
      '/splash',
      '/onboarding',
      '/login',
      '/register',
      '/forgot-password',
      '/phone-auth',
      '/uid-registration',
      '/otp-verification',
    ];
    return publicPaths.any((publicPath) => path == publicPath || path.startsWith(publicPath));
  }
}
