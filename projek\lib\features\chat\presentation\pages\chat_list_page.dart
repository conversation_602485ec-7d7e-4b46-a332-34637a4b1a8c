import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/services/chat_service.dart';
import '../../../../core/models/chat_models.dart';
import '../../../../core/services/analytics_service.dart';
import 'chat_detail_page.dart';
import 'new_chat_page.dart';

final userChatsProvider = StreamProvider<List<Chat>>((ref) {
  final currentUser = FirebaseAuth.instance.currentUser;
  if (currentUser == null) {
    return Stream.value([]);
  }
  return ChatService.getUserChats(currentUser.uid);
});

class ChatListPage extends ConsumerStatefulWidget {
  const ChatListPage({super.key});

  @override
  ConsumerState<ChatListPage> createState() => _ChatListPageState();
}

class _ChatListPageState extends ConsumerState<ChatListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    AnalyticsService.logEvent('chat_list_opened', null);
    // Update online status
    ChatService.updateOnlineStatus(true);
  }

  @override
  void dispose() {
    ChatService.updateOnlineStatus(false);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final chatsAsync = ref.watch(userChatsProvider);
    final currentUser = FirebaseAuth.instance.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('💬 Messages'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.add_comment),
            onPressed: () => _navigateToNewChat(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Quick Actions
          _buildQuickActions(context),

          // Search Bar (if searching)
          if (_searchQuery.isNotEmpty) _buildSearchBar(),

          // Chat List
          Expanded(
            child: chatsAsync.when(
              data: (chats) {
                final filteredChats = _filterChats(chats);

                if (filteredChats.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(userChatsProvider);
                  },
                  child: ListView.builder(
                    itemCount: filteredChats.length,
                    itemBuilder: (context, index) {
                      final chat = filteredChats[index];
                      return _buildChatTile(
                        context,
                        chat,
                        currentUser?.uid ?? '',
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    Text('Error loading chats: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.invalidate(userChatsProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.primaryBlue.withOpacity(0.05),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickActionCard(
              '🆘 Support',
              'Get help instantly',
              AppColors.error,
              () => _startSupportChat(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickActionCard(
              '🛒 Seller',
              'Contact a seller',
              AppColors.accentGreen,
              () => _navigateToNewChat(context, ChatType.userSeller),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickActionCard(
              '🚗 Rider',
              'Contact your rider',
              AppColors.info,
              () => _navigateToNewChat(context, ChatType.userRider),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            ),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(fontSize: 10, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search messages...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
              });
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildChatTile(BuildContext context, Chat chat, String currentUserId) {
    final unreadCount = chat.getUnreadCount(currentUserId);

    return FutureBuilder<List<ChatParticipant>>(
      future: ChatService.getChatParticipants(chat.id),
      builder: (context, snapshot) {
        final participants = snapshot.data ?? [];
        final chatTitle = chat.getChatTitle(currentUserId, participants);
        final chatSubtitle = chat.getChatSubtitle();

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            leading: _buildChatAvatar(chat, participants, currentUserId),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    chatTitle,
                    style: TextStyle(
                      fontWeight: unreadCount > 0
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ),
                if (chat.lastMessageTime != null)
                  Text(
                    _formatTime(chat.lastMessageTime!),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (chatSubtitle.isNotEmpty)
                  Text(
                    chatSubtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        chat.lastMessage.isEmpty
                            ? 'No messages yet'
                            : chat.lastMessage,
                        style: TextStyle(
                          color: unreadCount > 0
                              ? Colors.black87
                              : Colors.grey[600],
                          fontWeight: unreadCount > 0
                              ? FontWeight.w500
                              : FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (unreadCount > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            onTap: () => _openChat(context, chat),
            onLongPress: () => _showChatOptions(context, chat),
          ),
        );
      },
    );
  }

  Widget _buildChatAvatar(
    Chat chat,
    List<ChatParticipant> participants,
    String currentUserId,
  ) {
    if (participants.isEmpty) {
      return CircleAvatar(
        backgroundColor: _getChatTypeColor(chat.chatType),
        child: Icon(_getChatTypeIcon(chat.chatType), color: Colors.white),
      );
    }

    // Find the other participant (not current user)
    final otherParticipant = participants.firstWhere(
      (p) => p.id != currentUserId,
      orElse: () => participants.first,
    );

    return Stack(
      children: [
        CircleAvatar(
          backgroundColor: _getChatTypeColor(chat.chatType),
          backgroundImage: otherParticipant.avatar != null
              ? NetworkImage(otherParticipant.avatar!)
              : null,
          child: otherParticipant.avatar == null
              ? Icon(_getChatTypeIcon(chat.chatType), color: Colors.white)
              : null,
        ),
        if (otherParticipant.isOnline)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: AppColors.success,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No messages yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a conversation with sellers, riders, or support',
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToNewChat(context),
            icon: const Icon(Icons.add_comment),
            label: const Text('Start New Chat'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  List<Chat> _filterChats(List<Chat> chats) {
    if (_searchQuery.isEmpty) return chats;

    return chats.where((chat) {
      return chat.lastMessage.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          chat
              .getChatTitle('', [])
              .toLowerCase()
              .contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Color _getChatTypeColor(ChatType type) {
    switch (type) {
      case ChatType.support:
        return AppColors.error;
      case ChatType.userSeller:
        return AppColors.accentGreen;
      case ChatType.userRider:
        return AppColors.info;
      case ChatType.sellerRider:
        return AppColors.warning;
      case ChatType.groupOrder:
        return AppColors.primaryBlue;
      default:
        return Colors.grey;
    }
  }

  IconData _getChatTypeIcon(ChatType type) {
    switch (type) {
      case ChatType.support:
        return Icons.support_agent;
      case ChatType.userSeller:
        return Icons.store;
      case ChatType.userRider:
        return Icons.delivery_dining;
      case ChatType.sellerRider:
        return Icons.local_shipping;
      case ChatType.groupOrder:
        return Icons.group;
      default:
        return Icons.chat;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${time.day}/${time.month}';
    }
  }

  void _openChat(BuildContext context, Chat chat) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ChatDetailPage(chat: chat)),
    );
    AnalyticsService.logEvent('chat_opened', {
      'chat_type': chat.chatType.toString(),
      'chat_id': chat.id,
    });
  }

  void _navigateToNewChat(BuildContext context, [ChatType? chatType]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewChatPage(initialChatType: chatType),
      ),
    );
  }

  void _startSupportChat(BuildContext context) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final chatId = await ChatService.getOrCreateChat(
        otherUserId: 'support_agent_1',
        chatType: ChatType.support,
      );

      final chat = Chat(
        id: chatId,
        participants: [currentUser.uid, 'support_agent_1'],
        chatType: ChatType.support,
        metadata: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastMessage: '',
        lastMessageTime: DateTime.now(),
        unreadCounts: {},
        lastMessageSender: '',
        isActive: true,
      );

      _openChat(context, chat);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error starting support chat: $e')),
      );
    }
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Messages'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Enter search term...',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
              });
              Navigator.pop(context);
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showChatOptions(BuildContext context, Chat chat) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.mark_chat_read),
            title: const Text('Mark as Read'),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications_off),
            title: const Text('Mute Notifications'),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text(
              'Delete Chat',
              style: TextStyle(color: Colors.red),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}
