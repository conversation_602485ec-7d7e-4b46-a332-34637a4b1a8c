/* Admin Login Styles */

.admin-login-body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
}

/* Background Animation */
.login-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  bottom: 10%;
  right: 10%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Login Container */
.login-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  gap: 3rem;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  width: 100%;
  max-width: 380px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9933 0%, #ffffff 50%, #138808 100%);
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.admin-logo {
  margin-bottom: 1rem;
}

.admin-logo img {
  height: 50px;
  width: auto;
}

.login-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c2c54;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
}

/* Form Styles */
.login-form {
  position: relative;
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #2c2c54;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.form-group label .material-icons {
  font-size: 1.2rem;
  color: #667eea;
}

.form-group input {
  width: 100%;
  padding: 1rem 0;
  border: none;
  border-bottom: 2px solid #e0e0e0;
  background: transparent;
  font-size: 1rem;
  font-family: 'Poppins', sans-serif;
  color: #2c2c54;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-bottom-color: #667eea;
}

.form-group input::placeholder {
  color: #999;
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.form-group input:focus + .input-underline {
  width: 100%;
}

/* Password Input */
.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.85rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #666;
}

.remember-me input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Login Button */
.login-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn.loading {
  pointer-events: none;
}

.login-btn.loading .btn-text {
  opacity: 0;
}

.login-btn.loading .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Login Footer */
.login-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.login-footer p {
  color: #666;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  color: #138808;
  background: rgba(19, 136, 8, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.badge .material-icons {
  font-size: 0.9rem;
}

/* System Status */
.system-status {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2rem;
  width: 250px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.system-status h3 {
  color: #2c2c54;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  text-align: center;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #666;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.online {
  background: #138808;
  box-shadow: 0 0 10px rgba(19, 136, 8, 0.5);
}

.status-indicator.warning {
  background: #ff9933;
  box-shadow: 0 0 10px rgba(255, 153, 51, 0.5);
}

.status-indicator.offline {
  background: #dc3545;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: none;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}

.loading-overlay.active {
  display: flex;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* Modals */
.error-modal,
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-modal.active,
.success-modal.active {
  display: flex;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
  margin-bottom: 1.5rem;
}

.error-icon {
  font-size: 3rem;
  color: #dc3545;
  margin-bottom: 1rem;
}

.success-icon {
  font-size: 3rem;
  color: #138808;
  margin-bottom: 1rem;
}

.modal-header h3 {
  color: #2c2c54;
  margin: 0;
}

.modal-body p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  width: 0;
  animation: progressFill 3s ease-in-out forwards;
}

@keyframes progressFill {
  to { width: 100%; }
}

/* Demo Login Section */
.demo-login-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.demo-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.demo-header h4 {
  color: #2c2c54;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.demo-header p {
  color: #666;
  font-size: 0.8rem;
  margin: 0;
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-account {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-account:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.demo-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.demo-info {
  flex: 1;
}

.demo-info h5 {
  color: #2c2c54;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.demo-info p {
  color: #667eea;
  margin: 0 0 0.25rem 0;
  font-size: 0.8rem;
  font-weight: 500;
}

.demo-info small {
  color: #999;
  font-size: 0.7rem;
}

.demo-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

/* Enhanced Quick Access Buttons */
.quick-access-section {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: rgba(40, 167, 69, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(40, 167, 69, 0.1);
  text-align: center;
}

.quick-access-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.quick-btn.dashboard {
  background: #28a745;
  color: white;
}

.quick-btn.dashboard:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.quick-btn.auto-login {
  background: #007bff;
  color: white;
}

.quick-btn.auto-login:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Floating Action Button Enhancement */
.floating-dashboard-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-dashboard-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    gap: 2rem;
  }

  .system-status {
    width: 100%;
    max-width: 450px;
  }

  .login-card {
    padding: 2rem;
  }

  .security-badges {
    flex-direction: column;
    gap: 0.5rem;
  }

  .quick-access-buttons {
    flex-direction: column;
    align-items: center;
  }

  .demo-account {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
  }

  .demo-info {
    text-align: center;
  }
}
