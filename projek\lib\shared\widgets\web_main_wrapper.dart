import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/utils/app_router.dart';
import '../../core/utils/responsive_layout.dart';
import '../../features/cart/presentation/providers/cart_provider.dart';
import '../../features/wishlist/presentation/providers/wishlist_provider.dart';
import '../../features/wallet/presentation/providers/wallet_provider.dart';

class WebMainWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const WebMainWrapper({super.key, required this.child});

  @override
  ConsumerState<WebMainWrapper> createState() => _WebMainWrapperState();
}

class _WebMainWrapperState extends ConsumerState<WebMainWrapper> {
  int _selectedIndex = 0;

  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      route: AppRoutes.home,
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Home',
    ),
    NavigationItem(
      route: AppRoutes.categories,
      icon: Icons.grid_view_outlined,
      selectedIcon: Icons.grid_view,
      label: 'Categories',
    ),
    NavigationItem(
      route: AppRoutes.cart,
      icon: Icons.shopping_cart_outlined,
      selectedIcon: Icons.shopping_cart,
      label: 'Cart',
    ),
    NavigationItem(
      route: AppRoutes.wishlist,
      icon: Icons.favorite_border,
      selectedIcon: Icons.favorite,
      label: 'Wishlist',
    ),
    NavigationItem(
      route: AppRoutes.wallet,
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet,
      label: 'Wallet',
    ),
    NavigationItem(
      route: AppRoutes.profile,
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final cartItemCount = ref.watch(cartItemCountProvider);
    final wishlistItemCount = ref.watch(wishlistItemCountProvider);
    final walletBalance = ref.watch(walletBalanceProvider);

    return ResponsiveBuilder(
      builder: (context, isMobile, isTablet, isDesktop) {
        if (isDesktop) {
          return _buildDesktopLayout(cartItemCount, wishlistItemCount, walletBalance);
        } else if (isTablet) {
          return _buildTabletLayout(cartItemCount, wishlistItemCount, walletBalance);
        } else {
          return _buildMobileLayout(cartItemCount, wishlistItemCount, walletBalance);
        }
      },
    );
  }

  Widget _buildDesktopLayout(int cartItemCount, int wishlistItemCount, double walletBalance) {
    return Scaffold(
      body: Row(
        children: [
          // Sidebar Navigation
          Container(
            width: 280,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Column(
              children: [
                // Logo and Title
                Container(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    children: [
                      Icon(
                        Icons.shopping_bag,
                        size: 32,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Projek',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                // Navigation Items
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: _navigationItems.length,
                    itemBuilder: (context, index) {
                      final item = _navigationItems[index];
                      final isSelected = _selectedIndex == index;
                      
                      return _buildSidebarItem(
                        item,
                        isSelected,
                        cartItemCount,
                        wishlistItemCount,
                        walletBalance,
                      );
                    },
                  ),
                ),
                // Help Center Button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: OutlinedButton.icon(
                    onPressed: () => context.push('/help-center'),
                    icon: const Icon(Icons.help_center),
                    label: const Text('Help Center'),
                    style: OutlinedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Main Content
          Expanded(
            child: widget.child,
          ),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(int cartItemCount, int wishlistItemCount, double walletBalance) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: NavigationBar(
        selectedIndex: _selectedIndex,
        onDestinationSelected: _onDestinationSelected,
        destinations: _buildNavigationDestinations(cartItemCount, wishlistItemCount, walletBalance),
      ),
    );
  }

  Widget _buildMobileLayout(int cartItemCount, int wishlistItemCount, double walletBalance) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: NavigationBar(
        selectedIndex: _selectedIndex,
        onDestinationSelected: _onDestinationSelected,
        destinations: _buildNavigationDestinations(cartItemCount, wishlistItemCount, walletBalance),
      ),
    );
  }

  Widget _buildSidebarItem(
    NavigationItem item,
    bool isSelected,
    int cartItemCount,
    int wishlistItemCount,
    double walletBalance,
  ) {
    Widget icon = Icon(isSelected ? item.selectedIcon : item.icon);

    // Add badges
    if (item.route == AppRoutes.cart && cartItemCount > 0) {
      icon = Badge(
        label: Text(cartItemCount.toString()),
        child: icon,
      );
    } else if (item.route == AppRoutes.wishlist && wishlistItemCount > 0) {
      icon = Badge(
        label: Text(wishlistItemCount.toString()),
        child: icon,
      );
    } else if (item.route == AppRoutes.wallet && walletBalance > 0) {
      final balanceText = walletBalance >= 1000
          ? '${(walletBalance / 1000).toStringAsFixed(1)}K'
          : walletBalance.toStringAsFixed(0);
      icon = Badge(
        label: Text(balanceText),
        backgroundColor: Colors.green,
        child: icon,
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: ListTile(
        leading: icon,
        title: Text(item.label),
        selected: isSelected,
        onTap: () => _onItemTap(item.route, _navigationItems.indexOf(item)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  List<NavigationDestination> _buildNavigationDestinations(
    int cartItemCount,
    int wishlistItemCount,
    double walletBalance,
  ) {
    return _navigationItems.map((item) {
      Widget icon = Icon(item.icon);
      Widget selectedIcon = Icon(item.selectedIcon);

      // Add badges
      if (item.route == AppRoutes.cart && cartItemCount > 0) {
        icon = Badge(
          label: Text(cartItemCount.toString()),
          child: Icon(item.icon),
        );
        selectedIcon = Badge(
          label: Text(cartItemCount.toString()),
          child: Icon(item.selectedIcon),
        );
      } else if (item.route == AppRoutes.wishlist && wishlistItemCount > 0) {
        icon = Badge(
          label: Text(wishlistItemCount.toString()),
          child: Icon(item.icon),
        );
        selectedIcon = Badge(
          label: Text(wishlistItemCount.toString()),
          child: Icon(item.selectedIcon),
        );
      } else if (item.route == AppRoutes.wallet && walletBalance > 0) {
        final balanceText = walletBalance >= 1000
            ? '${(walletBalance / 1000).toStringAsFixed(1)}K'
            : walletBalance.toStringAsFixed(0);
        icon = Badge(
          label: Text(balanceText),
          backgroundColor: Colors.green,
          child: Icon(item.icon),
        );
        selectedIcon = Badge(
          label: Text(balanceText),
          backgroundColor: Colors.green,
          child: Icon(item.selectedIcon),
        );
      }

      return NavigationDestination(
        icon: icon,
        selectedIcon: selectedIcon,
        label: item.label,
      );
    }).toList();
  }

  void _onDestinationSelected(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  void _onItemTap(String route, int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(route);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final String location = GoRouterState.of(context).uri.path;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        if (_selectedIndex != i) {
          setState(() {
            _selectedIndex = i;
          });
        }
        break;
      }
    }
  }
}

class NavigationItem {
  final String route;
  final IconData icon;
  final IconData selectedIcon;
  final String label;

  const NavigationItem({
    required this.route,
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}
