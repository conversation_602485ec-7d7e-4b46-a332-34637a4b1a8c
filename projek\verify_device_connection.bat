@echo off
title Device Connection Verification
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                DEVICE CONNECTION VERIFICATION               ║
echo ║              Pre-Build Connection Testing                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Step 1: Checking ADB service...
adb version
if %errorlevel% neq 0 (
    echo ❌ ADB not found! Please install Android SDK Platform Tools
    pause
    exit /b 1
)
echo ✅ ADB service available
echo.

echo 📱 Step 2: Scanning for devices...
adb devices
echo.

echo 🎯 Step 3: Testing specific device connection...
echo Testing device: V2130 (1397182984001HG)
adb -s 1397182984001HG shell echo "Connection test successful"
if %errorlevel% neq 0 (
    echo ❌ ERROR: Cannot connect to target device!
    echo.
    echo TROUBLESHOOTING STEPS:
    echo.
    echo 1. USB DEBUGGING:
    echo    • Go to Settings → About phone
    echo    • Tap Build number 7 times
    echo    • Go to Settings → Developer options
    echo    • Enable USB debugging
    echo.
    echo 2. USB CONNECTION:
    echo    • Use a data cable (not charging-only)
    echo    • Try different USB ports
    echo    • Select "File Transfer" mode on device
    echo.
    echo 3. AUTHORIZATION:
    echo    • Allow USB debugging when prompted
    echo    • Check "Always allow from this computer"
    echo.
    echo 4. RESET ADB:
    echo    • Run: adb kill-server
    echo    • Run: adb start-server
    echo.
    pause
    exit /b 1
)
echo ✅ Device connection verified!
echo.

echo 📊 Step 4: Device information...
echo Device Model:
adb -s 1397182984001HG shell getprop ro.product.model
echo Android Version:
adb -s 1397182984001HG shell getprop ro.build.version.release
echo API Level:
adb -s 1397182984001HG shell getprop ro.build.version.sdk
echo.

echo 🔧 Step 5: Flutter device recognition...
flutter devices | findstr 1397182984001HG
if %errorlevel% neq 0 (
    echo ❌ WARNING: Flutter doesn't recognize the device
    echo This might still work, but consider restarting Flutter tools
) else (
    echo ✅ Flutter recognizes the device
)
echo.

echo 📦 Step 6: Checking existing app installation...
adb -s 1397182984001HG shell pm list packages | findstr com.projek.user
if %errorlevel% neq 0 (
    echo ℹ️  No existing Projek User app found (fresh install)
) else (
    echo ℹ️  Existing Projek User app found (will be updated)
)
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    CONNECTION STATUS: READY                 ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  ✅ ADB service working                                     ║
echo ║  ✅ Device V2130 connected and responsive                   ║
echo ║  ✅ USB debugging enabled and authorized                    ║
echo ║  ✅ Device ready for APK installation                       ║
echo ║                                                              ║
echo ║  🚀 READY TO BUILD AND DEPLOY!                             ║
echo ║                                                              ║
echo ║  Next steps:                                                 ║
echo ║    • Run: .\build_and_deploy_user.bat                      ║
echo ║    • Or: .\quick_deploy_user.bat                           ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Press any key to continue...
pause >nul
