@echo off
echo ========================================
echo Firebase Auto Setup for Projek Chat App
echo ========================================
echo.

echo Step 1: Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo Please install Node.js from https://nodejs.org
    echo Then run this script again.
    pause
    exit /b 1
)
echo ✅ Node.js is installed

echo.
echo Step 2: Installing Firebase CLI...
npm install -g firebase-tools
if %errorlevel% neq 0 (
    echo ❌ Failed to install Firebase CLI
    echo Try running as Administrator
    pause
    exit /b 1
)
echo ✅ Firebase CLI installed

echo.
echo Step 3: Checking Firebase CLI installation...
firebase --version
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI not working properly
    pause
    exit /b 1
)
echo ✅ Firebase CLI is working

echo.
echo Step 4: Firebase Login...
echo Opening browser for Firebase login...
firebase login
if %errorlevel% neq 0 (
    echo ❌ Firebase login failed
    pause
    exit /b 1
)
echo ✅ Firebase login successful

echo.
echo Step 5: Initializing Firebase in project...
echo.
echo IMPORTANT: When prompted, select:
echo - Firestore Database
echo - Authentication
echo - Use existing project (select your project)
echo - Use default settings for Firestore
echo.
pause
firebase init
if %errorlevel% neq 0 (
    echo ❌ Firebase initialization failed
    pause
    exit /b 1
)
echo ✅ Firebase initialized

echo.
echo Step 6: Deploying Firestore security rules...
firebase deploy --only firestore:rules
if %errorlevel% neq 0 (
    echo ❌ Failed to deploy security rules
    echo You can deploy them manually later
)
echo ✅ Security rules deployed

echo.
echo Step 7: Testing Flutter app...
flutter doctor
flutter pub get
echo.
echo ========================================
echo 🎉 FIREBASE SETUP COMPLETE!
echo ========================================
echo.
echo Your Firebase chat app is ready!
echo.
echo Next steps:
echo 1. Run: flutter run
echo 2. Test Google Sign-In
echo 3. Test chat functionality
echo 4. Test support chat
echo.
echo If you need to configure Google Sign-In:
echo 1. Go to Firebase Console
echo 2. Authentication → Sign-in method
echo 3. Enable Google Sign-In
echo 4. Add your app's SHA-1 fingerprint
echo.
pause
