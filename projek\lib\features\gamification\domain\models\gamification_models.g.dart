// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gamification_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AchievementAdapter extends TypeAdapter<Achievement> {
  @override
  final int typeId = 93;

  @override
  Achievement read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Achievement(
      id: fields[0] as String,
      type: fields[1] as AchievementType,
      title: fields[2] as String,
      description: fields[3] as String,
      icon: fields[4] as String,
      rewardAmount: fields[5] as double,
      targetValue: fields[6] as int,
      currentProgress: fields[7] as int,
      isUnlocked: fields[8] as bool,
      isClaimed: fields[9] as bool,
      unlockedAt: fields[10] as DateTime?,
      claimedAt: fields[11] as DateTime?,
      metadata: (fields[12] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Achievement obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.icon)
      ..writeByte(5)
      ..write(obj.rewardAmount)
      ..writeByte(6)
      ..write(obj.targetValue)
      ..writeByte(7)
      ..write(obj.currentProgress)
      ..writeByte(8)
      ..write(obj.isUnlocked)
      ..writeByte(9)
      ..write(obj.isClaimed)
      ..writeByte(10)
      ..write(obj.unlockedAt)
      ..writeByte(11)
      ..write(obj.claimedAt)
      ..writeByte(12)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AchievementAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LeaderboardEntryAdapter extends TypeAdapter<LeaderboardEntry> {
  @override
  final int typeId = 95;

  @override
  LeaderboardEntry read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LeaderboardEntry(
      userId: fields[0] as String,
      username: fields[1] as String,
      avatarUrl: fields[2] as String?,
      rank: fields[3] as int,
      score: fields[4] as int,
      tier: fields[5] as LoyaltyTier,
      stats: (fields[6] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, LeaderboardEntry obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.username)
      ..writeByte(2)
      ..write(obj.avatarUrl)
      ..writeByte(3)
      ..write(obj.rank)
      ..writeByte(4)
      ..write(obj.score)
      ..writeByte(5)
      ..write(obj.tier)
      ..writeByte(6)
      ..write(obj.stats);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LeaderboardEntryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReferralProgramAdapter extends TypeAdapter<ReferralProgram> {
  @override
  final int typeId = 96;

  @override
  ReferralProgram read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReferralProgram(
      userId: fields[0] as String,
      referralCode: fields[1] as String,
      totalReferrals: fields[2] as int,
      successfulReferrals: fields[3] as int,
      totalEarnings: fields[4] as double,
      referrals: (fields[5] as List).cast<ReferralEntry>(),
      createdAt: fields[6] as DateTime,
      updatedAt: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ReferralProgram obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.referralCode)
      ..writeByte(2)
      ..write(obj.totalReferrals)
      ..writeByte(3)
      ..write(obj.successfulReferrals)
      ..writeByte(4)
      ..write(obj.totalEarnings)
      ..writeByte(5)
      ..write(obj.referrals)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReferralProgramAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReferralEntryAdapter extends TypeAdapter<ReferralEntry> {
  @override
  final int typeId = 97;

  @override
  ReferralEntry read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReferralEntry(
      referredUserId: fields[0] as String,
      referredUsername: fields[1] as String,
      referredAt: fields[2] as DateTime,
      isSuccessful: fields[3] as bool,
      rewardEarned: fields[4] as double,
      rewardClaimedAt: fields[5] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, ReferralEntry obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.referredUserId)
      ..writeByte(1)
      ..write(obj.referredUsername)
      ..writeByte(2)
      ..write(obj.referredAt)
      ..writeByte(3)
      ..write(obj.isSuccessful)
      ..writeByte(4)
      ..write(obj.rewardEarned)
      ..writeByte(5)
      ..write(obj.rewardClaimedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReferralEntryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GameSessionAdapter extends TypeAdapter<GameSession> {
  @override
  final int typeId = 98;

  @override
  GameSession read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GameSession(
      id: fields[0] as String,
      userId: fields[1] as String,
      gameType: fields[2] as GameType,
      entryFee: fields[3] as double,
      winAmount: fields[4] as double,
      isWin: fields[5] as bool,
      playedAt: fields[6] as DateTime,
      gameData: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, GameSession obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.gameType)
      ..writeByte(3)
      ..write(obj.entryFee)
      ..writeByte(4)
      ..write(obj.winAmount)
      ..writeByte(5)
      ..write(obj.isWin)
      ..writeByte(6)
      ..write(obj.playedAt)
      ..writeByte(7)
      ..write(obj.gameData);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GameSessionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AchievementTypeAdapter extends TypeAdapter<AchievementType> {
  @override
  final int typeId = 90;

  @override
  AchievementType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AchievementType.firstOrder;
      case 1:
        return AchievementType.orderMilestone;
      case 2:
        return AchievementType.spendingMilestone;
      case 3:
        return AchievementType.referralMilestone;
      case 4:
        return AchievementType.reviewMilestone;
      case 5:
        return AchievementType.loginStreak;
      case 6:
        return AchievementType.gameWins;
      case 7:
        return AchievementType.socialSharing;
      case 8:
        return AchievementType.profileCompletion;
      case 9:
        return AchievementType.loyaltyTier;
      case 10:
        return AchievementType.specialEvent;
      default:
        return AchievementType.firstOrder;
    }
  }

  @override
  void write(BinaryWriter writer, AchievementType obj) {
    switch (obj) {
      case AchievementType.firstOrder:
        writer.writeByte(0);
        break;
      case AchievementType.orderMilestone:
        writer.writeByte(1);
        break;
      case AchievementType.spendingMilestone:
        writer.writeByte(2);
        break;
      case AchievementType.referralMilestone:
        writer.writeByte(3);
        break;
      case AchievementType.reviewMilestone:
        writer.writeByte(4);
        break;
      case AchievementType.loginStreak:
        writer.writeByte(5);
        break;
      case AchievementType.gameWins:
        writer.writeByte(6);
        break;
      case AchievementType.socialSharing:
        writer.writeByte(7);
        break;
      case AchievementType.profileCompletion:
        writer.writeByte(8);
        break;
      case AchievementType.loyaltyTier:
        writer.writeByte(9);
        break;
      case AchievementType.specialEvent:
        writer.writeByte(10);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AchievementTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoyaltyTierAdapter extends TypeAdapter<LoyaltyTier> {
  @override
  final int typeId = 91;

  @override
  LoyaltyTier read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LoyaltyTier.bronze;
      case 1:
        return LoyaltyTier.silver;
      case 2:
        return LoyaltyTier.gold;
      case 3:
        return LoyaltyTier.platinum;
      case 4:
        return LoyaltyTier.diamond;
      default:
        return LoyaltyTier.bronze;
    }
  }

  @override
  void write(BinaryWriter writer, LoyaltyTier obj) {
    switch (obj) {
      case LoyaltyTier.bronze:
        writer.writeByte(0);
        break;
      case LoyaltyTier.silver:
        writer.writeByte(1);
        break;
      case LoyaltyTier.gold:
        writer.writeByte(2);
        break;
      case LoyaltyTier.platinum:
        writer.writeByte(3);
        break;
      case LoyaltyTier.diamond:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoyaltyTierAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GameTypeAdapter extends TypeAdapter<GameType> {
  @override
  final int typeId = 92;

  @override
  GameType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return GameType.spinWheel;
      case 1:
        return GameType.dailyCheckin;
      case 2:
        return GameType.scratchCard;
      case 3:
        return GameType.quiz;
      case 4:
        return GameType.treasure;
      case 5:
        return GameType.roulette;
      case 6:
        return GameType.slotMachine;
      default:
        return GameType.spinWheel;
    }
  }

  @override
  void write(BinaryWriter writer, GameType obj) {
    switch (obj) {
      case GameType.spinWheel:
        writer.writeByte(0);
        break;
      case GameType.dailyCheckin:
        writer.writeByte(1);
        break;
      case GameType.scratchCard:
        writer.writeByte(2);
        break;
      case GameType.quiz:
        writer.writeByte(3);
        break;
      case GameType.treasure:
        writer.writeByte(4);
        break;
      case GameType.roulette:
        writer.writeByte(5);
        break;
      case GameType.slotMachine:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GameTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
      id: json['id'] as String,
      type: $enumDecode(_$AchievementTypeEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      rewardAmount: (json['rewardAmount'] as num).toDouble(),
      targetValue: (json['targetValue'] as num).toInt(),
      currentProgress: (json['currentProgress'] as num?)?.toInt() ?? 0,
      isUnlocked: json['isUnlocked'] as bool? ?? false,
      isClaimed: json['isClaimed'] as bool? ?? false,
      unlockedAt: json['unlockedAt'] == null
          ? null
          : DateTime.parse(json['unlockedAt'] as String),
      claimedAt: json['claimedAt'] == null
          ? null
          : DateTime.parse(json['claimedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AchievementTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'icon': instance.icon,
      'rewardAmount': instance.rewardAmount,
      'targetValue': instance.targetValue,
      'currentProgress': instance.currentProgress,
      'isUnlocked': instance.isUnlocked,
      'isClaimed': instance.isClaimed,
      'unlockedAt': instance.unlockedAt?.toIso8601String(),
      'claimedAt': instance.claimedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$AchievementTypeEnumMap = {
  AchievementType.firstOrder: 'firstOrder',
  AchievementType.orderMilestone: 'orderMilestone',
  AchievementType.spendingMilestone: 'spendingMilestone',
  AchievementType.referralMilestone: 'referralMilestone',
  AchievementType.reviewMilestone: 'reviewMilestone',
  AchievementType.loginStreak: 'loginStreak',
  AchievementType.gameWins: 'gameWins',
  AchievementType.socialSharing: 'socialSharing',
  AchievementType.profileCompletion: 'profileCompletion',
  AchievementType.loyaltyTier: 'loyaltyTier',
  AchievementType.specialEvent: 'specialEvent',
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      userId: json['userId'] as String,
      username: json['username'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      loyaltyTier:
          $enumDecodeNullable(_$LoyaltyTierEnumMap, json['loyaltyTier']) ??
              LoyaltyTier.bronze,
      totalPoints: (json['totalPoints'] as num?)?.toInt() ?? 0,
      level: (json['level'] as num?)?.toInt() ?? 1,
      experience: (json['experience'] as num?)?.toInt() ?? 0,
      experienceToNextLevel:
          (json['experienceToNextLevel'] as num?)?.toInt() ?? 100,
      totalOrders: (json['totalOrders'] as num?)?.toInt() ?? 0,
      totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
      referralCount: (json['referralCount'] as num?)?.toInt() ?? 0,
      loginStreak: (json['loginStreak'] as num?)?.toInt() ?? 0,
      maxLoginStreak: (json['maxLoginStreak'] as num?)?.toInt() ?? 0,
      lastLoginDate: DateTime.parse(json['lastLoginDate'] as String),
      unlockedAchievements: (json['unlockedAchievements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      stats: json['stats'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'avatarUrl': instance.avatarUrl,
      'loyaltyTier': _$LoyaltyTierEnumMap[instance.loyaltyTier]!,
      'totalPoints': instance.totalPoints,
      'level': instance.level,
      'experience': instance.experience,
      'experienceToNextLevel': instance.experienceToNextLevel,
      'totalOrders': instance.totalOrders,
      'totalSpent': instance.totalSpent,
      'referralCount': instance.referralCount,
      'loginStreak': instance.loginStreak,
      'maxLoginStreak': instance.maxLoginStreak,
      'lastLoginDate': instance.lastLoginDate.toIso8601String(),
      'unlockedAchievements': instance.unlockedAchievements,
      'stats': instance.stats,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$LoyaltyTierEnumMap = {
  LoyaltyTier.bronze: 'bronze',
  LoyaltyTier.silver: 'silver',
  LoyaltyTier.gold: 'gold',
  LoyaltyTier.platinum: 'platinum',
  LoyaltyTier.diamond: 'diamond',
};

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) =>
    LeaderboardEntry(
      userId: json['userId'] as String,
      username: json['username'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      rank: (json['rank'] as num).toInt(),
      score: (json['score'] as num).toInt(),
      tier: $enumDecode(_$LoyaltyTierEnumMap, json['tier']),
      stats: json['stats'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$LeaderboardEntryToJson(LeaderboardEntry instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'avatarUrl': instance.avatarUrl,
      'rank': instance.rank,
      'score': instance.score,
      'tier': _$LoyaltyTierEnumMap[instance.tier]!,
      'stats': instance.stats,
    };

ReferralProgram _$ReferralProgramFromJson(Map<String, dynamic> json) =>
    ReferralProgram(
      userId: json['userId'] as String,
      referralCode: json['referralCode'] as String,
      totalReferrals: (json['totalReferrals'] as num?)?.toInt() ?? 0,
      successfulReferrals: (json['successfulReferrals'] as num?)?.toInt() ?? 0,
      totalEarnings: (json['totalEarnings'] as num?)?.toDouble() ?? 0.0,
      referrals: (json['referrals'] as List<dynamic>?)
              ?.map((e) => ReferralEntry.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ReferralProgramToJson(ReferralProgram instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'referralCode': instance.referralCode,
      'totalReferrals': instance.totalReferrals,
      'successfulReferrals': instance.successfulReferrals,
      'totalEarnings': instance.totalEarnings,
      'referrals': instance.referrals,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

ReferralEntry _$ReferralEntryFromJson(Map<String, dynamic> json) =>
    ReferralEntry(
      referredUserId: json['referredUserId'] as String,
      referredUsername: json['referredUsername'] as String,
      referredAt: DateTime.parse(json['referredAt'] as String),
      isSuccessful: json['isSuccessful'] as bool? ?? false,
      rewardEarned: (json['rewardEarned'] as num?)?.toDouble() ?? 0.0,
      rewardClaimedAt: json['rewardClaimedAt'] == null
          ? null
          : DateTime.parse(json['rewardClaimedAt'] as String),
    );

Map<String, dynamic> _$ReferralEntryToJson(ReferralEntry instance) =>
    <String, dynamic>{
      'referredUserId': instance.referredUserId,
      'referredUsername': instance.referredUsername,
      'referredAt': instance.referredAt.toIso8601String(),
      'isSuccessful': instance.isSuccessful,
      'rewardEarned': instance.rewardEarned,
      'rewardClaimedAt': instance.rewardClaimedAt?.toIso8601String(),
    };

GameSession _$GameSessionFromJson(Map<String, dynamic> json) => GameSession(
      id: json['id'] as String,
      userId: json['userId'] as String,
      gameType: $enumDecode(_$GameTypeEnumMap, json['gameType']),
      entryFee: (json['entryFee'] as num).toDouble(),
      winAmount: (json['winAmount'] as num).toDouble(),
      isWin: json['isWin'] as bool,
      playedAt: DateTime.parse(json['playedAt'] as String),
      gameData: json['gameData'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$GameSessionToJson(GameSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'gameType': _$GameTypeEnumMap[instance.gameType]!,
      'entryFee': instance.entryFee,
      'winAmount': instance.winAmount,
      'isWin': instance.isWin,
      'playedAt': instance.playedAt.toIso8601String(),
      'gameData': instance.gameData,
    };

const _$GameTypeEnumMap = {
  GameType.spinWheel: 'spinWheel',
  GameType.dailyCheckin: 'dailyCheckin',
  GameType.scratchCard: 'scratchCard',
  GameType.quiz: 'quiz',
  GameType.treasure: 'treasure',
  GameType.roulette: 'roulette',
  GameType.slotMachine: 'slotMachine',
};
