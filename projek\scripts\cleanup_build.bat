@echo off
REM Script to clean up unnecessary build artifacts and cache files
echo Cleaning up build artifacts and cache files...
echo.

REM Clean Flutter build cache
echo Cleaning Flutter build cache...
flutter clean

REM Remove Gradle cache (if exists)
if exist "android\.gradle" (
    echo Removing Gradle cache...
    rmdir /s /q "android\.gradle"
    echo Gradle cache removed.
)

REM Remove Android build intermediates (keep outputs)
if exist "android\app\build\intermediates" (
    echo Removing Android build intermediates...
    rmdir /s /q "android\app\build\intermediates"
    echo Android build intermediates removed.
)

REM Remove temporary build files
if exist "android\app\build\tmp" (
    echo Removing temporary build files...
    rmdir /s /q "android\app\build\tmp"
    echo Temporary build files removed.
)

REM Remove Kotlin compilation cache
if exist "android\app\build\kotlin" (
    echo Removing Kotlin compilation cache...
    rmdir /s /q "android\app\build\kotlin"
    echo Kotlin compilation cache removed.
)

REM Clean pub cache
echo Cleaning pub cache...
flutter pub cache clean

echo.
echo Cleanup completed!
echo.
echo Preserved directories:
echo - android\app\build\outputs\ (APK files and essential outputs)
echo - build\android\outputs\ (consolidated APK location)
echo.
echo You can now run a fresh build using:
echo - scripts\dev_build.bat (for debug APK)
echo - scripts\prod_build.bat (for release APK)
echo.
pause
