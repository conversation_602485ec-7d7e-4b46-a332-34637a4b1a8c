// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_order.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationPointAdapter extends TypeAdapter<LocationPoint> {
  @override
  final int typeId = 53;

  @override
  LocationPoint read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationPoint(
      latitude: fields[0] as double,
      longitude: fields[1] as double,
      address: fields[2] as String?,
      landmark: fields[3] as String?,
      timestamp: fields[4] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, LocationPoint obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude)
      ..writeByte(2)
      ..write(obj.address)
      ..writeByte(3)
      ..write(obj.landmark)
      ..writeByte(4)
      ..write(obj.timestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationPointAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderMilestoneAdapter extends TypeAdapter<OrderMilestone> {
  @override
  final int typeId = 54;

  @override
  OrderMilestone read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OrderMilestone(
      id: fields[0] as String,
      status: fields[1] as OrderStatus,
      timestamp: fields[2] as DateTime,
      description: fields[3] as String?,
      performedBy: fields[4] as String?,
      location: fields[5] as LocationPoint?,
      metadata: (fields[6] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, OrderMilestone obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.status)
      ..writeByte(2)
      ..write(obj.timestamp)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.performedBy)
      ..writeByte(5)
      ..write(obj.location)
      ..writeByte(6)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderMilestoneAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DeliveryTrackingAdapter extends TypeAdapter<DeliveryTracking> {
  @override
  final int typeId = 55;

  @override
  DeliveryTracking read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DeliveryTracking(
      riderId: fields[0] as String?,
      riderName: fields[1] as String?,
      riderPhone: fields[2] as String?,
      vehicleNumber: fields[3] as String?,
      currentLocation: fields[4] as LocationPoint?,
      route: (fields[5] as List).cast<LocationPoint>(),
      estimatedDeliveryTime: fields[6] as DateTime?,
      distanceRemaining: fields[7] as double?,
      timeRemaining: fields[8] as int?,
      deliveryType: fields[9] as DeliveryType,
      additionalInfo: (fields[10] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, DeliveryTracking obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.riderId)
      ..writeByte(1)
      ..write(obj.riderName)
      ..writeByte(2)
      ..write(obj.riderPhone)
      ..writeByte(3)
      ..write(obj.vehicleNumber)
      ..writeByte(4)
      ..write(obj.currentLocation)
      ..writeByte(5)
      ..write(obj.route)
      ..writeByte(6)
      ..write(obj.estimatedDeliveryTime)
      ..writeByte(7)
      ..write(obj.distanceRemaining)
      ..writeByte(8)
      ..write(obj.timeRemaining)
      ..writeByte(9)
      ..write(obj.deliveryType)
      ..writeByte(10)
      ..write(obj.additionalInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeliveryTrackingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderParticipantAdapter extends TypeAdapter<OrderParticipant> {
  @override
  final int typeId = 56;

  @override
  OrderParticipant read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OrderParticipant(
      id: fields[0] as String,
      name: fields[1] as String,
      phone: fields[2] as String?,
      email: fields[3] as String?,
      imageUrl: fields[4] as String?,
      role: fields[5] as String,
      rating: fields[6] as double?,
      additionalInfo: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, OrderParticipant obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.phone)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.imageUrl)
      ..writeByte(5)
      ..write(obj.role)
      ..writeByte(6)
      ..write(obj.rating)
      ..writeByte(7)
      ..write(obj.additionalInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderParticipantAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UnifiedOrderAdapter extends TypeAdapter<UnifiedOrder> {
  @override
  final int typeId = 57;

  @override
  UnifiedOrder read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UnifiedOrder(
      id: fields[0] as String,
      orderNumber: fields[1] as String,
      type: fields[2] as OrderType,
      status: fields[3] as OrderStatus,
      user: fields[4] as OrderParticipant,
      seller: fields[5] as OrderParticipant,
      rider: fields[6] as OrderParticipant?,
      pickupLocation: fields[7] as LocationPoint,
      deliveryLocation: fields[8] as LocationPoint,
      tracking: fields[9] as DeliveryTracking?,
      milestones: (fields[10] as List).cast<OrderMilestone>(),
      totalAmount: fields[11] as double,
      deliveryFee: fields[12] as double,
      taxes: fields[13] as double,
      currency: fields[14] as String,
      paymentId: fields[15] as String?,
      paymentStatus: fields[16] as String,
      createdAt: fields[17] as DateTime,
      updatedAt: fields[18] as DateTime,
      scheduledDeliveryTime: fields[19] as DateTime?,
      specialInstructions: fields[20] as String?,
      tags: (fields[21] as List).cast<String>(),
      priority: fields[22] as int,
      orderData: (fields[23] as Map).cast<String, dynamic>(),
      metadata: (fields[24] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, UnifiedOrder obj) {
    writer
      ..writeByte(25)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.orderNumber)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.user)
      ..writeByte(5)
      ..write(obj.seller)
      ..writeByte(6)
      ..write(obj.rider)
      ..writeByte(7)
      ..write(obj.pickupLocation)
      ..writeByte(8)
      ..write(obj.deliveryLocation)
      ..writeByte(9)
      ..write(obj.tracking)
      ..writeByte(10)
      ..write(obj.milestones)
      ..writeByte(11)
      ..write(obj.totalAmount)
      ..writeByte(12)
      ..write(obj.deliveryFee)
      ..writeByte(13)
      ..write(obj.taxes)
      ..writeByte(14)
      ..write(obj.currency)
      ..writeByte(15)
      ..write(obj.paymentId)
      ..writeByte(16)
      ..write(obj.paymentStatus)
      ..writeByte(17)
      ..write(obj.createdAt)
      ..writeByte(18)
      ..write(obj.updatedAt)
      ..writeByte(19)
      ..write(obj.scheduledDeliveryTime)
      ..writeByte(20)
      ..write(obj.specialInstructions)
      ..writeByte(21)
      ..write(obj.tags)
      ..writeByte(22)
      ..write(obj.priority)
      ..writeByte(23)
      ..write(obj.orderData)
      ..writeByte(24)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnifiedOrderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderTypeAdapter extends TypeAdapter<OrderType> {
  @override
  final int typeId = 50;

  @override
  OrderType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OrderType.marketplace;
      case 1:
        return OrderType.service;
      case 2:
        return OrderType.food;
      case 3:
        return OrderType.grocery;
      case 4:
        return OrderType.medicine;
      case 5:
        return OrderType.emergency;
      default:
        return OrderType.marketplace;
    }
  }

  @override
  void write(BinaryWriter writer, OrderType obj) {
    switch (obj) {
      case OrderType.marketplace:
        writer.writeByte(0);
        break;
      case OrderType.service:
        writer.writeByte(1);
        break;
      case OrderType.food:
        writer.writeByte(2);
        break;
      case OrderType.grocery:
        writer.writeByte(3);
        break;
      case OrderType.medicine:
        writer.writeByte(4);
        break;
      case OrderType.emergency:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderStatusAdapter extends TypeAdapter<OrderStatus> {
  @override
  final int typeId = 51;

  @override
  OrderStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OrderStatus.created;
      case 1:
        return OrderStatus.confirmed;
      case 2:
        return OrderStatus.preparing;
      case 3:
        return OrderStatus.ready;
      case 4:
        return OrderStatus.assigned;
      case 5:
        return OrderStatus.pickedUp;
      case 6:
        return OrderStatus.inTransit;
      case 7:
        return OrderStatus.nearDestination;
      case 8:
        return OrderStatus.delivered;
      case 9:
        return OrderStatus.completed;
      case 10:
        return OrderStatus.cancelled;
      case 11:
        return OrderStatus.failed;
      case 12:
        return OrderStatus.refunded;
      default:
        return OrderStatus.created;
    }
  }

  @override
  void write(BinaryWriter writer, OrderStatus obj) {
    switch (obj) {
      case OrderStatus.created:
        writer.writeByte(0);
        break;
      case OrderStatus.confirmed:
        writer.writeByte(1);
        break;
      case OrderStatus.preparing:
        writer.writeByte(2);
        break;
      case OrderStatus.ready:
        writer.writeByte(3);
        break;
      case OrderStatus.assigned:
        writer.writeByte(4);
        break;
      case OrderStatus.pickedUp:
        writer.writeByte(5);
        break;
      case OrderStatus.inTransit:
        writer.writeByte(6);
        break;
      case OrderStatus.nearDestination:
        writer.writeByte(7);
        break;
      case OrderStatus.delivered:
        writer.writeByte(8);
        break;
      case OrderStatus.completed:
        writer.writeByte(9);
        break;
      case OrderStatus.cancelled:
        writer.writeByte(10);
        break;
      case OrderStatus.failed:
        writer.writeByte(11);
        break;
      case OrderStatus.refunded:
        writer.writeByte(12);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DeliveryTypeAdapter extends TypeAdapter<DeliveryType> {
  @override
  final int typeId = 52;

  @override
  DeliveryType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DeliveryType.standard;
      case 1:
        return DeliveryType.express;
      case 2:
        return DeliveryType.scheduled;
      case 3:
        return DeliveryType.emergency;
      case 4:
        return DeliveryType.contactless;
      default:
        return DeliveryType.standard;
    }
  }

  @override
  void write(BinaryWriter writer, DeliveryType obj) {
    switch (obj) {
      case DeliveryType.standard:
        writer.writeByte(0);
        break;
      case DeliveryType.express:
        writer.writeByte(1);
        break;
      case DeliveryType.scheduled:
        writer.writeByte(2);
        break;
      case DeliveryType.emergency:
        writer.writeByte(3);
        break;
      case DeliveryType.contactless:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeliveryTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationPoint _$LocationPointFromJson(Map<String, dynamic> json) =>
    LocationPoint(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String?,
      landmark: json['landmark'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$LocationPointToJson(LocationPoint instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'landmark': instance.landmark,
      'timestamp': instance.timestamp.toIso8601String(),
    };

OrderMilestone _$OrderMilestoneFromJson(Map<String, dynamic> json) =>
    OrderMilestone(
      id: json['id'] as String,
      status: $enumDecode(_$OrderStatusEnumMap, json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      description: json['description'] as String?,
      performedBy: json['performedBy'] as String?,
      location: json['location'] == null
          ? null
          : LocationPoint.fromJson(json['location'] as Map<String, dynamic>),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$OrderMilestoneToJson(OrderMilestone instance) =>
    <String, dynamic>{
      'id': instance.id,
      'status': _$OrderStatusEnumMap[instance.status]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'description': instance.description,
      'performedBy': instance.performedBy,
      'location': instance.location,
      'metadata': instance.metadata,
    };

const _$OrderStatusEnumMap = {
  OrderStatus.created: 'created',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.preparing: 'preparing',
  OrderStatus.ready: 'ready',
  OrderStatus.assigned: 'assigned',
  OrderStatus.pickedUp: 'pickedUp',
  OrderStatus.inTransit: 'inTransit',
  OrderStatus.nearDestination: 'nearDestination',
  OrderStatus.delivered: 'delivered',
  OrderStatus.completed: 'completed',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.failed: 'failed',
  OrderStatus.refunded: 'refunded',
};

DeliveryTracking _$DeliveryTrackingFromJson(Map<String, dynamic> json) =>
    DeliveryTracking(
      riderId: json['riderId'] as String?,
      riderName: json['riderName'] as String?,
      riderPhone: json['riderPhone'] as String?,
      vehicleNumber: json['vehicleNumber'] as String?,
      currentLocation: json['currentLocation'] == null
          ? null
          : LocationPoint.fromJson(
              json['currentLocation'] as Map<String, dynamic>),
      route: (json['route'] as List<dynamic>?)
              ?.map((e) => LocationPoint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      estimatedDeliveryTime: json['estimatedDeliveryTime'] == null
          ? null
          : DateTime.parse(json['estimatedDeliveryTime'] as String),
      distanceRemaining: (json['distanceRemaining'] as num?)?.toDouble(),
      timeRemaining: (json['timeRemaining'] as num?)?.toInt(),
      deliveryType:
          $enumDecodeNullable(_$DeliveryTypeEnumMap, json['deliveryType']) ??
              DeliveryType.standard,
      additionalInfo:
          json['additionalInfo'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$DeliveryTrackingToJson(DeliveryTracking instance) =>
    <String, dynamic>{
      'riderId': instance.riderId,
      'riderName': instance.riderName,
      'riderPhone': instance.riderPhone,
      'vehicleNumber': instance.vehicleNumber,
      'currentLocation': instance.currentLocation,
      'route': instance.route,
      'estimatedDeliveryTime':
          instance.estimatedDeliveryTime?.toIso8601String(),
      'distanceRemaining': instance.distanceRemaining,
      'timeRemaining': instance.timeRemaining,
      'deliveryType': _$DeliveryTypeEnumMap[instance.deliveryType]!,
      'additionalInfo': instance.additionalInfo,
    };

const _$DeliveryTypeEnumMap = {
  DeliveryType.standard: 'standard',
  DeliveryType.express: 'express',
  DeliveryType.scheduled: 'scheduled',
  DeliveryType.emergency: 'emergency',
  DeliveryType.contactless: 'contactless',
};

OrderParticipant _$OrderParticipantFromJson(Map<String, dynamic> json) =>
    OrderParticipant(
      id: json['id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      imageUrl: json['imageUrl'] as String?,
      role: json['role'] as String,
      rating: (json['rating'] as num?)?.toDouble(),
      additionalInfo:
          json['additionalInfo'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$OrderParticipantToJson(OrderParticipant instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
      'imageUrl': instance.imageUrl,
      'role': instance.role,
      'rating': instance.rating,
      'additionalInfo': instance.additionalInfo,
    };

UnifiedOrder _$UnifiedOrderFromJson(Map<String, dynamic> json) => UnifiedOrder(
      id: json['id'] as String,
      orderNumber: json['orderNumber'] as String,
      type: $enumDecode(_$OrderTypeEnumMap, json['type']),
      status: $enumDecode(_$OrderStatusEnumMap, json['status']),
      user: OrderParticipant.fromJson(json['user'] as Map<String, dynamic>),
      seller: OrderParticipant.fromJson(json['seller'] as Map<String, dynamic>),
      rider: json['rider'] == null
          ? null
          : OrderParticipant.fromJson(json['rider'] as Map<String, dynamic>),
      pickupLocation: LocationPoint.fromJson(
          json['pickupLocation'] as Map<String, dynamic>),
      deliveryLocation: LocationPoint.fromJson(
          json['deliveryLocation'] as Map<String, dynamic>),
      tracking: json['tracking'] == null
          ? null
          : DeliveryTracking.fromJson(json['tracking'] as Map<String, dynamic>),
      milestones: (json['milestones'] as List<dynamic>?)
              ?.map((e) => OrderMilestone.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalAmount: (json['totalAmount'] as num).toDouble(),
      deliveryFee: (json['deliveryFee'] as num?)?.toDouble() ?? 0.0,
      taxes: (json['taxes'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'INR',
      paymentId: json['paymentId'] as String?,
      paymentStatus: json['paymentStatus'] as String? ?? 'pending',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      scheduledDeliveryTime: json['scheduledDeliveryTime'] == null
          ? null
          : DateTime.parse(json['scheduledDeliveryTime'] as String),
      specialInstructions: json['specialInstructions'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      priority: (json['priority'] as num?)?.toInt() ?? 3,
      orderData: json['orderData'] as Map<String, dynamic>? ?? const {},
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UnifiedOrderToJson(UnifiedOrder instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderNumber': instance.orderNumber,
      'type': _$OrderTypeEnumMap[instance.type]!,
      'status': _$OrderStatusEnumMap[instance.status]!,
      'user': instance.user,
      'seller': instance.seller,
      'rider': instance.rider,
      'pickupLocation': instance.pickupLocation,
      'deliveryLocation': instance.deliveryLocation,
      'tracking': instance.tracking,
      'milestones': instance.milestones,
      'totalAmount': instance.totalAmount,
      'deliveryFee': instance.deliveryFee,
      'taxes': instance.taxes,
      'currency': instance.currency,
      'paymentId': instance.paymentId,
      'paymentStatus': instance.paymentStatus,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'scheduledDeliveryTime':
          instance.scheduledDeliveryTime?.toIso8601String(),
      'specialInstructions': instance.specialInstructions,
      'tags': instance.tags,
      'priority': instance.priority,
      'orderData': instance.orderData,
      'metadata': instance.metadata,
    };

const _$OrderTypeEnumMap = {
  OrderType.marketplace: 'marketplace',
  OrderType.service: 'service',
  OrderType.food: 'food',
  OrderType.grocery: 'grocery',
  OrderType.medicine: 'medicine',
  OrderType.emergency: 'emergency',
};
