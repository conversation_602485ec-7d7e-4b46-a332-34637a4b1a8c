import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class WorkingWallet extends Equatable {
  final String userId;
  final double projekCoinBalance;
  final double inrBalance;
  final double rewardsBalance;
  final double cashbackBalance;
  final bool isActive;
  final bool isVerified;
  final DateTime lastUpdated;
  final Map<String, dynamic> settings;
  final Map<String, dynamic> limits;

  const WorkingWallet({
    required this.userId,
    this.projekCoinBalance = 0.0,
    this.inrBalance = 0.0,
    this.rewardsBalance = 0.0,
    this.cashbackBalance = 0.0,
    this.isActive = true,
    this.isVerified = false,
    required this.lastUpdated,
    this.settings = const {},
    this.limits = const {},
  });

  factory WorkingWallet.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkingWallet(
      userId: doc.id,
      projekCoinBalance: (data['projekCoinBalance'] ?? 0.0).toDouble(),
      inrBalance: (data['inrBalance'] ?? 0.0).toDouble(),
      rewardsBalance: (data['rewardsBalance'] ?? 0.0).toDouble(),
      cashbackBalance: (data['cashbackBalance'] ?? 0.0).toDouble(),
      isActive: data['isActive'] ?? true,
      isVerified: data['isVerified'] ?? false,
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      settings: Map<String, dynamic>.from(data['settings'] ?? {}),
      limits: Map<String, dynamic>.from(data['limits'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'projekCoinBalance': projekCoinBalance,
      'inrBalance': inrBalance,
      'rewardsBalance': rewardsBalance,
      'cashbackBalance': cashbackBalance,
      'isActive': isActive,
      'isVerified': isVerified,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'settings': settings,
      'limits': limits,
    };
  }

  // Formatted getters for UI display
  String get formattedTotalBalance {
    final total =
        inrBalance + projekCoinBalance + rewardsBalance + cashbackBalance;
    return '₹${total.toStringAsFixed(2)}';
  }

  String get formattedProjekCoinBalance {
    return '${projekCoinBalance.toStringAsFixed(0)} PC';
  }

  String get formattedInrBalance {
    return '₹${inrBalance.toStringAsFixed(2)}';
  }

  String get formattedRewardsBalance {
    return '₹${rewardsBalance.toStringAsFixed(2)}';
  }

  String get formattedCashbackBalance {
    return '₹${cashbackBalance.toStringAsFixed(2)}';
  }

  /// Copy with new values
  WorkingWallet copyWith({
    String? userId,
    double? projekCoinBalance,
    double? inrBalance,
    double? rewardsBalance,
    double? cashbackBalance,
    bool? isActive,
    bool? isVerified,
    DateTime? lastUpdated,
    Map<String, dynamic>? settings,
    Map<String, double>? limits,
  }) {
    return WorkingWallet(
      userId: userId ?? this.userId,
      projekCoinBalance: projekCoinBalance ?? this.projekCoinBalance,
      inrBalance: inrBalance ?? this.inrBalance,
      rewardsBalance: rewardsBalance ?? this.rewardsBalance,
      cashbackBalance: cashbackBalance ?? this.cashbackBalance,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      settings: settings ?? this.settings,
      limits: limits ?? this.limits,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'projekCoinBalance': projekCoinBalance,
      'inrBalance': inrBalance,
      'rewardsBalance': rewardsBalance,
      'cashbackBalance': cashbackBalance,
      'isActive': isActive,
      'isVerified': isVerified,
      'lastUpdated': lastUpdated.toIso8601String(),
      'settings': settings,
      'limits': limits,
    };
  }

  /// Create from JSON
  factory WorkingWallet.fromJson(Map<String, dynamic> json) {
    return WorkingWallet(
      userId: json['userId'] ?? '',
      projekCoinBalance: (json['projekCoinBalance'] ?? 0.0).toDouble(),
      inrBalance: (json['inrBalance'] ?? 0.0).toDouble(),
      rewardsBalance: (json['rewardsBalance'] ?? 0.0).toDouble(),
      cashbackBalance: (json['cashbackBalance'] ?? 0.0).toDouble(),
      isActive: json['isActive'] ?? true,
      isVerified: json['isVerified'] ?? false,
      lastUpdated: DateTime.parse(
        json['lastUpdated'] ?? DateTime.now().toIso8601String(),
      ),
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
      limits: Map<String, double>.from(json['limits'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [
    userId,
    projekCoinBalance,
    inrBalance,
    rewardsBalance,
    cashbackBalance,
    isActive,
    isVerified,
    lastUpdated,
    settings,
    limits,
  ];
}

/// Working Transaction Model for Projek Super App
class WorkingTransaction extends Equatable {
  final String id;
  final String userId;
  final String type;
  final double amount;
  final String description;
  final DateTime timestamp;
  final String status;
  final String? recipientId;
  final String? recipientName;
  final Map<String, dynamic> metadata;

  const WorkingTransaction({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    required this.status,
    this.recipientId,
    this.recipientName,
    this.metadata = const {},
  });

  /// Create from Firestore document
  factory WorkingTransaction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkingTransaction(
      id: doc.id,
      userId: data['userId'] ?? '',
      type: data['type'] ?? '',
      amount: (data['amount'] ?? 0.0).toDouble(),
      description: data['description'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      status: data['status'] ?? '',
      recipientId: data['recipientId'],
      recipientName: data['recipientName'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'type': type,
      'amount': amount,
      'description': description,
      'timestamp': Timestamp.fromDate(timestamp),
      'status': status,
      'recipientId': recipientId,
      'recipientName': recipientName,
      'metadata': metadata,
    };
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type,
      'amount': amount,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'status': status,
      'recipientId': recipientId,
      'recipientName': recipientName,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory WorkingTransaction.fromJson(Map<String, dynamic> json) {
    return WorkingTransaction(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      type: json['type'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      status: json['status'] ?? '',
      recipientId: json['recipientId'],
      recipientName: json['recipientName'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Formatted amount for display
  String get formattedAmount {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Check if transaction is credit (money coming in)
  bool get isCredit {
    return ['topup', 'reward', 'cashback', 'refund'].contains(type);
  }

  /// Check if transaction is completed
  bool get isCompleted {
    return status == 'completed';
  }

  /// Check if transaction is pending
  bool get isPending {
    return status == 'pending';
  }

  /// Check if transaction is failed
  bool get isFailed {
    return status == 'failed';
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    type,
    amount,
    description,
    timestamp,
    status,
    recipientId,
    recipientName,
    metadata,
  ];

  @override
  String toString() {
    return 'WorkingTransaction(id: $id, type: $type, amount: $amount, status: $status)';
  }
}

/// Payment Method Model for Projek Super App
class PaymentMethod extends Equatable {
  final String id;
  final String type; // 'card', 'upi', 'wallet', 'netbanking', 'cod'
  final String name;
  final String? cardNumber; // Last 4 digits for cards
  final String? upiId;
  final String? bankName;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastUsedAt;
  final Map<String, dynamic> metadata;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.name,
    this.cardNumber,
    this.upiId,
    this.bankName,
    this.isDefault = false,
    this.isActive = true,
    required this.createdAt,
    this.lastUsedAt,
    this.metadata = const {},
  });

  /// Create from Firestore document
  factory PaymentMethod.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PaymentMethod(
      id: doc.id,
      type: data['type'] ?? '',
      name: data['name'] ?? '',
      cardNumber: data['cardNumber'],
      upiId: data['upiId'],
      bankName: data['bankName'],
      isDefault: data['isDefault'] ?? false,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastUsedAt: data['lastUsedAt'] != null
          ? (data['lastUsedAt'] as Timestamp).toDate()
          : null,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'type': type,
      'name': name,
      'cardNumber': cardNumber,
      'upiId': upiId,
      'bankName': bankName,
      'isDefault': isDefault,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastUsedAt': lastUsedAt != null ? Timestamp.fromDate(lastUsedAt!) : null,
      'metadata': metadata,
    };
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'cardNumber': cardNumber,
      'upiId': upiId,
      'bankName': bankName,
      'isDefault': isDefault,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastUsedAt': lastUsedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      name: json['name'] ?? '',
      cardNumber: json['cardNumber'],
      upiId: json['upiId'],
      bankName: json['bankName'],
      isDefault: json['isDefault'] ?? false,
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      lastUsedAt: json['lastUsedAt'] != null
          ? DateTime.parse(json['lastUsedAt'])
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Get display name for UI
  String get displayName {
    switch (type) {
      case 'card':
        return '$name ****${cardNumber ?? ''}';
      case 'upi':
        return upiId ?? name;
      case 'wallet':
        return 'Projek Wallet';
      case 'netbanking':
        return bankName ?? name;
      case 'cod':
        return 'Cash on Delivery';
      default:
        return name;
    }
  }

  /// Get icon for payment method
  String get iconName {
    switch (type) {
      case 'card':
        return 'credit_card';
      case 'upi':
        return 'account_balance';
      case 'wallet':
        return 'account_balance_wallet';
      case 'netbanking':
        return 'account_balance';
      case 'cod':
        return 'money';
      default:
        return 'payment';
    }
  }

  /// Copy with new values
  PaymentMethod copyWith({
    String? id,
    String? type,
    String? name,
    String? cardNumber,
    String? upiId,
    String? bankName,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      cardNumber: cardNumber ?? this.cardNumber,
      upiId: upiId ?? this.upiId,
      bankName: bankName ?? this.bankName,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
    id,
    type,
    name,
    cardNumber,
    upiId,
    bankName,
    isDefault,
    isActive,
    createdAt,
    lastUsedAt,
    metadata,
  ];

  @override
  String toString() {
    return 'PaymentMethod(id: $id, type: $type, name: $name)';
  }
}
