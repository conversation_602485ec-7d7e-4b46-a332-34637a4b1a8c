// Enhanced Admin Dashboard JavaScript - PROJEK Super Vision
document.addEventListener('DOMContentLoaded', function() {

  // Check authentication
  checkAuthentication();

  // Initialize enhanced dashboard
  initializeDashboard();

  // Initialize complete analytics
  initializeCompleteAnalytics();

  // Initialize real-time updates
  initializeRealTimeUpdates();

  // Initialize GPS tracking
  initializeGPSTracking();

  // Initialize booking reports
  initializeBookingReports();

  // Initialize enhanced features
  initializeUserManagement();
  initializeGamingControls();
  initializeWalletOversight();

  // Initialize profile management
  initializeProfileManagement();

  // Initialize navigation
  initializeNavigation();
  
  function checkAuthentication() {
    const isLoggedIn = sessionStorage.getItem('adminLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
      window.location.href = 'login.html';
      return;
    }
    
    // Check session expiry (24 hours)
    const loginTime = new Date(sessionStorage.getItem('loginTime'));
    const now = new Date();
    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
    
    if (hoursDiff > 24) {
      sessionStorage.clear();
      window.location.href = 'login.html';
      return;
    }
  }
  
  function initializeDashboard() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    logoutBtn.addEventListener('click', function() {
      if (confirm('Are you sure you want to logout?')) {
        sessionStorage.clear();
        window.location.href = 'login.html';
      }
    });
    
    // Sidebar toggle for mobile
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.admin-sidebar');
    
    sidebarToggle.addEventListener('click', function() {
      sidebar.classList.toggle('open');
    });
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
      if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
          sidebar.classList.remove('open');
        }
      }
    });
    
    // Animate stats cards on load
    animateStatsCards();
    
    // Initialize quick actions
    initializeQuickActions();
  }
  
  function animateStatsCards() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
      setTimeout(() => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, 100);
      }, index * 200);
    });
    
    // Animate numbers
    setTimeout(() => {
      animateNumbers();
    }, 1000);
  }
  
  function animateNumbers() {
    const numberElements = [
      { element: document.querySelector('.stat-card.revenue h3'), target: 1245678, prefix: '₹', suffix: '' },
      { element: document.querySelector('.stat-card.orders h3'), target: 8456, prefix: '', suffix: '' },
      { element: document.querySelector('.stat-card.users h3'), target: 45234, prefix: '', suffix: '' },
      { element: document.querySelector('.stat-card.riders h3'), target: 2156, prefix: '', suffix: '' }
    ];
    
    numberElements.forEach(({ element, target, prefix, suffix }) => {
      animateNumber(element, 0, target, 2000, prefix, suffix);
    });
  }
  
  function animateNumber(element, start, end, duration, prefix = '', suffix = '') {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = Math.floor(start + (end - start) * easeOutQuart);
      
      element.textContent = prefix + current.toLocaleString() + suffix;
      
      if (progress < 1) {
        requestAnimationFrame(updateNumber);
      }
    }
    
    requestAnimationFrame(updateNumber);
  }
  
  // Complete Analytics System
  function initializeCompleteAnalytics() {
    // Initialize revenue analytics
    initializeRevenueAnalytics();

    // Initialize user analytics
    initializeUserAnalytics();

    // Initialize transaction analytics
    initializeTransactionAnalytics();

    // Initialize performance metrics
    initializePerformanceMetrics();

    // Initialize real-time dashboard
    initializeRealTimeDashboard();
  }

  function initializeRevenueAnalytics() {
    // Enhanced Revenue Chart with multiple data sources
    const revenueCtx = document.getElementById('revenueChart')?.getContext('2d');
    if (!revenueCtx) return;

    // Set canvas size explicitly for small container
    revenueCtx.canvas.style.height = '120px';
    revenueCtx.canvas.height = 120;

    const revenueChart = new Chart(revenueCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [
          {
            label: 'Gaming Revenue',
            data: [85000, 92000, 88000, 95000, 102000, 108000],
            borderColor: '#ff9933',
            backgroundColor: 'rgba(255, 153, 51, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          },
          {
            label: 'Wallet Revenue',
            data: [120000, 135000, 128000, 142000, 155000, 168000],
            borderColor: '#146eb4',
            backgroundColor: 'rgba(20, 110, 180, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          },
          {
            label: 'Service Revenue',
            data: [200000, 220000, 210000, 235000, 248000, 265000],
            borderColor: '#138808',
            backgroundColor: 'rgba(19, 136, 8, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString();
              }
            }
          }
        },
        scales: {
          x: {
            grid: { display: false },
            ticks: { font: { size: 10 } }
          },
          y: {
            beginAtZero: true,
            grid: { color: 'rgba(0,0,0,0.1)' },
            ticks: {
              maxTicksLimit: 4,
              font: { size: 10 },
              callback: function(value) {
                return '₹' + (value / 1000) + 'K';
              }
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    });

    // Revenue breakdown chart
    initializeRevenueBreakdown();
  }

  function initializeRevenueBreakdown() {
    const breakdownCtx = document.getElementById('revenueBreakdownChart')?.getContext('2d');
    if (!breakdownCtx) return;

    new Chart(breakdownCtx, {
      type: 'doughnut',
      data: {
        labels: ['Gaming', 'Wallet Fees', 'Service Commissions', 'Delivery Fees', 'Staking Revenue'],
        datasets: [{
          data: [25, 15, 35, 20, 5],
          backgroundColor: [
            '#ff9933',
            '#146eb4',
            '#138808',
            '#e74c3c',
            '#f39c12'
          ],
          borderWidth: 0,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              font: { size: 11 }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return context.label + ': ' + context.parsed + '%';
              }
            }
          }
        }
      }
    });
  }

  function initializeCharts() {
    // Legacy chart initialization for backward compatibility
    const revenueCtx = document.getElementById('revenueChart')?.getContext('2d');
    if (!revenueCtx) return;

    // Set canvas size explicitly
    revenueCtx.canvas.style.height = '120px';
    revenueCtx.canvas.height = 120;

    const revenueChart = new Chart(revenueCtx, {
      type: 'line',
      data: {
        labels: ['W1', 'W2', 'W3', 'W4'],
        datasets: [{
          label: 'Revenue',
          data: [285000, 320000, 298000, 345000],
          borderColor: '#667eea',
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            },
            ticks: {
              font: {
                size: 10
              }
            }
          },
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0,0,0,0.1)'
            },
            ticks: {
              maxTicksLimit: 4,
              font: {
                size: 10
              },
              callback: function(value) {
                return '₹' + (value / 1000) + 'K';
              }
            }
          }
        },
        elements: {
          point: {
            radius: 3
          }
        }
      }
    });
    
    // Orders Chart (Doughnut)
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    const ordersChart = new Chart(ordersCtx, {
      type: 'doughnut',
      data: {
        labels: ['Food Delivery', 'Grocery', 'Services'],
        datasets: [{
          data: [65, 25, 10],
          backgroundColor: ['#ff9933', '#138808', '#667eea'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
    
    // Real-time mini charts
    initializeMiniCharts();
  }
  
  function initializeMiniCharts() {
    const chartConfigs = [
      { id: 'activeOrdersChart', data: [1200, 1180, 1220, 1234, 1210, 1234] },
      { id: 'onlineRidersChart', data: [820, 840, 860, 850, 856, 856] },
      { id: 'deliveryTimeChart', data: [32, 30, 28, 29, 27, 28] },
      { id: 'satisfactionChart', data: [4.6, 4.7, 4.8, 4.7, 4.8, 4.8] }
    ];

    chartConfigs.forEach(config => {
      const ctx = document.getElementById(config.id)?.getContext('2d');
      if (!ctx) return;

      new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['', '', '', '', '', ''],
          datasets: [{
            data: config.data,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: { display: false },
            y: { display: false }
          },
          elements: {
            point: { radius: 0 }
          }
        }
      });
    });
  }

  // User Management System
  function initializeUserManagement() {
    // Initialize user analytics
    initializeUserAnalytics();

    // Initialize user controls
    initializeUserControls();

    // Initialize user search and filters
    initializeUserFilters();

    // Initialize bulk actions
    initializeBulkActions();
  }

  function initializeUserAnalytics() {
    // User growth chart
    const userGrowthCtx = document.getElementById('userGrowthChart')?.getContext('2d');
    if (userGrowthCtx) {
      new Chart(userGrowthCtx, {
        type: 'line',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          datasets: [
            {
              label: 'Total Users',
              data: [12000, 15000, 18500, 22000, 26500, 31000],
              borderColor: '#146eb4',
              backgroundColor: 'rgba(20, 110, 180, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Active Users',
              data: [8500, 11200, 14000, 16800, 20100, 23500],
              borderColor: '#28a745',
              backgroundColor: 'rgba(40, 167, 69, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Premium Users',
              data: [1200, 1800, 2500, 3200, 4100, 5200],
              borderColor: '#ff9933',
              backgroundColor: 'rgba(255, 153, 51, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { usePointStyle: true, padding: 20 }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return (value / 1000) + 'K';
                }
              }
            }
          }
        }
      });
    }

    // User demographics chart
    const demographicsCtx = document.getElementById('userDemographicsChart')?.getContext('2d');
    if (demographicsCtx) {
      new Chart(demographicsCtx, {
        type: 'doughnut',
        data: {
          labels: ['18-25', '26-35', '36-45', '46-55', '55+'],
          datasets: [{
            data: [35, 28, 20, 12, 5],
            backgroundColor: ['#ff9933', '#146eb4', '#28a745', '#e74c3c', '#f39c12'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { usePointStyle: true, padding: 15 }
            }
          }
        }
      });
    }
  }

  function initializeUserControls() {
    // User action buttons
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('user-action-btn')) {
        const action = e.target.dataset.action;
        const userId = e.target.dataset.userId;
        handleUserAction(action, userId);
      }
    });

    // User search
    const userSearchInput = document.getElementById('userSearch');
    if (userSearchInput) {
      userSearchInput.addEventListener('input', function() {
        filterUsers(this.value);
      });
    }

    // User status filter
    const userStatusFilter = document.getElementById('userStatusFilter');
    if (userStatusFilter) {
      userStatusFilter.addEventListener('change', function() {
        filterUsersByStatus(this.value);
      });
    }
  }

  function handleUserAction(action, userId) {
    const actions = {
      'view': () => showUserDetails(userId),
      'edit': () => editUser(userId),
      'suspend': () => suspendUser(userId),
      'activate': () => activateUser(userId),
      'delete': () => deleteUser(userId),
      'message': () => messageUser(userId)
    };

    if (actions[action]) {
      actions[action]();
    }
  }

  function showUserDetails(userId) {
    // Mock user data
    const userData = {
      'U001': {
        name: 'Rajesh Kumar',
        email: '<EMAIL>',
        phone: '+91 98765 43210',
        joinDate: '2024-01-15',
        status: 'Active',
        totalOrders: 45,
        totalSpent: 12500,
        gamingBalance: 250,
        walletBalance: 1200,
        lastLogin: '2024-06-15 14:30'
      }
    };

    const user = userData[userId] || userData['U001'];

    const modal = createModal('User Details', `
      <div class="user-details">
        <div class="user-header">
          <div class="user-avatar">
            <span class="material-icons">person</span>
          </div>
          <div class="user-info">
            <h3>${user.name}</h3>
            <p>ID: ${userId}</p>
            <span class="status-badge ${user.status.toLowerCase()}">${user.status}</span>
          </div>
        </div>

        <div class="user-stats">
          <div class="stat-item">
            <span class="label">Total Orders</span>
            <span class="value">${user.totalOrders}</span>
          </div>
          <div class="stat-item">
            <span class="label">Total Spent</span>
            <span class="value">₹${user.totalSpent.toLocaleString()}</span>
          </div>
          <div class="stat-item">
            <span class="label">Gaming Balance</span>
            <span class="value">${user.gamingBalance} PC</span>
          </div>
          <div class="stat-item">
            <span class="label">Wallet Balance</span>
            <span class="value">₹${user.walletBalance}</span>
          </div>
        </div>

        <div class="user-contact">
          <p><strong>Email:</strong> ${user.email}</p>
          <p><strong>Phone:</strong> ${user.phone}</p>
          <p><strong>Join Date:</strong> ${user.joinDate}</p>
          <p><strong>Last Login:</strong> ${user.lastLogin}</p>
        </div>

        <div class="user-actions">
          <button class="btn btn-primary" onclick="editUser('${userId}')">Edit User</button>
          <button class="btn btn-warning" onclick="suspendUser('${userId}')">Suspend</button>
          <button class="btn btn-info" onclick="messageUser('${userId}')">Send Message</button>
        </div>
      </div>
    `);
  }

  // Gaming Controls System
  function initializeGamingControls() {
    // Initialize gaming analytics
    initializeGamingAnalytics();

    // Initialize gaming monitoring
    initializeGamingMonitoring();

    // Initialize gaming controls
    initializeGamingControlPanel();

    // Initialize fraud detection
    initializeGamingFraudDetection();

    // Initialize demo manual controls
    initializeDemoManualControls();
  }

  function initializeGamingAnalytics() {
    // Gaming revenue chart
    const gamingRevenueCtx = document.getElementById('gamingRevenueChart')?.getContext('2d');
    if (gamingRevenueCtx) {
      new Chart(gamingRevenueCtx, {
        type: 'bar',
        data: {
          labels: ['Spin Games', 'Wheel Games', 'Card Games', 'Lottery', 'Tournaments'],
          datasets: [{
            label: 'Revenue (₹)',
            data: [125000, 98000, 75000, 45000, 32000],
            backgroundColor: [
              'rgba(255, 153, 51, 0.8)',
              'rgba(20, 110, 180, 0.8)',
              'rgba(40, 167, 69, 0.8)',
              'rgba(231, 76, 60, 0.8)',
              'rgba(243, 156, 18, 0.8)'
            ],
            borderColor: [
              '#ff9933',
              '#146eb4',
              '#28a745',
              '#e74c3c',
              '#f39c12'
            ],
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return 'Revenue: ₹' + context.parsed.y.toLocaleString();
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '₹' + (value / 1000) + 'K';
                }
              }
            }
          }
        }
      });
    }

    // Gaming activity chart
    const gamingActivityCtx = document.getElementById('gamingActivityChart')?.getContext('2d');
    if (gamingActivityCtx) {
      new Chart(gamingActivityCtx, {
        type: 'line',
        data: {
          labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
          datasets: [
            {
              label: 'Active Players',
              data: [1200, 800, 1500, 2200, 2800, 3200],
              borderColor: '#ff9933',
              backgroundColor: 'rgba(255, 153, 51, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Games Played',
              data: [450, 320, 680, 920, 1150, 1380],
              borderColor: '#146eb4',
              backgroundColor: 'rgba(20, 110, 180, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { usePointStyle: true, padding: 20 }
            }
          },
          scales: {
            y: { beginAtZero: true }
          }
        }
      });
    }
  }

  function initializeGamingMonitoring() {
    // Real-time gaming metrics
    const gamingMetrics = {
      activePlayers: 2847,
      gamesPlayed: 15623,
      totalWinnings: 2456789,
      houseEdge: 12.5,
      suspiciousActivity: 3
    };

    // Update gaming metrics display
    updateGamingMetrics(gamingMetrics);

    // Monitor for suspicious activity
    setInterval(monitorGamingActivity, 10000);

    // Update gaming leaderboard
    updateGamingLeaderboard();
  }

  function updateGamingLeaderboard() {
    // Mock gaming leaderboard data
    const leaderboardData = [
      { rank: 1, name: 'Rajesh Kumar', wins: 245, earnings: 125000 },
      { rank: 2, name: 'Priya Sharma', wins: 198, earnings: 98500 },
      { rank: 3, name: 'Amit Singh', wins: 176, earnings: 87200 },
      { rank: 4, name: 'Vikram Patel', wins: 154, earnings: 76800 },
      { rank: 5, name: 'Anita Gupta', wins: 132, earnings: 65400 }
    ];

    const leaderboardContainer = document.getElementById('gamingLeaderboard');
    if (leaderboardContainer) {
      leaderboardContainer.innerHTML = leaderboardData.map(player => `
        <div class="leaderboard-item">
          <span class="rank">#${player.rank}</span>
          <span class="name">${player.name}</span>
          <span class="wins">${player.wins} wins</span>
          <span class="earnings">₹${player.earnings.toLocaleString()}</span>
        </div>
      `).join('');
    }
  }

  function initializeGamingControlPanel() {
    // Gaming control actions
    const gamingActions = {
      pauseAllGames: () => {
        if (confirm('Pause all gaming activities? This will stop new games from starting.')) {
          showNotification('All gaming activities paused', 'warning');
        }
      },
      resumeAllGames: () => {
        showNotification('All gaming activities resumed', 'success');
      },
      adjustHouseEdge: () => {
        const newEdge = prompt('Enter new house edge percentage (current: 12.5%):');
        if (newEdge && !isNaN(newEdge) && newEdge >= 0 && newEdge <= 50) {
          showNotification(`House edge updated to ${newEdge}%`, 'info');
        }
      },
      viewSuspiciousActivity: () => {
        showSuspiciousActivityModal();
      }
    };

    // Attach event listeners to gaming control buttons
    Object.entries(gamingActions).forEach(([action, handler]) => {
      const button = document.getElementById(action);
      if (button) {
        button.addEventListener('click', handler);
      }
    });
  }

  function initializeGamingFraudDetection() {
    // Fraud detection patterns
    const fraudPatterns = [
      'Multiple wins from same IP address',
      'Unusual betting patterns detected',
      'Account sharing suspected',
      'Bot-like behavior identified'
    ];

    // Simulate fraud detection alerts
    setInterval(() => {
      if (Math.random() < 0.02) { // 2% chance
        const pattern = fraudPatterns[Math.floor(Math.random() * fraudPatterns.length)];
        showFraudAlert(pattern);
      }
    }, 30000);
  }

  function showFraudAlert(pattern) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fraud-alert';
    alertDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 140px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 300px;
        animation: slideIn 0.3s ease;
      ">
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span class="material-icons">security</span>
          <div style="flex: 1;">
            <strong>Fraud Alert</strong><br>
            <span style="font-size: 0.9rem;">${pattern}</span>
          </div>
          <button onclick="this.closest('.fraud-alert').remove()"
                  style="background: none; border: none; color: white; cursor: pointer;">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 12000);
  }

  function updateGamingMetrics(metrics) {
    const elements = {
      'activePlayersCount': metrics.activePlayers.toLocaleString(),
      'gamesPlayedCount': metrics.gamesPlayed.toLocaleString(),
      'totalWinningsAmount': '₹' + metrics.totalWinnings.toLocaleString(),
      'houseEdgePercent': metrics.houseEdge + '%',
      'suspiciousActivityCount': metrics.suspiciousActivity
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) element.textContent = value;
    });
  }

  function monitorGamingActivity() {
    // Simulate real-time monitoring
    const alerts = [
      'High-frequency betting detected from User #12345',
      'Unusual winning pattern in Spin Game #SG789',
      'Multiple accounts from same IP detected'
    ];

    // Random alert simulation
    if (Math.random() < 0.1) { // 10% chance
      const alert = alerts[Math.floor(Math.random() * alerts.length)];
      showGamingAlert(alert);
    }
  }

  function showGamingAlert(message) {
    const alertContainer = document.getElementById('gamingAlerts');
    if (!alertContainer) return;

    const alertElement = document.createElement('div');
    alertElement.className = 'gaming-alert';
    alertElement.innerHTML = `
      <div class="alert alert-warning">
        <span class="material-icons">warning</span>
        <span>${message}</span>
        <button class="alert-close" onclick="this.parentElement.parentElement.remove()">
          <span class="material-icons">close</span>
        </button>
      </div>
    `;

    alertContainer.appendChild(alertElement);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (alertElement.parentNode) {
        alertElement.remove();
      }
    }, 10000);
  }

  // Wallet Oversight System
  function initializeWalletOversight() {
    // Initialize wallet analytics
    initializeWalletAnalytics();

    // Initialize transaction monitoring
    initializeTransactionMonitoring();

    // Initialize wallet controls
    initializeWalletControls();

    // Initialize compliance monitoring
    initializeComplianceMonitoring();
  }

  function initializeWalletControls() {
    // Wallet control actions
    const walletActions = {
      freezeWallet: () => {
        const userId = prompt('Enter User ID to freeze wallet:');
        if (userId && confirm(`Freeze wallet for User ${userId}?`)) {
          showNotification(`Wallet frozen for User ${userId}`, 'warning');
        }
      },
      approveWithdrawals: () => {
        showPendingWithdrawalsModal();
      },
      adjustExchangeRate: () => {
        const newRate = prompt('Enter new INR to PC exchange rate (current: 1:1):');
        if (newRate && !isNaN(newRate) && newRate > 0) {
          showNotification(`Exchange rate updated to 1 INR = ${newRate} PC`, 'info');
        }
      },
      emergencyStop: () => {
        if (confirm('EMERGENCY STOP: This will halt all wallet operations. Continue?')) {
          showNotification('🚨 EMERGENCY STOP ACTIVATED - All wallet operations suspended!', 'error');
        }
      },
      viewWalletReports: () => {
        showWalletReportsModal();
      }
    };

    // Attach event listeners to wallet control buttons
    Object.entries(walletActions).forEach(([action, handler]) => {
      const button = document.getElementById(action);
      if (button) {
        button.addEventListener('click', handler);
      }
    });
  }

  function initializeComplianceMonitoring() {
    // Compliance monitoring for wallet transactions
    const complianceRules = [
      'Daily withdrawal limit exceeded',
      'KYC verification required for large transaction',
      'Suspicious conversion pattern detected',
      'Multiple accounts linked to same bank account'
    ];

    // Simulate compliance alerts
    setInterval(() => {
      if (Math.random() < 0.03) { // 3% chance
        const rule = complianceRules[Math.floor(Math.random() * complianceRules.length)];
        showComplianceAlert(rule);
      }
    }, 25000);
  }

  function showComplianceAlert(rule) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'compliance-alert';
    alertDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 200px;
        right: 20px;
        background: #f39c12;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 320px;
        animation: slideIn 0.3s ease;
      ">
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span class="material-icons">gavel</span>
          <div style="flex: 1;">
            <strong>Compliance Alert</strong><br>
            <span style="font-size: 0.9rem;">${rule}</span>
          </div>
          <button onclick="this.closest('.compliance-alert').remove()"
                  style="background: none; border: none; color: white; cursor: pointer;">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 15000);
  }

  function initializeWalletAnalytics() {
    // Wallet balance distribution chart
    const walletBalanceCtx = document.getElementById('walletBalanceChart')?.getContext('2d');
    if (walletBalanceCtx) {
      new Chart(walletBalanceCtx, {
        type: 'doughnut',
        data: {
          labels: ['ProjekCoin', 'INR Balance', 'Gaming Balance', 'Staking Balance', 'Rewards'],
          datasets: [{
            data: [45, 25, 15, 10, 5],
            backgroundColor: [
              '#ff9933',
              '#28a745',
              '#146eb4',
              '#f39c12',
              '#e74c3c'
            ],
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { usePointStyle: true, padding: 15 }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.label + ': ' + context.parsed + '%';
                }
              }
            }
          }
        }
      });
    }

    // Transaction volume chart
    const transactionVolumeCtx = document.getElementById('transactionVolumeChart')?.getContext('2d');
    if (transactionVolumeCtx) {
      new Chart(transactionVolumeCtx, {
        type: 'line',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          datasets: [
            {
              label: 'Deposits',
              data: [850000, 920000, 1050000, 1180000, 1320000, 1450000],
              borderColor: '#28a745',
              backgroundColor: 'rgba(40, 167, 69, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Withdrawals',
              data: [650000, 720000, 810000, 890000, 980000, 1080000],
              borderColor: '#e74c3c',
              backgroundColor: 'rgba(231, 76, 60, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            },
            {
              label: 'Gaming Transactions',
              data: [320000, 380000, 420000, 480000, 540000, 620000],
              borderColor: '#ff9933',
              backgroundColor: 'rgba(255, 153, 51, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { usePointStyle: true, padding: 20 }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '₹' + (value / 100000) + 'L';
                }
              }
            }
          }
        }
      });
    }
  }

  function initializeTransactionMonitoring() {
    // Real-time transaction monitoring
    const transactionMetrics = {
      totalTransactions: 156789,
      totalVolume: 45678900,
      pendingTransactions: 234,
      failedTransactions: 12,
      suspiciousTransactions: 5
    };

    updateTransactionMetrics(transactionMetrics);

    // Monitor transactions in real-time
    setInterval(updateTransactionMetrics, 5000);

    // Initialize transaction alerts
    initializeTransactionAlerts();
  }

  function updateTransactionMetrics(metrics) {
    const elements = {
      'totalTransactionsCount': metrics.totalTransactions.toLocaleString(),
      'totalVolumeAmount': '₹' + (metrics.totalVolume / 100000).toFixed(1) + 'L',
      'pendingTransactionsCount': metrics.pendingTransactions,
      'failedTransactionsCount': metrics.failedTransactions,
      'suspiciousTransactionsCount': metrics.suspiciousTransactions
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) element.textContent = value;
    });
  }

  function initializeTransactionAlerts() {
    // Monitor for suspicious transactions
    setInterval(() => {
      if (Math.random() < 0.05) { // 5% chance
        const alerts = [
          'Large withdrawal detected: ₹50,000 by User #U12345',
          'Multiple failed login attempts for wallet access',
          'Unusual staking pattern detected',
          'High-frequency micro-transactions detected'
        ];

        const alert = alerts[Math.floor(Math.random() * alerts.length)];
        showTransactionAlert(alert);
      }
    }, 15000);
  }

  function showTransactionAlert(message) {
    const alertContainer = document.getElementById('transactionAlerts');
    if (!alertContainer) return;

    const alertElement = document.createElement('div');
    alertElement.className = 'transaction-alert';
    alertElement.innerHTML = `
      <div class="alert alert-danger">
        <span class="material-icons">security</span>
        <span>${message}</span>
        <button class="alert-close" onclick="this.parentElement.parentElement.remove()">
          <span class="material-icons">close</span>
        </button>
      </div>
    `;

    alertContainer.appendChild(alertElement);

    // Auto-remove after 15 seconds
    setTimeout(() => {
      if (alertElement.parentNode) {
        alertElement.remove();
      }
    }, 15000);
  }

  // Utility function to create modals
  function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'admin-modal';
    modal.innerHTML = `
      <div class="modal-overlay" onclick="this.parentElement.remove()">
        <div class="modal-content" onclick="event.stopPropagation()">
          <div class="modal-header">
            <h3>${title}</h3>
            <button class="modal-close" onclick="this.closest('.admin-modal').remove()">
              <span class="material-icons">close</span>
            </button>
          </div>
          <div class="modal-body">
            ${content}
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    return modal;
  }

  // Enhanced notification system
  function showNotification(message, type = 'info') {
    const notificationDiv = document.createElement('div');
    notificationDiv.className = 'admin-notification';

    const colors = {
      success: '#28a745',
      error: '#dc3545',
      warning: '#ffc107',
      info: '#17a2b8'
    };

    const icons = {
      success: 'check_circle',
      error: 'error',
      warning: 'warning',
      info: 'info'
    };

    notificationDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10001;
        max-width: 350px;
        animation: slideIn 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      ">
        <span class="material-icons">${icons[type]}</span>
        <span style="flex: 1;">${message}</span>
        <button onclick="this.closest('.admin-notification').remove()"
                style="background: none; border: none; color: white; cursor: pointer; margin-left: 0.5rem;">
          <span class="material-icons">close</span>
        </button>
      </div>
    `;

    document.body.appendChild(notificationDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notificationDiv.parentNode) {
        notificationDiv.remove();
      }
    }, 5000);
  }

  // Modal functions for detailed views
  function showSuspiciousActivityModal() {
    const suspiciousActivities = [
      { user: 'U12345', activity: 'High-frequency betting', risk: 'High', time: '2 mins ago' },
      { user: 'U67890', activity: 'Unusual winning pattern', risk: 'Medium', time: '5 mins ago' },
      { user: 'U54321', activity: 'Multiple accounts same IP', risk: 'High', time: '8 mins ago' },
      { user: 'U98765', activity: 'Bot-like behavior', risk: 'Medium', time: '12 mins ago' }
    ];

    const content = `
      <div class="suspicious-activity-list">
        <h4>Recent Suspicious Gaming Activities</h4>
        ${suspiciousActivities.map(activity => `
          <div class="activity-item" style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 0.5rem;
          ">
            <div>
              <strong>User ${activity.user}</strong><br>
              <span style="color: #666;">${activity.activity}</span>
            </div>
            <div style="text-align: right;">
              <span class="risk-badge ${activity.risk.toLowerCase()}" style="
                padding: 0.25rem 0.5rem;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
                background: ${activity.risk === 'High' ? '#dc3545' : '#ffc107'};
                color: white;
              ">${activity.risk} Risk</span><br>
              <small style="color: #666;">${activity.time}</small>
            </div>
          </div>
        `).join('')}
        <div style="margin-top: 1rem; text-align: center;">
          <button class="btn btn-primary" onclick="alert('Opening detailed fraud investigation tools...')">
            Investigate All
          </button>
        </div>
      </div>
    `;

    createModal('Suspicious Gaming Activities', content);
  }

  function showPendingWithdrawalsModal() {
    const pendingWithdrawals = [
      { id: 'W001', user: 'Rajesh Kumar', amount: 50000, bank: 'HDFC ****1234', time: '2 hours ago' },
      { id: 'W002', user: 'Priya Sharma', amount: 25000, bank: 'SBI ****5678', time: '4 hours ago' },
      { id: 'W003', user: 'Amit Singh', amount: 75000, bank: 'ICICI ****9012', time: '6 hours ago' },
      { id: 'W004', user: 'Vikram Patel', amount: 30000, bank: 'Axis ****3456', time: '8 hours ago' }
    ];

    const content = `
      <div class="pending-withdrawals-list">
        <h4>Pending Withdrawal Approvals</h4>
        ${pendingWithdrawals.map(withdrawal => `
          <div class="withdrawal-item" style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 0.5rem;
          ">
            <div>
              <strong>${withdrawal.user}</strong> (${withdrawal.id})<br>
              <span style="color: #666;">To: ${withdrawal.bank}</span><br>
              <small style="color: #666;">${withdrawal.time}</small>
            </div>
            <div style="text-align: right;">
              <div style="font-size: 1.2rem; font-weight: 700; color: #28a745; margin-bottom: 0.5rem;">
                ₹${withdrawal.amount.toLocaleString()}
              </div>
              <div>
                <button class="btn btn-sm btn-success" onclick="approveWithdrawal('${withdrawal.id}')"
                        style="margin-right: 0.5rem; padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                  Approve
                </button>
                <button class="btn btn-sm btn-danger" onclick="rejectWithdrawal('${withdrawal.id}')"
                        style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                  Reject
                </button>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    createModal('Pending Withdrawals', content);
  }

  function showWalletReportsModal() {
    const content = `
      <div class="wallet-reports">
        <h4>Wallet Transaction Reports</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0;">
          <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
            <h5>Today's Transactions</h5>
            <div style="font-size: 2rem; font-weight: 700; color: #28a745;">15,623</div>
            <small>Volume: ₹45.6L</small>
          </div>
          <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
            <h5>Pending Approvals</h5>
            <div style="font-size: 2rem; font-weight: 700; color: #ffc107;">234</div>
            <small>Value: ₹12.3L</small>
          </div>
        </div>
        <div style="margin-top: 1rem;">
          <button class="btn btn-primary" onclick="alert('Generating detailed wallet report...')"
                  style="width: 100%; margin-bottom: 0.5rem;">
            Generate Detailed Report
          </button>
          <button class="btn btn-secondary" onclick="alert('Exporting transaction data...')"
                  style="width: 100%;">
            Export Transaction Data
          </button>
        </div>
      </div>
    `;

    createModal('Wallet Reports', content);
  }

  // Withdrawal approval functions
  function approveWithdrawal(withdrawalId) {
    if (confirm(`Approve withdrawal ${withdrawalId}?`)) {
      showNotification(`Withdrawal ${withdrawalId} approved successfully`, 'success');
      // Close modal and refresh data
      document.querySelector('.admin-modal')?.remove();
    }
  }

  function rejectWithdrawal(withdrawalId) {
    const reason = prompt('Enter rejection reason:');
    if (reason) {
      showNotification(`Withdrawal ${withdrawalId} rejected: ${reason}`, 'warning');
      // Close modal and refresh data
      document.querySelector('.admin-modal')?.remove();
    }
  }

  // DEMO ONLY: Manual Game Controls
  function initializeDemoManualControls() {
    console.log('⚠️ DEMO ONLY: Initializing Manual Game Controls');

    // Show demo warning
    showDemoWarning();

    // Initialize active games display
    initializeActiveGamesDisplay();

    // Initialize manual control buttons
    initializeManualControlButtons();

    // Initialize player selection controls
    initializePlayerSelectionControls();

    // Initialize audit trail
    initializeAuditTrail();

    // Show disclaimer on first load
    setTimeout(() => {
      showNotification('⚠️ DEMO MODE: Manual controls are for demonstration only!', 'warning');
    }, 2000);
  }

  function showDemoWarning() {
    // Add periodic warning reminders
    setInterval(() => {
      if (Math.random() < 0.1) { // 10% chance every interval
        console.warn('⚠️ DEMO ONLY: These controls should NEVER be used in production!');
      }
    }, 30000);
  }

  function initializeActiveGamesDisplay() {
    // Mock active games data
    const mockActiveGames = [
      {
        id: 'G123',
        type: 'Spin Game',
        players: ['Rajesh Kumar', 'Priya Sharma'],
        status: 'active',
        bet: 500,
        timeRemaining: '45s'
      },
      {
        id: 'G124',
        type: 'Card Game',
        players: ['Amit Singh', 'Vikram Patel'],
        status: 'waiting',
        bet: 1000,
        timeRemaining: '2m 15s'
      },
      {
        id: 'G125',
        type: 'Wheel Game',
        players: ['Anita Gupta', 'Suresh Reddy'],
        status: 'active',
        bet: 750,
        timeRemaining: '1m 30s'
      },
      {
        id: 'G126',
        type: 'Lottery',
        players: ['Deepak Sharma', 'Kavita Patel'],
        status: 'waiting',
        bet: 250,
        timeRemaining: '5m 45s'
      }
    ];

    // Display active games
    updateActiveGamesDisplay(mockActiveGames);

    // Update every 10 seconds
    setInterval(() => {
      updateActiveGamesDisplay(mockActiveGames);
    }, 10000);
  }

  function updateActiveGamesDisplay(games) {
    const container = document.getElementById('activeGamesList');
    if (!container) return;

    container.innerHTML = games.map(game => `
      <div class="active-game-item" data-game-id="${game.id}">
        <div class="game-info">
          <div class="game-id">Game ${game.id} - ${game.type}</div>
          <div class="game-players">Players: ${game.players.join(' vs ')}</div>
          <div class="game-bet">Bet: ₹${game.bet} | Time: ${game.timeRemaining}</div>
          <span class="game-status ${game.status}">${game.status.toUpperCase()}</span>
        </div>
        <div class="game-actions">
          <button class="manual-btn force-win" onclick="forceGameOutcome('${game.id}', 'win')"
                  style="font-size: 0.8rem; padding: 0.5rem;">
            Force Win
          </button>
          <button class="manual-btn force-lose" onclick="forceGameOutcome('${game.id}', 'lose')"
                  style="font-size: 0.8rem; padding: 0.5rem;">
            Force Loss
          </button>
        </div>
      </div>
    `).join('');
  }

  function initializeManualControlButtons() {
    // Force Win Button
    const forceWinBtn = document.getElementById('forceWinBtn');
    if (forceWinBtn) {
      forceWinBtn.addEventListener('click', () => {
        showDemoConfirmation('Force Player Win', () => {
          const player = prompt('Enter player name to force win:');
          if (player) {
            logManualIntervention('Force Win', `Forced win for player: ${player}`, '₹5,000');
            showNotification(`⚠️ DEMO: Forced win for ${player}`, 'warning');
          }
        });
      });
    }

    // Force Lose Button
    const forceLoseBtn = document.getElementById('forceLoseBtn');
    if (forceLoseBtn) {
      forceLoseBtn.addEventListener('click', () => {
        showDemoConfirmation('Force Player Loss', () => {
          const player = prompt('Enter player name to force loss:');
          if (player) {
            logManualIntervention('Force Loss', `Forced loss for player: ${player}`, '₹0');
            showNotification(`⚠️ DEMO: Forced loss for ${player}`, 'warning');
          }
        });
      });
    }

    // Override Result Button
    const overrideResultBtn = document.getElementById('overrideResultBtn');
    if (overrideResultBtn) {
      overrideResultBtn.addEventListener('click', () => {
        showDemoConfirmation('Override Game Result', () => {
          const gameId = prompt('Enter Game ID to override:');
          const result = prompt('Enter new result (win/lose/draw):');
          if (gameId && result) {
            logManualIntervention('Override Result', `Game ${gameId} result changed to: ${result}`, 'Variable');
            showNotification(`⚠️ DEMO: Overrode result for Game ${gameId}`, 'warning');
          }
        });
      });
    }

    // Custom Outcome Button
    const customOutcomeBtn = document.getElementById('customOutcomeBtn');
    if (customOutcomeBtn) {
      customOutcomeBtn.addEventListener('click', () => {
        showDemoConfirmation('Set Custom Outcome', () => {
          const player = prompt('Enter player name:');
          const amount = prompt('Enter custom winning amount (₹):');
          if (player && amount) {
            logManualIntervention('Custom Outcome', `Custom outcome for ${player}`, `₹${amount}`);
            showNotification(`⚠️ DEMO: Set custom outcome ₹${amount} for ${player}`, 'warning');
          }
        });
      });
    }
  }

  function initializePlayerSelectionControls() {
    // Outcome selection change handler
    const outcomeSelect = document.getElementById('gameOutcomeSelect');
    const customAmountInput = document.getElementById('customAmountInput');

    if (outcomeSelect && customAmountInput) {
      outcomeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
          customAmountInput.style.display = 'block';
          customAmountInput.required = true;
        } else {
          customAmountInput.style.display = 'none';
          customAmountInput.required = false;
        }
      });
    }

    // Apply Outcome Button
    const applyOutcomeBtn = document.getElementById('applyOutcomeBtn');
    if (applyOutcomeBtn) {
      applyOutcomeBtn.addEventListener('click', () => {
        const playerSelect = document.getElementById('targetPlayerSelect');
        const outcomeSelect = document.getElementById('gameOutcomeSelect');
        const customAmountInput = document.getElementById('customAmountInput');

        const player = playerSelect?.value;
        const outcome = outcomeSelect?.value;
        const customAmount = customAmountInput?.value;

        if (!player || !outcome) {
          alert('Please select both player and outcome!');
          return;
        }

        showDemoConfirmation(`Apply ${outcome} to ${player}`, () => {
          let details = `Applied ${outcome} to ${player}`;
          let amount = 'Variable';

          if (outcome === 'custom' && customAmount) {
            details += ` with custom amount ₹${customAmount}`;
            amount = `₹${customAmount}`;
          } else if (outcome === 'win') {
            amount = '₹2,500';
          } else if (outcome === 'lose') {
            amount = '₹0';
          }

          logManualIntervention('Player Control', details, amount);
          showNotification(`⚠️ DEMO: ${details}`, 'warning');

          // Reset form
          playerSelect.value = '';
          outcomeSelect.value = '';
          customAmountInput.value = '';
          customAmountInput.style.display = 'none';
        });
      });
    }
  }

  function initializeAuditTrail() {
    // Initialize empty audit trail
    const auditTrail = document.getElementById('auditTrail');
    if (auditTrail) {
      auditTrail.innerHTML = ''; // Start with empty trail
    }
  }

  function logManualIntervention(action, details, amount) {
    const auditTrail = document.getElementById('auditTrail');
    if (!auditTrail) return;

    const timestamp = new Date().toLocaleString();
    const auditEntry = document.createElement('div');
    auditEntry.className = 'audit-entry';
    auditEntry.innerHTML = `
      <div>
        <div class="audit-action">⚠️ DEMO: ${action}</div>
        <div class="audit-details">${details}</div>
        <div class="audit-amount">Amount: ${amount}</div>
      </div>
      <div class="audit-timestamp">${timestamp}</div>
    `;

    // Add to top of audit trail
    auditTrail.insertBefore(auditEntry, auditTrail.firstChild);

    // Keep only last 10 entries
    while (auditTrail.children.length > 10) {
      auditTrail.removeChild(auditTrail.lastChild);
    }

    // Log to console with warning
    console.warn(`⚠️ DEMO INTERVENTION: ${action} - ${details} - ${amount} at ${timestamp}`);
  }

  function showDemoConfirmation(action, callback) {
    const confirmed = confirm(`
⚠️ DEMO ONLY WARNING ⚠️

You are about to perform: ${action}

This is a DEMONSTRATION ONLY and should NEVER be used in production!
Manual game outcome control violates gaming regulations and fair play standards.

This action will be logged for audit purposes.

Continue with demo action?
    `);

    if (confirmed) {
      callback();
    }
  }

  function forceGameOutcome(gameId, outcome) {
    showDemoConfirmation(`Force ${outcome} for Game ${gameId}`, () => {
      const amount = outcome === 'win' ? '₹1,500' : '₹0';
      logManualIntervention('Game Override', `Forced ${outcome} for Game ${gameId}`, amount);
      showNotification(`⚠️ DEMO: Forced ${outcome} for Game ${gameId}`, 'warning');

      // Update game display to show intervention
      const gameElement = document.querySelector(`[data-game-id="${gameId}"]`);
      if (gameElement) {
        gameElement.style.border = '3px solid #dc3545';
        gameElement.style.background = 'rgba(220, 53, 69, 0.1)';

        // Add intervention badge
        const badge = document.createElement('div');
        badge.innerHTML = '⚠️ DEMO INTERVENTION';
        badge.style.cssText = `
          position: absolute;
          top: -10px;
          right: -10px;
          background: #dc3545;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          font-size: 0.7rem;
          font-weight: 700;
          z-index: 10;
        `;
        gameElement.style.position = 'relative';
        gameElement.appendChild(badge);
      }
    });
  }

  // Add global demo reminder
  function addGlobalDemoReminder() {
    // Add demo watermark
    const watermark = document.createElement('div');
    watermark.innerHTML = '⚠️ DEMO MODE ⚠️';
    watermark.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 4rem;
      color: rgba(220, 53, 69, 0.1);
      font-weight: 900;
      z-index: -1;
      pointer-events: none;
      user-select: none;
    `;
    document.body.appendChild(watermark);

    // Periodic console warnings
    setInterval(() => {
      console.warn(`
⚠️⚠️⚠️ DEMO MODE WARNING ⚠️⚠️⚠️
Manual game controls are active for DEMONSTRATION ONLY!
These features should NEVER be used in production!
Manual outcome control violates gaming regulations!
⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️
      `);
    }, 60000); // Every minute
  }

  // Initialize demo reminder on page load
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(addGlobalDemoReminder, 3000);
  });

  function initializeRealTimeUpdates() {
    // Update real-time metrics every 5 seconds
    setInterval(updateRealTimeMetrics, 5000);
    
    // Update city progress bars
    animateProgressBars();
  }
  
  function updateRealTimeMetrics() {
    const metrics = [
      { id: 'activeOrders', min: 1200, max: 1300, current: 1234 },
      { id: 'onlineRiders', min: 800, max: 900, current: 856 },
      { id: 'avgDeliveryTime', min: 25, max: 35, current: 28, suffix: ' min' },
      { id: 'customerSatisfaction', min: 4.5, max: 5.0, current: 4.8, suffix: '★', decimals: 1 }
    ];
    
    metrics.forEach(metric => {
      const element = document.getElementById(metric.id);
      const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
      let newValue = metric.current + (metric.current * variation);
      
      // Keep within bounds
      newValue = Math.max(metric.min, Math.min(metric.max, newValue));
      
      if (metric.decimals) {
        newValue = newValue.toFixed(metric.decimals);
      } else {
        newValue = Math.round(newValue);
      }
      
      element.textContent = newValue + (metric.suffix || '');
    });
  }
  
  function animateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-fill');

    progressBars.forEach((bar, index) => {
      setTimeout(() => {
        const width = bar.style.width;
        bar.style.width = '0%';
        bar.style.transition = 'width 1.5s ease';

        setTimeout(() => {
          bar.style.width = width;
        }, 100);
      }, index * 200);
    });
  }

  function initializeGPSTracking() {
    // Refresh map button
    const refreshMapBtn = document.getElementById('refreshMap');
    if (refreshMapBtn) {
      refreshMapBtn.addEventListener('click', function() {
        updateRiderPositions();

        // Add refresh animation
        const icon = this.querySelector('.material-icons');
        icon.style.animation = 'spin 1s linear';
        setTimeout(() => {
          icon.style.animation = '';
        }, 1000);
      });
    }

    // Initialize rider markers with tooltips
    const riderMarkers = document.querySelectorAll('.rider-marker');
    riderMarkers.forEach(marker => {
      marker.addEventListener('click', function() {
        const riderId = this.getAttribute('data-rider');
        showRiderDetails(riderId);
      });
    });

    // Auto-update rider positions every 10 seconds
    setInterval(updateRiderPositions, 10000);

    // Update legend counts
    updateRiderCounts();
  }

  function updateRiderPositions() {
    const markers = document.querySelectorAll('.rider-marker');

    markers.forEach(marker => {
      // Simulate movement by slightly changing position
      const currentTop = parseFloat(marker.style.top);
      const currentLeft = parseFloat(marker.style.left);

      const newTop = Math.max(10, Math.min(80, currentTop + (Math.random() - 0.5) * 10));
      const newLeft = Math.max(10, Math.min(80, currentLeft + (Math.random() - 0.5) * 10));

      marker.style.top = newTop + '%';
      marker.style.left = newLeft + '%';

      // Add movement animation
      marker.style.transition = 'all 2s ease';
      setTimeout(() => {
        marker.style.transition = '';
      }, 2000);
    });

    console.log('Rider positions updated');
  }

  function updateRiderCounts() {
    // Simulate real-time count updates
    const legendItems = document.querySelectorAll('.map-legend .legend-item');
    const counts = [
      Math.floor(Math.random() * 50) + 800, // Available
      Math.floor(Math.random() * 50) + 200, // Busy
      Math.floor(Math.random() * 20) + 30   // Offline
    ];

    legendItems.forEach((item, index) => {
      const text = item.textContent.split('(')[0];
      item.innerHTML = `<span class="legend-dot ${['active', 'busy', 'offline'][index]}"></span>${text}(${counts[index]})`;
    });
  }

  function showRiderDetails(riderId) {
    const riderData = {
      'R001': { name: 'Rajesh Kumar', phone: '+91 98765 43210', status: 'Delivering', eta: '12 min', orders: 5 },
      'R002': { name: 'Amit Singh', phone: '+91 87654 32109', status: 'Available', location: 'MG Road', orders: 3 },
      'R003': { name: 'Priya Sharma', phone: '+91 76543 21098', status: 'Picking Up', restaurant: 'Pizza Palace', orders: 7 },
      'R004': { name: 'Vikram Patel', phone: '+91 65432 10987', status: 'Available', location: 'Brigade Road', orders: 4 }
    };

    const rider = riderData[riderId];
    if (!rider) return;

    const modal = document.createElement('div');
    modal.className = 'rider-details-modal';
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000;
    `;

    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 400px; width: 90%;">
        <div style="text-align: center; margin-bottom: 1.5rem;">
          <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: #28a745; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <span class="material-icons" style="color: white; font-size: 2.5rem;">delivery_dining</span>
          </div>
          <h3 style="color: #2c2c54; margin: 0;">${rider.name}</h3>
          <p style="color: #6c757d; margin: 0.5rem 0;">Rider ID: ${riderId}</p>
        </div>

        <div style="margin-bottom: 1.5rem;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">Phone:</span>
            <span style="color: #2c2c54; font-weight: 600;">${rider.phone}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">Status:</span>
            <span style="color: #28a745; font-weight: 600;">${rider.status}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">Total Orders:</span>
            <span style="color: #2c2c54; font-weight: 600;">${rider.orders}</span>
          </div>
          ${rider.eta ? `<div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">ETA:</span>
            <span style="color: #ff9933; font-weight: 600;">${rider.eta}</span>
          </div>` : ''}
          ${rider.location ? `<div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">Location:</span>
            <span style="color: #2c2c54; font-weight: 600;">${rider.location}</span>
          </div>` : ''}
          ${rider.restaurant ? `<div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #6c757d;">Restaurant:</span>
            <span style="color: #2c2c54; font-weight: 600;">${rider.restaurant}</span>
          </div>` : ''}
        </div>

        <div style="display: flex; gap: 1rem;">
          <button onclick="this.closest('.rider-details-modal').remove()" style="flex: 1; background: #6c757d; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Close
          </button>
          <button onclick="alert('Calling ${rider.name}...')" style="flex: 1; background: #28a745; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Call Rider
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  function initializeBookingReports() {
    // Booking filter functionality
    const bookingFilter = document.getElementById('bookingFilter');
    if (bookingFilter) {
      bookingFilter.addEventListener('change', function() {
        filterBookingTable(this.value);
      });
    }

    // Export button functionality
    const exportBtn = document.querySelector('.btn-export');
    if (exportBtn) {
      exportBtn.addEventListener('click', function() {
        exportBookingData();
      });
    }

    // Initialize booking table interactions
    initializeBookingTable();

    // Auto-update booking stats every 30 seconds
    setInterval(updateBookingStats, 30000);

    // Animate booking stats on load
    animateBookingStats();
  }

  function filterBookingTable(status) {
    const tableRows = document.querySelectorAll('#bookingTableBody tr');

    tableRows.forEach(row => {
      const statusBadge = row.querySelector('.status-badge');
      const rowStatus = statusBadge.textContent.toLowerCase();

      if (status === 'all' || rowStatus === status) {
        row.style.display = '';
        row.style.animation = 'fadeIn 0.3s ease';
      } else {
        row.style.display = 'none';
      }
    });

    console.log(`Filtered bookings by: ${status}`);
  }

  function exportBookingData() {
    // Simulate export functionality
    const exportModal = document.createElement('div');
    exportModal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000;
    `;

    exportModal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 400px; width: 90%; text-align: center;">
        <div style="width: 60px; height: 60px; margin: 0 auto 1rem; background: #28a745; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <span class="material-icons" style="color: white; font-size: 2rem;">download</span>
        </div>
        <h3 style="color: #2c2c54; margin-bottom: 1rem;">Export Booking Data</h3>
        <p style="color: #6c757d; margin-bottom: 2rem;">Your booking report is being generated. You will receive an email with the download link shortly.</p>
        <div style="display: flex; gap: 1rem;">
          <button onclick="this.closest('div').remove()" style="flex: 1; background: #6c757d; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Close
          </button>
          <button onclick="alert('Report sent to ${sessionStorage.getItem('adminEmail')}')" style="flex: 1; background: #28a745; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Send Email
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(exportModal);
  }

  function initializeBookingTable() {
    // Add click handlers for action buttons
    const actionButtons = document.querySelectorAll('.action-btn-small');

    actionButtons.forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.stopPropagation();

        const action = this.classList.contains('view') ? 'view' : 'edit';
        const row = this.closest('tr');
        const bookingId = row.cells[0].textContent;

        if (action === 'view') {
          showBookingDetails(bookingId, row);
        } else {
          editBooking(bookingId, row);
        }
      });
    });

    // Add row click handlers
    const tableRows = document.querySelectorAll('#bookingTableBody tr');
    tableRows.forEach(row => {
      row.addEventListener('click', function() {
        const bookingId = this.cells[0].textContent;
        showBookingDetails(bookingId, this);
      });

      row.style.cursor = 'pointer';
    });
  }

  function showBookingDetails(bookingId, row) {
    const cells = row.cells;
    const customer = cells[1].textContent;
    const service = cells[2].textContent;
    const datetime = cells[3].textContent;
    const amount = cells[4].textContent;
    const status = cells[5].textContent.trim();

    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000;
    `;

    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 500px; width: 90%;">
        <div style="text-align: center; margin-bottom: 1.5rem;">
          <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <span class="material-icons" style="color: white; font-size: 2.5rem;">receipt_long</span>
          </div>
          <h3 style="color: #2c2c54; margin: 0;">Booking Details</h3>
          <p style="color: #6c757d; margin: 0.5rem 0;">${bookingId}</p>
        </div>

        <div style="margin-bottom: 1.5rem;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; padding: 0.5rem; background: #f8f9fa; border-radius: 8px;">
            <span style="color: #6c757d; font-weight: 600;">Customer:</span>
            <span style="color: #2c2c54; font-weight: 600;">${customer}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; padding: 0.5rem; background: #f8f9fa; border-radius: 8px;">
            <span style="color: #6c757d; font-weight: 600;">Service/Product:</span>
            <span style="color: #2c2c54; font-weight: 600;">${service}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; padding: 0.5rem; background: #f8f9fa; border-radius: 8px;">
            <span style="color: #6c757d; font-weight: 600;">Date & Time:</span>
            <span style="color: #2c2c54; font-weight: 600;">${datetime}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; padding: 0.5rem; background: #f8f9fa; border-radius: 8px;">
            <span style="color: #6c757d; font-weight: 600;">Amount:</span>
            <span style="color: #28a745; font-weight: 700; font-size: 1.1rem;">${amount}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 0.8rem; padding: 0.5rem; background: #f8f9fa; border-radius: 8px;">
            <span style="color: #6c757d; font-weight: 600;">Status:</span>
            <span class="status-badge ${status.toLowerCase()}">${status}</span>
          </div>
        </div>

        <div style="display: flex; gap: 1rem;">
          <button onclick="this.closest('div').remove()" style="flex: 1; background: #6c757d; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Close
          </button>
          <button onclick="editBooking('${bookingId}'); this.closest('div').remove();" style="flex: 1; background: #667eea; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Edit Booking
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  function editBooking(bookingId) {
    alert(`Edit functionality for ${bookingId} will be implemented in the next update.`);
  }

  function updateBookingStats() {
    // Simulate real-time booking stats updates
    const statCards = document.querySelectorAll('.booking-stat-card');

    const updates = [
      { selector: '.confirmed h4', value: Math.floor(Math.random() * 100) + 5600 },
      { selector: '.pending h4', value: Math.floor(Math.random() * 200) + 1200 },
      { selector: '.cancelled h4', value: Math.floor(Math.random() * 50) + 450 },
      { selector: '.total h4', value: Math.floor(Math.random() * 300) + 7300 }
    ];

    updates.forEach(update => {
      const element = document.querySelector(`.booking-stat-card${update.selector}`);
      if (element) {
        element.textContent = update.value.toLocaleString();
      }
    });

    console.log('Booking stats updated');
  }

  function animateBookingStats() {
    const statCards = document.querySelectorAll('.booking-stat-card');

    statCards.forEach((card, index) => {
      setTimeout(() => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';

        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, 100);
      }, index * 150);
    });
  }

  function initializeProfileManagement() {
    // Initialize all profile management features
    initializeRiderManagement();
    initializeSellerManagement();
    initializeServiceProviderManagement();
    initializeVerificationSystem();
  }

  function initializeRiderManagement() {
    // Rider status filter
    const riderStatusFilter = document.getElementById('riderStatusFilter');
    if (riderStatusFilter) {
      riderStatusFilter.addEventListener('change', function() {
        filterProfiles('riders', this.value);
      });
    }

    // Add global rider profile view function
    window.viewRiderProfile = function(riderId) {
      showProfileModal('rider', riderId);
    };
  }

  function initializeSellerManagement() {
    // Seller filters
    const sellerStatusFilter = document.getElementById('sellerStatusFilter');
    const sellerCategoryFilter = document.getElementById('sellerCategoryFilter');

    if (sellerStatusFilter) {
      sellerStatusFilter.addEventListener('change', function() {
        filterProfiles('sellers', this.value, sellerCategoryFilter.value);
      });
    }

    if (sellerCategoryFilter) {
      sellerCategoryFilter.addEventListener('change', function() {
        filterProfiles('sellers', sellerStatusFilter.value, this.value);
      });
    }

    // Add global seller profile view function
    window.viewSellerProfile = function(sellerId) {
      showProfileModal('seller', sellerId);
    };
  }

  function initializeServiceProviderManagement() {
    // Service provider filters
    const providerStatusFilter = document.getElementById('providerStatusFilter');
    const providerCategoryFilter = document.getElementById('providerCategoryFilter');

    if (providerStatusFilter) {
      providerStatusFilter.addEventListener('change', function() {
        filterProfiles('service-providers', this.value, providerCategoryFilter.value);
      });
    }

    if (providerCategoryFilter) {
      providerCategoryFilter.addEventListener('change', function() {
        filterProfiles('service-providers', providerStatusFilter.value, this.value);
      });
    }

    // Add global service provider profile view function
    window.viewProviderProfile = function(providerId) {
      showProfileModal('service-provider', providerId);
    };
  }

  function initializeVerificationSystem() {
    // Verification filters
    const verificationTypeFilter = document.getElementById('verificationTypeFilter');
    const verificationStatusFilter = document.getElementById('verificationStatusFilter');

    if (verificationTypeFilter) {
      verificationTypeFilter.addEventListener('change', function() {
        filterVerifications(this.value, verificationStatusFilter.value);
      });
    }

    if (verificationStatusFilter) {
      verificationStatusFilter.addEventListener('change', function() {
        filterVerifications(verificationTypeFilter.value, this.value);
      });
    }

    // Add global document review function
    window.reviewDocument = function(verificationId) {
      showDocumentReviewModal(verificationId);
    };
  }

  function filterProfiles(section, status, category = 'all') {
    const tableRows = document.querySelectorAll(`#${section}-section .profile-table tbody tr`);

    tableRows.forEach(row => {
      const statusBadge = row.querySelector('.status-badge');
      const categoryBadge = row.querySelector('.category-badge');

      const rowStatus = statusBadge ? statusBadge.textContent.toLowerCase() : '';
      const rowCategory = categoryBadge ? categoryBadge.className.split(' ')[1] : '';

      const statusMatch = status === 'all' || rowStatus.includes(status);
      const categoryMatch = category === 'all' || rowCategory === category;

      if (statusMatch && categoryMatch) {
        row.style.display = '';
        row.style.animation = 'fadeIn 0.3s ease';
      } else {
        row.style.display = 'none';
      }
    });

    console.log(`Filtered ${section} by status: ${status}, category: ${category}`);
  }

  function filterVerifications(type, status) {
    const tableRows = document.querySelectorAll('.verification-table tbody tr');

    tableRows.forEach(row => {
      const documentType = row.querySelector('.document-type strong').textContent.toLowerCase();
      const statusBadge = row.querySelector('.status-badge').textContent.toLowerCase();

      const typeMatch = type === 'all' || documentType.includes(type.replace('-', ' '));
      const statusMatch = status === 'all' || statusBadge.includes(status.replace('pending review', 'pending'));

      if (typeMatch && statusMatch) {
        row.style.display = '';
        row.style.animation = 'fadeIn 0.3s ease';
      } else {
        row.style.display = 'none';
      }
    });

    console.log(`Filtered verifications by type: ${type}, status: ${status}`);
  }

  function showProfileModal(type, profileId) {
    const profileData = getProfileData(type, profileId);
    if (!profileData) return;

    const modal = document.createElement('div');
    modal.className = 'profile-modal';
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000; overflow-y: auto;
    `;

    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <div style="text-align: center; margin-bottom: 2rem;">
          <div style="width: 100px; height: 100px; margin: 0 auto 1rem; border-radius: 50%; overflow: hidden; border: 4px solid #e9ecef;">
            <img src="${profileData.avatar}" alt="${profileData.name}" style="width: 100%; height: 100%; object-fit: cover;">
          </div>
          <h2 style="color: #2c2c54; margin: 0;">${profileData.name}</h2>
          <p style="color: #6c757d; margin: 0.5rem 0;">${profileData.type} - ${profileData.id}</p>
          <span class="status-badge ${profileData.status.toLowerCase()}">${profileData.status}</span>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 2rem;">
          <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
            <h4 style="margin: 0 0 0.5rem 0; color: #2c2c54;">Contact Information</h4>
            <p style="margin: 0.3rem 0;"><strong>Phone:</strong> ${profileData.phone}</p>
            <p style="margin: 0.3rem 0;"><strong>Email:</strong> ${profileData.email}</p>
            ${profileData.address ? `<p style="margin: 0.3rem 0;"><strong>Address:</strong> ${profileData.address}</p>` : ''}
          </div>

          <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
            <h4 style="margin: 0 0 0.5rem 0; color: #2c2c54;">Performance</h4>
            <p style="margin: 0.3rem 0;"><strong>Rating:</strong> ${profileData.rating} ⭐</p>
            <p style="margin: 0.3rem 0;"><strong>Total Orders:</strong> ${profileData.totalOrders || 'N/A'}</p>
            <p style="margin: 0.3rem 0;"><strong>Joined:</strong> ${profileData.joinDate}</p>
          </div>
        </div>

        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">Document Status</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
            ${profileData.documents.map(doc => `
              <span class="doc-item ${doc.status}">${doc.name} ${doc.status === 'verified' ? '✓' : doc.status === 'pending' ? '⏳' : '❌'}</span>
            `).join('')}
          </div>
        </div>

        <div style="display: flex; gap: 1rem; justify-content: center;">
          <button onclick="this.closest('.profile-modal').remove()" style="flex: 1; background: #6c757d; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Close
          </button>
          <button onclick="approveProfile('${profileId}')" style="flex: 1; background: #28a745; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Approve Profile
          </button>
          <button onclick="editProfile('${profileId}')" style="flex: 1; background: #667eea; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Edit Profile
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  function getProfileData(type, profileId) {
    // Sample profile data - in real app, this would come from API
    const profiles = {
      'rider': {
        'R001': {
          id: 'R001', name: 'Rajesh Kumar', type: 'Rider', status: 'Active',
          avatar: '../assets/images/avatars/rider1.jpg', phone: '+91 98765 43210',
          email: '<EMAIL>', rating: '4.8', totalOrders: '1,234',
          joinDate: 'Jan 2024', address: 'MG Road, Bangalore',
          documents: [
            { name: 'Driving License', status: 'verified' },
            { name: 'Aadhar Card', status: 'verified' },
            { name: 'Vehicle RC', status: 'verified' }
          ]
        },
        'R002': {
          id: 'R002', name: 'Amit Singh', type: 'Rider', status: 'Pending',
          avatar: '../assets/images/avatars/rider2.jpg', phone: '+91 87654 32109',
          email: '<EMAIL>', rating: '4.6', totalOrders: '856',
          joinDate: 'Dec 2023', address: 'Brigade Road, Bangalore',
          documents: [
            { name: 'Driving License', status: 'verified' },
            { name: 'Aadhar Card', status: 'pending' },
            { name: 'Vehicle RC', status: 'verified' }
          ]
        }
      },
      'seller': {
        'S001': {
          id: 'S001', name: 'Pizza Palace', type: 'Restaurant Seller', status: 'Active',
          avatar: '../assets/images/avatars/store1.jpg', phone: '+91 98765 11111',
          email: '<EMAIL>', rating: '4.7', totalOrders: '5,678',
          joinDate: 'Nov 2023', address: 'Koramangala, Bangalore',
          documents: [
            { name: 'FSSAI License', status: 'verified' },
            { name: 'GST Certificate', status: 'verified' },
            { name: 'Trade License', status: 'verified' }
          ]
        }
      },
      'service-provider': {
        'SP001': {
          id: 'SP001', name: 'Dr. Priya Sharma', type: 'Healthcare Provider', status: 'Active',
          avatar: '../assets/images/avatars/doctor1.jpg', phone: '+91 98765 55555',
          email: '<EMAIL>', rating: '4.9', totalOrders: '2,345',
          joinDate: 'Oct 2023', address: 'Indiranagar, Bangalore',
          documents: [
            { name: 'MBBS Certificate', status: 'verified' },
            { name: 'Medical License', status: 'verified' },
            { name: 'Experience Certificate', status: 'verified' }
          ]
        }
      }
    };

    return profiles[type] && profiles[type][profileId] ? profiles[type][profileId] : null;
  }

  function showDocumentReviewModal(verificationId) {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000;
    `;

    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <div style="text-align: center; margin-bottom: 2rem;">
          <h2 style="color: #2c2c54; margin: 0;">Document Review - ${verificationId}</h2>
          <p style="color: #6c757d; margin: 0.5rem 0;">Review and verify submitted documents</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
          <div>
            <h4 style="color: #2c2c54; margin-bottom: 1rem;">Document Preview</h4>
            <div style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 2rem; text-align: center; min-height: 300px; display: flex; align-items: center; justify-content: center;">
              <div>
                <span class="material-icons" style="font-size: 4rem; color: #6c757d; margin-bottom: 1rem;">description</span>
                <p style="color: #6c757d; margin: 0;">Document preview would appear here</p>
                <button style="margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                  View Full Document
                </button>
              </div>
            </div>
          </div>

          <div>
            <h4 style="color: #2c2c54; margin-bottom: 1rem;">Verification Checklist</h4>
            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                  <input type="checkbox" checked>
                  <span>Document is clear and readable</span>
                </label>
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                  <input type="checkbox" checked>
                  <span>Information matches profile data</span>
                </label>
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                  <input type="checkbox">
                  <span>Document is not expired</span>
                </label>
              </div>
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                  <input type="checkbox">
                  <span>No signs of tampering</span>
                </label>
              </div>

              <div style="margin-top: 1.5rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #2c2c54;">Comments:</label>
                <textarea style="width: 100%; padding: 0.8rem; border: 1px solid #dee2e6; border-radius: 6px; resize: vertical; min-height: 80px;" placeholder="Add verification comments..."></textarea>
              </div>
            </div>
          </div>
        </div>

        <div style="display: flex; gap: 1rem; justify-content: center;">
          <button onclick="this.closest('div').remove()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 8px; cursor: pointer;">
            Cancel
          </button>
          <button onclick="rejectDocument('${verificationId}'); this.closest('div').remove();" style="background: #dc3545; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 8px; cursor: pointer;">
            Reject Document
          </button>
          <button onclick="approveDocument('${verificationId}'); this.closest('div').remove();" style="background: #28a745; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 8px; cursor: pointer;">
            Approve Document
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // Global functions for profile actions
  window.approveProfile = function(profileId) {
    alert(`Profile ${profileId} approved successfully!`);
    // Update UI to reflect approval
    updateProfileStatus(profileId, 'active');
  };

  window.editProfile = function(profileId) {
    alert(`Edit functionality for profile ${profileId} will be implemented.`);
  };

  window.approveDocument = function(verificationId) {
    alert(`Document ${verificationId} approved successfully!`);
    updateVerificationStatus(verificationId, 'approved');
  };

  window.rejectDocument = function(verificationId) {
    alert(`Document ${verificationId} rejected.`);
    updateVerificationStatus(verificationId, 'rejected');
  };

  // Cancellation history functions
  window.viewCancelDetails = function(orderId) {
    showCancellationDetailsModal(orderId);
  };

  window.exportCancellationData = function() {
    alert('Cancellation data export functionality will be implemented.');
  };

  function showCancellationDetailsModal(orderId) {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
      justify-content: center; z-index: 2000; overflow-y: auto;
    `;

    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <div style="text-align: center; margin-bottom: 2rem;">
          <div style="width: 80px; height: 80px; margin: 0 auto 1rem; border-radius: 50%; background: linear-gradient(135deg, #dc3545, #c82333); display: flex; align-items: center; justify-content: center;">
            <span class="material-icons" style="color: white; font-size: 2.5rem;">cancel</span>
          </div>
          <h2 style="color: #2c2c54; margin: 0;">Cancellation Details</h2>
          <p style="color: #6c757d; margin: 0.5rem 0;">Order ${orderId}</p>
        </div>

        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem;">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">Order Information</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
              <p style="margin: 0.5rem 0;"><strong>Customer:</strong> Rajesh Kumar</p>
              <p style="margin: 0.5rem 0;"><strong>Restaurant:</strong> Pizza Palace</p>
              <p style="margin: 0.5rem 0;"><strong>Order Value:</strong> ₹850</p>
            </div>
            <div>
              <p style="margin: 0.5rem 0;"><strong>Order Time:</strong> 10:30 AM</p>
              <p style="margin: 0.5rem 0;"><strong>Cancel Time:</strong> 10:45 AM</p>
              <p style="margin: 0.5rem 0;"><strong>Duration:</strong> 15 minutes</p>
            </div>
          </div>
        </div>

        <div style="background: #fff3cd; padding: 1.5rem; border-radius: 12px; border-left: 4px solid #ffc107; margin-bottom: 2rem;">
          <h4 style="margin: 0 0 1rem 0; color: #856404;">Cancellation Details</h4>
          <p style="margin: 0.5rem 0;"><strong>Reason:</strong> Customer changed mind</p>
          <p style="margin: 0.5rem 0;"><strong>Cancelled By:</strong> Customer</p>
          <p style="margin: 0.5rem 0;"><strong>Refund Status:</strong> <span style="color: #28a745; font-weight: 600;">Completed</span></p>
          <p style="margin: 0.5rem 0;"><strong>Refund Amount:</strong> ₹850</p>
        </div>

        <div style="background: #d1ecf1; padding: 1.5rem; border-radius: 12px; border-left: 4px solid #17a2b8; margin-bottom: 2rem;">
          <h4 style="margin: 0 0 1rem 0; color: #0c5460;">Timeline</h4>
          <div style="font-size: 0.9rem;">
            <p style="margin: 0.3rem 0;">🕐 10:30 AM - Order placed</p>
            <p style="margin: 0.3rem 0;">🕐 10:32 AM - Payment confirmed</p>
            <p style="margin: 0.3rem 0;">🕐 10:35 AM - Restaurant accepted</p>
            <p style="margin: 0.3rem 0;">❌ 10:45 AM - Customer cancelled order</p>
            <p style="margin: 0.3rem 0;">💰 10:46 AM - Refund initiated</p>
            <p style="margin: 0.3rem 0;">✅ 10:50 AM - Refund completed</p>
          </div>
        </div>

        <div style="display: flex; gap: 1rem; justify-content: center;">
          <button onclick="this.closest('div').remove()" style="flex: 1; background: #6c757d; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Close
          </button>
          <button onclick="contactCustomer('${orderId}')" style="flex: 1; background: #667eea; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Contact Customer
          </button>
          <button onclick="downloadCancelReport('${orderId}')" style="flex: 1; background: #28a745; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer;">
            Download Report
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  window.contactCustomer = function(orderId) {
    alert(`Contacting customer for order ${orderId}...`);
  };

  window.downloadCancelReport = function(orderId) {
    alert(`Downloading cancellation report for order ${orderId}...`);
  };

  function updateProfileStatus(profileId, newStatus) {
    // Find and update the profile status in the table
    const profileRows = document.querySelectorAll('.profile-table tbody tr');
    profileRows.forEach(row => {
      const idCell = row.cells[0];
      if (idCell && idCell.textContent === profileId) {
        const statusBadge = row.querySelector('.status-badge');
        if (statusBadge) {
          statusBadge.className = `status-badge ${newStatus}`;
          statusBadge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
        }
      }
    });
  }

  function updateVerificationStatus(verificationId, newStatus) {
    // Find and update the verification status in the table
    const verificationRows = document.querySelectorAll('.verification-table tbody tr');
    verificationRows.forEach(row => {
      const idCell = row.cells[0];
      if (idCell && idCell.textContent === verificationId) {
        const statusBadge = row.querySelector('.status-badge');
        if (statusBadge) {
          statusBadge.className = `status-badge ${newStatus}`;
          statusBadge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
        }
      }
    });
  }

  function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item a');
    const sections = document.querySelectorAll('.content-section');
    const pageTitle = document.getElementById('pageTitle');

    console.log('Initializing navigation...');
    console.log('Found nav items:', navItems.length);
    console.log('Found sections:', sections.length);

    // Add click event listeners to navigation items
    navItems.forEach((item, index) => {
      console.log(`Nav item ${index}:`, item.getAttribute('data-section'));

      item.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const targetSection = this.getAttribute('data-section');

        console.log('Navigation clicked:', targetSection);

        // Force navigation
        navigateToSection(targetSection);
      });
    });
  }

  // Centralized navigation function
  function navigateToSection(sectionName) {
    console.log('Navigating to section:', sectionName);

    // Define titles
    const titles = {
      'dashboard': 'Dashboard Overview',
      'orders': 'Orders Management',
      'users': 'Users Management',
      'riders': 'Riders Management',
      'sellers': 'Sellers Management',
      'service-providers': 'Service Providers Management',
      'verification': 'KYC & Verification',
      'restaurants': 'Restaurants Management',
      'analytics': 'Advanced Analytics',
      'payments': 'Payments Management',
      'settings': 'System Settings'
    };

    // Update active nav item
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));

    const targetNavItem = document.querySelector(`a[data-section="${sectionName}"]`);
    if (targetNavItem) {
      targetNavItem.parentElement.classList.add('active');
    }

    // Hide all sections
    const allSections = document.querySelectorAll('.content-section');
    allSections.forEach(section => {
      section.classList.remove('active');
      section.style.display = 'none';
    });

    // Show target section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
      targetSection.classList.add('active');
      targetSection.style.display = 'block';

      // Initialize graphs for the section
      initializeSectionGraphs(sectionName);

      // Update page title
      const pageTitle = document.getElementById('pageTitle');
      if (pageTitle) {
        pageTitle.textContent = titles[sectionName] || 'Dashboard';
      }

      // Show success notification
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 3001;
        background: #28a745; color: white; padding: 0.8rem 1.2rem;
        border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-family: 'Poppins', sans-serif; font-weight: 600; font-size: 0.9rem;
      `;
      notification.innerHTML = `✅ Navigated to ${titles[sectionName]}`;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);

      console.log('Successfully navigated to:', sectionName);
    } else {
      console.error('Section not found:', sectionName + '-section');
    }

    // Close mobile sidebar
    if (window.innerWidth <= 768) {
      const sidebar = document.querySelector('.admin-sidebar');
      if (sidebar) {
        sidebar.classList.remove('open');
      }
    }
  }

  // Initialize graphs for each section
  function initializeSectionGraphs(sectionName) {
    console.log('Initializing graphs for section:', sectionName);

    // Wait a bit for the section to be visible
    setTimeout(() => {
      switch(sectionName) {
        case 'riders':
          initializeRidersGraphs();
          break;
        case 'sellers':
          initializeSellersGraphs();
          break;
        case 'service-providers':
          initializeServiceProvidersGraphs();
          break;
        case 'verification':
          initializeVerificationGraphs();
          break;
        case 'orders':
          initializeOrdersGraphs();
          break;
        case 'users':
          initializeUsersGraphs();
          break;
        default:
          console.log('No specific graphs for section:', sectionName);
      }
    }, 100);
  }

  function initializeRidersGraphs() {
    // Create riders performance chart
    const ridersChartContainer = document.querySelector('#riders-section .profile-stats-grid');
    if (ridersChartContainer && !document.getElementById('ridersChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 Riders Performance Analytics</h4>
          <canvas id="ridersChart" width="400" height="200"></canvas>
        </div>
      `;
      ridersChartContainer.parentNode.insertBefore(chartDiv, ridersChartContainer.nextSibling);

      // Create simple chart
      createSimpleChart('ridersChart', 'Riders Performance', [
        { label: 'Active', value: 1856, color: '#28a745' },
        { label: 'Pending', value: 234, color: '#ffc107' },
        { label: 'Suspended', value: 66, color: '#dc3545' }
      ]);
    }
  }

  function initializeSellersGraphs() {
    const sellersChartContainer = document.querySelector('#sellers-section .profile-stats-grid');
    if (sellersChartContainer && !document.getElementById('sellersChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 Sellers Category Distribution</h4>
          <canvas id="sellersChart" width="400" height="200"></canvas>
        </div>
      `;
      sellersChartContainer.parentNode.insertBefore(chartDiv, sellersChartContainer.nextSibling);

      createSimpleChart('sellersChart', 'Sellers by Category', [
        { label: 'Restaurants', value: 567, color: '#ff9933' },
        { label: 'Grocery', value: 345, color: '#138808' },
        { label: 'Pharmacy', value: 234, color: '#dc3545' },
        { label: 'Electronics', value: 99, color: '#667eea' }
      ]);
    }
  }

  function initializeServiceProvidersGraphs() {
    const providersChartContainer = document.querySelector('#service-providers-section .profile-stats-grid');
    if (providersChartContainer && !document.getElementById('providersChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 Service Providers by Category</h4>
          <canvas id="providersChart" width="400" height="200"></canvas>
        </div>
      `;
      providersChartContainer.parentNode.insertBefore(chartDiv, providersChartContainer.nextSibling);

      createSimpleChart('providersChart', 'Service Providers', [
        { label: 'Healthcare', value: 1234, color: '#dc3545' },
        { label: 'Education', value: 987, color: '#667eea' },
        { label: 'Home Services', value: 765, color: '#ffc107' },
        { label: 'Beauty & Wellness', value: 345, color: '#e83e8c' },
        { label: 'Automotive', value: 125, color: '#6c757d' }
      ]);
    }
  }

  function initializeVerificationGraphs() {
    const verificationChartContainer = document.querySelector('#verification-section .profile-stats-grid');
    if (verificationChartContainer && !document.getElementById('verificationChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 Document Verification Status</h4>
          <canvas id="verificationChart" width="400" height="200"></canvas>
        </div>
      `;
      verificationChartContainer.parentNode.insertBefore(chartDiv, verificationChartContainer.nextSibling);

      createSimpleChart('verificationChart', 'Document Status', [
        { label: 'Verified', value: 5234, color: '#28a745' },
        { label: 'Pending', value: 89, color: '#ffc107' },
        { label: 'Rejected', value: 156, color: '#dc3545' }
      ]);
    }
  }

  function initializeOrdersGraphs() {
    const ordersChartContainer = document.querySelector('#orders-section .profile-stats-grid');
    if (ordersChartContainer && !document.getElementById('ordersChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 Orders Status Distribution</h4>
          <canvas id="ordersChart" width="400" height="200"></canvas>
        </div>
      `;
      ordersChartContainer.parentNode.insertBefore(chartDiv, ordersChartContainer.nextSibling);

      createSimpleChart('ordersChart', 'Orders Status', [
        { label: 'Completed', value: 5678, color: '#28a745' },
        { label: 'Pending', value: 1234, color: '#ffc107' },
        { label: 'Cancelled', value: 456, color: '#dc3545' }
      ]);
    }

    // Initialize cancellation reasons chart
    const cancelChartContainer = document.querySelector('#orders-section .cancellation-chart-container');
    if (cancelChartContainer && !document.getElementById('cancellationReasonsChart')) {
      setTimeout(() => {
        createCancellationReasonsChart();
      }, 200);
    }
  }

  function createCancellationReasonsChart() {
    const canvas = document.getElementById('cancellationReasonsChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Cancellation reasons data
    const data = [
      { label: 'Customer Changed Mind', value: 278, color: '#fd7e14' },
      { label: 'Restaurant Unavailable', value: 123, color: '#6f42c1' },
      { label: 'Payment Failed', value: 35, color: '#dc3545' },
      { label: 'No Rider Available', value: 20, color: '#6c757d' }
    ];

    // Calculate total for percentages
    const total = data.reduce((sum, item) => sum + item.value, 0);

    // Draw pie chart
    let currentAngle = 0;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 4;

    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI;

      // Draw slice
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fillStyle = item.color;
      ctx.fill();

      // Draw label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius + 40);
      const labelY = centerY + Math.sin(labelAngle) * (radius + 40);

      ctx.fillStyle = '#2c2c54';
      ctx.font = '11px Poppins';
      ctx.textAlign = 'center';
      ctx.fillText(`${item.label}`, labelX, labelY);
      ctx.fillText(`${item.value} (${((item.value/total)*100).toFixed(1)}%)`, labelX, labelY + 12);

      currentAngle += sliceAngle;
    });
  }

  function initializeUsersGraphs() {
    const usersChartContainer = document.querySelector('#users-section .profile-stats-grid');
    if (usersChartContainer && !document.getElementById('usersChart')) {
      const chartDiv = document.createElement('div');
      chartDiv.innerHTML = `
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);">
          <h4 style="margin: 0 0 1rem 0; color: #2c2c54;">📊 User Activity Analytics</h4>
          <canvas id="usersChart" width="400" height="200"></canvas>
        </div>
      `;
      usersChartContainer.parentNode.insertBefore(chartDiv, usersChartContainer.nextSibling);

      createSimpleChart('usersChart', 'User Status', [
        { label: 'Active Users', value: 45234, color: '#28a745' },
        { label: 'New This Month', value: 2456, color: '#667eea' },
        { label: 'Blocked Users', value: 234, color: '#dc3545' }
      ]);
    }
  }

  // Simple chart creation function
  function createSimpleChart(canvasId, title, data) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Calculate total for percentages
    const total = data.reduce((sum, item) => sum + item.value, 0);

    // Draw pie chart
    let currentAngle = 0;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;

    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI;

      // Draw slice
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fillStyle = item.color;
      ctx.fill();

      // Draw label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius + 30);
      const labelY = centerY + Math.sin(labelAngle) * (radius + 30);

      ctx.fillStyle = '#2c2c54';
      ctx.font = '12px Poppins';
      ctx.textAlign = 'center';
      ctx.fillText(`${item.label}: ${item.value}`, labelX, labelY);

      currentAngle += sliceAngle;
    });
  }

  function initializeQuickActions() {
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(card => {
      card.addEventListener('click', function() {
        const action = this.querySelector('span:last-child').textContent;
        
        // Add click animation
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);
        
        // Show action modal
        showActionModal(action);
      });
    });
  }
  
  function showActionModal(action) {
    const modal = document.createElement('div');
    modal.className = 'action-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
    `;
    
    modal.innerHTML = `
      <div style="background: white; border-radius: 16px; padding: 2rem; max-width: 400px; width: 90%; text-align: center;">
        <div style="width: 60px; height: 60px; margin: 0 auto 1rem; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <span class="material-icons" style="color: white; font-size: 2rem;">settings</span>
        </div>
        <h3 style="color: #2c2c54; margin-bottom: 1rem;">${action}</h3>
        <p style="color: #6c757d; margin-bottom: 2rem;">This feature is coming soon! The ${action.toLowerCase()} functionality will be available in the next update.</p>
        <button onclick="this.closest('.action-modal').remove()" style="background: #667eea; color: white; border: none; padding: 0.8rem 2rem; border-radius: 8px; cursor: pointer; font-family: 'Poppins', sans-serif;">
          Got it
        </button>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-close after 5 seconds
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 5000);
  }
  
  // Notification and message handlers
  document.getElementById('notificationsBtn').addEventListener('click', function() {
    showNotificationsPanel();
  });
  
  document.getElementById('messagesBtn').addEventListener('click', function() {
    showMessagesPanel();
  });
  
  function showNotificationsPanel() {
    // Implementation for notifications panel
    console.log('Notifications panel opened');
  }
  
  function showMessagesPanel() {
    // Implementation for messages panel
    console.log('Messages panel opened');
  }
  
  // Keyboard shortcuts
  document.addEventListener('keydown', function(e) {
    // Ctrl + D for Dashboard
    if (e.ctrlKey && e.key === 'd') {
      e.preventDefault();
      document.querySelector('[data-section="dashboard"]').click();
    }
    
    // Ctrl + O for Orders
    if (e.ctrlKey && e.key === 'o') {
      e.preventDefault();
      document.querySelector('[data-section="orders"]').click();
    }
    
    // Ctrl + U for Users
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault();
      document.querySelector('[data-section="users"]').click();
    }
  });
  
  console.log('Admin dashboard initialized successfully!');
  console.log('Welcome, ' + sessionStorage.getItem('adminEmail'));
});
